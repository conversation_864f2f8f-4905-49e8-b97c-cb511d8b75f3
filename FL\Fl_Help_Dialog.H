//
// Fl_Help_Dialog dialog for the Fast Light Tool Kit (FLTK).
//
// Copyright 1998-2021 by <PERSON> and others.
//
// This library is free software. Distribution and use rights are outlined in
// the file "COPYING" which should have been included with this file.  If this
// file is missing or damaged, see the license at:
//
//     https://www.fltk.org/COPYING.php
//
// Please see the following page on how to report bugs and issues:
//
//     https://www.fltk.org/bugs.php
//
// ========================================================================
//  DO NOT EDIT FL/Fl_Help_Dialog.H and src/Fl_Help_Dialog.cxx !!!
// ========================================================================
//  Please use fluid to change src/Fl_Help_Dialog.fl interactively
//  and then use fluid to "write code" or edit and use fluid -c .
// ========================================================================
//

// generated by Fast Light User Interface Designer (fluid) version 1.0404

#ifndef Fl_Help_Dialog_H
#define Fl_Help_Dialog_H
#include <FL/Fl.H>
#include <FL/Fl_Double_Window.H>
#include <FL/Fl_Group.H>
#include <FL/Fl_Button.H>
#include <FL/Fl_Input.H>
#include <FL/Fl_Box.H>
#include <FL/Fl_Help_View.H>

class FL_EXPORT Fl_Help_Dialog {
  int index_;
  int max_;
  int line_[100]; // FIXME: we must remove those static numbers
  char file_[100][FL_PATH_MAX]; // FIXME: we must remove those static numbers
  int find_pos_;
public:
  Fl_Help_Dialog();
private:
  Fl_Double_Window *window_;
  Fl_Button *back_;
  inline void cb_back__i(Fl_Button*, void*);
  static void cb_back_(Fl_Button*, void*);
  Fl_Button *forward_;
  inline void cb_forward__i(Fl_Button*, void*);
  static void cb_forward_(Fl_Button*, void*);
  Fl_Button *smaller_;
  inline void cb_smaller__i(Fl_Button*, void*);
  static void cb_smaller_(Fl_Button*, void*);
  Fl_Button *larger_;
  inline void cb_larger__i(Fl_Button*, void*);
  static void cb_larger_(Fl_Button*, void*);
  Fl_Input *find_;
  inline void cb_find__i(Fl_Input*, void*);
  static void cb_find_(Fl_Input*, void*);
  Fl_Help_View *view_;
  inline void cb_view__i(Fl_Help_View*, void*);
  static void cb_view_(Fl_Help_View*, void*);
public:
  ~Fl_Help_Dialog();
  int h();
  void hide();
  int load(const char *f);
  void position(int xx, int yy);
  void resize(int xx, int yy, int ww, int hh);
  void show();
  void show(int argc, char **argv);
  void textsize(Fl_Fontsize s);
  Fl_Fontsize textsize();
  void topline(const char *n);
  void topline(int n);
  void value(const char *f);
  const char * value() const;
  int visible();
  int w();
  int x();
  int y();
};
#endif
