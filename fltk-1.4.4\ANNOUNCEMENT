FLTK 1.4 is based on the final release of FLTK 1.3.4. Later updates
have been backported to 1.3.5 - 1.3.11 and branch-1.3 (Git).

FLTK 1.4 is now in maintenance mode, i.e. it receives only bug fixes
and important enhancements, e.g. for Linux releases or macOS support.
The current branch on GitHub [2] is `branch-1.4` whereas the `master`
branch moved on to development of FLTK 1.5.

FLTK 1.4 is intended to be mostly API compatible with FLTK 1.3.x so
you don't need to change source code when you switch to FLTK 1.4.
However, all programs must be recompiled with FLTK 1.4 because the
ABI (Application Binary Interface) has changed.

Potential source code conflicts are documented in chapter "Migrating
Code from FLTK 1.3 to 1.4" of the user documentation [1].

FLTK 1.4 adds some new widgets (e.g. Fl_Flex, Fl_Grid) for flexible GUI
layout, Fl_Scheme_Choice for scheme selection by users, and more.
Other widgets (<PERSON>l_<PERSON>bs, <PERSON>l_Tile, <PERSON>l_Spinner etc.) have been improved
for better user experience.

FLTK 1.4 supports HighDPI displays under Linux/Unix and Windows and
improves HighDPI support on macOS. The initial screen scaling factor is
read from the system and application windows can be zoomed (in/out/reset)
by the user with ctrl/+/-/0 shortcuts, respectively.

CMake support has been improved significantly and requires CMake 3.15 or
higher, autotools/configure/make is still supported. The latter will be
dropped in the next minor release (1.5.0).

macOS is supported up to 15.3 "Sequoia".

The platform dependent code in FLTK 1.4 was rewritten to enable easier
porting to new platforms. Basically all platform dependent code has
been isolated and implemented in virtual methods of "driver" classes.
For details see 'src/drivers' and subdirectories.

FLTK is now compatible with the Wayland platform on current Linux
distributions and FreeBSD. The default build of the library on these
platforms supports both X11 and Wayland in a "hybrid" library. Programs
compiled and linked to this library start using Wayland if it is
available at runtime and fall back to using X11 if not. Programs using
X11 specific code that are not yet ported to Wayland can still be used
on pure X11 systems or by disabling the Wayland support on startup so
they fall back to using X11 only. This requires 'XWayland' support on
Wayland enabled (Linux) systems.

[1] https://www.fltk.org/doc-1.4/ (HTML) and
    https://www.fltk.org/doc-1.4/fltk.pdf (PDF)
[2] https://github.com/fltk/fltk.git
