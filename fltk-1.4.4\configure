#! /bin/sh
# Guess values for system-dependent variables and create Makefiles.
# Generated by GNU Autoconf 2.71 for fltk 1.4.4.
#
# Report bugs to <https://github.com/fltk/fltk/issues>.
#
#
# Copyright (C) 1992-1996, 1998-2017, 2020-2021 Free Software Foundation,
# Inc.
#
#
# This configure script is free software; the Free Software Foundation
# gives unlimited permission to copy, distribute and modify it.
## -------------------- ##
## M4sh Initialization. ##
## -------------------- ##

# Be more Bourne compatible
DUALCASE=1; export DUALCASE # for MKS sh
as_nop=:
if test ${ZSH_VERSION+y} && (emulate sh) >/dev/null 2>&1
then :
  emulate sh
  NULLCMD=:
  # Pre-4.2 versions of Zsh do word splitting on ${1+"$@"}, which
  # is contrary to our usage.  Disable this feature.
  alias -g '${1+"$@"}'='"$@"'
  setopt NO_GLOB_SUBST
else $as_nop
  case `(set -o) 2>/dev/null` in #(
  *posix*) :
    set -o posix ;; #(
  *) :
     ;;
esac
fi



# Reset variables that may have inherited troublesome values from
# the environment.

# IFS needs to be set, to space, tab, and newline, in precisely that order.
# (If _AS_PATH_WALK were called with IFS unset, it would have the
# side effect of setting IFS to empty, thus disabling word splitting.)
# Quoting is to prevent editors from complaining about space-tab.
as_nl='
'
export as_nl
IFS=" ""	$as_nl"

PS1='$ '
PS2='> '
PS4='+ '

# Ensure predictable behavior from utilities with locale-dependent output.
LC_ALL=C
export LC_ALL
LANGUAGE=C
export LANGUAGE

# We cannot yet rely on "unset" to work, but we need these variables
# to be unset--not just set to an empty or harmless value--now, to
# avoid bugs in old shells (e.g. pre-3.0 UWIN ksh).  This construct
# also avoids known problems related to "unset" and subshell syntax
# in other old shells (e.g. bash 2.01 and pdksh 5.2.14).
for as_var in BASH_ENV ENV MAIL MAILPATH CDPATH
do eval test \${$as_var+y} \
  && ( (unset $as_var) || exit 1) >/dev/null 2>&1 && unset $as_var || :
done

# Ensure that fds 0, 1, and 2 are open.
if (exec 3>&0) 2>/dev/null; then :; else exec 0</dev/null; fi
if (exec 3>&1) 2>/dev/null; then :; else exec 1>/dev/null; fi
if (exec 3>&2)            ; then :; else exec 2>/dev/null; fi

# The user is always right.
if ${PATH_SEPARATOR+false} :; then
  PATH_SEPARATOR=:
  (PATH='/bin;/bin'; FPATH=$PATH; sh -c :) >/dev/null 2>&1 && {
    (PATH='/bin:/bin'; FPATH=$PATH; sh -c :) >/dev/null 2>&1 ||
      PATH_SEPARATOR=';'
  }
fi


# Find who we are.  Look in the path if we contain no directory separator.
as_myself=
case $0 in #((
  *[\\/]* ) as_myself=$0 ;;
  *) as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    test -r "$as_dir$0" && as_myself=$as_dir$0 && break
  done
IFS=$as_save_IFS

     ;;
esac
# We did not find ourselves, most probably we were run as `sh COMMAND'
# in which case we are not to be found in the path.
if test "x$as_myself" = x; then
  as_myself=$0
fi
if test ! -f "$as_myself"; then
  printf "%s\n" "$as_myself: error: cannot find myself; rerun with an absolute file name" >&2
  exit 1
fi


# Use a proper internal environment variable to ensure we don't fall
  # into an infinite loop, continuously re-executing ourselves.
  if test x"${_as_can_reexec}" != xno && test "x$CONFIG_SHELL" != x; then
    _as_can_reexec=no; export _as_can_reexec;
    # We cannot yet assume a decent shell, so we have to provide a
# neutralization value for shells without unset; and this also
# works around shells that cannot unset nonexistent variables.
# Preserve -v and -x to the replacement shell.
BASH_ENV=/dev/null
ENV=/dev/null
(unset BASH_ENV) >/dev/null 2>&1 && unset BASH_ENV ENV
case $- in # ((((
  *v*x* | *x*v* ) as_opts=-vx ;;
  *v* ) as_opts=-v ;;
  *x* ) as_opts=-x ;;
  * ) as_opts= ;;
esac
exec $CONFIG_SHELL $as_opts "$as_myself" ${1+"$@"}
# Admittedly, this is quite paranoid, since all the known shells bail
# out after a failed `exec'.
printf "%s\n" "$0: could not re-execute with $CONFIG_SHELL" >&2
exit 255
  fi
  # We don't want this to propagate to other subprocesses.
          { _as_can_reexec=; unset _as_can_reexec;}
if test "x$CONFIG_SHELL" = x; then
  as_bourne_compatible="as_nop=:
if test \${ZSH_VERSION+y} && (emulate sh) >/dev/null 2>&1
then :
  emulate sh
  NULLCMD=:
  # Pre-4.2 versions of Zsh do word splitting on \${1+\"\$@\"}, which
  # is contrary to our usage.  Disable this feature.
  alias -g '\${1+\"\$@\"}'='\"\$@\"'
  setopt NO_GLOB_SUBST
else \$as_nop
  case \`(set -o) 2>/dev/null\` in #(
  *posix*) :
    set -o posix ;; #(
  *) :
     ;;
esac
fi
"
  as_required="as_fn_return () { (exit \$1); }
as_fn_success () { as_fn_return 0; }
as_fn_failure () { as_fn_return 1; }
as_fn_ret_success () { return 0; }
as_fn_ret_failure () { return 1; }

exitcode=0
as_fn_success || { exitcode=1; echo as_fn_success failed.; }
as_fn_failure && { exitcode=1; echo as_fn_failure succeeded.; }
as_fn_ret_success || { exitcode=1; echo as_fn_ret_success failed.; }
as_fn_ret_failure && { exitcode=1; echo as_fn_ret_failure succeeded.; }
if ( set x; as_fn_ret_success y && test x = \"\$1\" )
then :

else \$as_nop
  exitcode=1; echo positional parameters were not saved.
fi
test x\$exitcode = x0 || exit 1
blah=\$(echo \$(echo blah))
test x\"\$blah\" = xblah || exit 1
test -x / || exit 1"
  as_suggested="  as_lineno_1=";as_suggested=$as_suggested$LINENO;as_suggested=$as_suggested" as_lineno_1a=\$LINENO
  as_lineno_2=";as_suggested=$as_suggested$LINENO;as_suggested=$as_suggested" as_lineno_2a=\$LINENO
  eval 'test \"x\$as_lineno_1'\$as_run'\" != \"x\$as_lineno_2'\$as_run'\" &&
  test \"x\`expr \$as_lineno_1'\$as_run' + 1\`\" = \"x\$as_lineno_2'\$as_run'\"' || exit 1
test \$(( 1 + 1 )) = 2 || exit 1"
  if (eval "$as_required") 2>/dev/null
then :
  as_have_required=yes
else $as_nop
  as_have_required=no
fi
  if test x$as_have_required = xyes && (eval "$as_suggested") 2>/dev/null
then :

else $as_nop
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
as_found=false
for as_dir in /bin$PATH_SEPARATOR/usr/bin$PATH_SEPARATOR$PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
  as_found=:
  case $as_dir in #(
	 /*)
	   for as_base in sh bash ksh sh5; do
	     # Try only shells that exist, to save several forks.
	     as_shell=$as_dir$as_base
	     if { test -f "$as_shell" || test -f "$as_shell.exe"; } &&
		    as_run=a "$as_shell" -c "$as_bourne_compatible""$as_required" 2>/dev/null
then :
  CONFIG_SHELL=$as_shell as_have_required=yes
		   if as_run=a "$as_shell" -c "$as_bourne_compatible""$as_suggested" 2>/dev/null
then :
  break 2
fi
fi
	   done;;
       esac
  as_found=false
done
IFS=$as_save_IFS
if $as_found
then :

else $as_nop
  if { test -f "$SHELL" || test -f "$SHELL.exe"; } &&
	      as_run=a "$SHELL" -c "$as_bourne_compatible""$as_required" 2>/dev/null
then :
  CONFIG_SHELL=$SHELL as_have_required=yes
fi
fi


      if test "x$CONFIG_SHELL" != x
then :
  export CONFIG_SHELL
             # We cannot yet assume a decent shell, so we have to provide a
# neutralization value for shells without unset; and this also
# works around shells that cannot unset nonexistent variables.
# Preserve -v and -x to the replacement shell.
BASH_ENV=/dev/null
ENV=/dev/null
(unset BASH_ENV) >/dev/null 2>&1 && unset BASH_ENV ENV
case $- in # ((((
  *v*x* | *x*v* ) as_opts=-vx ;;
  *v* ) as_opts=-v ;;
  *x* ) as_opts=-x ;;
  * ) as_opts= ;;
esac
exec $CONFIG_SHELL $as_opts "$as_myself" ${1+"$@"}
# Admittedly, this is quite paranoid, since all the known shells bail
# out after a failed `exec'.
printf "%s\n" "$0: could not re-execute with $CONFIG_SHELL" >&2
exit 255
fi

    if test x$as_have_required = xno
then :
  printf "%s\n" "$0: This script requires a shell more modern than all"
  printf "%s\n" "$0: the shells that I found on your system."
  if test ${ZSH_VERSION+y} ; then
    printf "%s\n" "$0: In particular, zsh $ZSH_VERSION has bugs and should"
    printf "%s\n" "$0: be upgraded to zsh 4.3.4 or later."
  else
    printf "%s\n" "$0: <NAME_EMAIL> and
$0: https://github.com/fltk/fltk/issues about your system,
$0: including any error possibly output before this
$0: message. Then install a modern shell, or manually run
$0: the script under such a shell if you do have one."
  fi
  exit 1
fi
fi
fi
SHELL=${CONFIG_SHELL-/bin/sh}
export SHELL
# Unset more variables known to interfere with behavior of common tools.
CLICOLOR_FORCE= GREP_OPTIONS=
unset CLICOLOR_FORCE GREP_OPTIONS

## --------------------- ##
## M4sh Shell Functions. ##
## --------------------- ##
# as_fn_unset VAR
# ---------------
# Portably unset VAR.
as_fn_unset ()
{
  { eval $1=; unset $1;}
}
as_unset=as_fn_unset


# as_fn_set_status STATUS
# -----------------------
# Set $? to STATUS, without forking.
as_fn_set_status ()
{
  return $1
} # as_fn_set_status

# as_fn_exit STATUS
# -----------------
# Exit the shell with STATUS, even in a "trap 0" or "set -e" context.
as_fn_exit ()
{
  set +e
  as_fn_set_status $1
  exit $1
} # as_fn_exit
# as_fn_nop
# ---------
# Do nothing but, unlike ":", preserve the value of $?.
as_fn_nop ()
{
  return $?
}
as_nop=as_fn_nop

# as_fn_mkdir_p
# -------------
# Create "$as_dir" as a directory, including parents if necessary.
as_fn_mkdir_p ()
{

  case $as_dir in #(
  -*) as_dir=./$as_dir;;
  esac
  test -d "$as_dir" || eval $as_mkdir_p || {
    as_dirs=
    while :; do
      case $as_dir in #(
      *\'*) as_qdir=`printf "%s\n" "$as_dir" | sed "s/'/'\\\\\\\\''/g"`;; #'(
      *) as_qdir=$as_dir;;
      esac
      as_dirs="'$as_qdir' $as_dirs"
      as_dir=`$as_dirname -- "$as_dir" ||
$as_expr X"$as_dir" : 'X\(.*[^/]\)//*[^/][^/]*/*$' \| \
	 X"$as_dir" : 'X\(//\)[^/]' \| \
	 X"$as_dir" : 'X\(//\)$' \| \
	 X"$as_dir" : 'X\(/\)' \| . 2>/dev/null ||
printf "%s\n" X"$as_dir" |
    sed '/^X\(.*[^/]\)\/\/*[^/][^/]*\/*$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)[^/].*/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`
      test -d "$as_dir" && break
    done
    test -z "$as_dirs" || eval "mkdir $as_dirs"
  } || test -d "$as_dir" || as_fn_error $? "cannot create directory $as_dir"


} # as_fn_mkdir_p

# as_fn_executable_p FILE
# -----------------------
# Test if FILE is an executable regular file.
as_fn_executable_p ()
{
  test -f "$1" && test -x "$1"
} # as_fn_executable_p
# as_fn_append VAR VALUE
# ----------------------
# Append the text in VALUE to the end of the definition contained in VAR. Take
# advantage of any shell optimizations that allow amortized linear growth over
# repeated appends, instead of the typical quadratic growth present in naive
# implementations.
if (eval "as_var=1; as_var+=2; test x\$as_var = x12") 2>/dev/null
then :
  eval 'as_fn_append ()
  {
    eval $1+=\$2
  }'
else $as_nop
  as_fn_append ()
  {
    eval $1=\$$1\$2
  }
fi # as_fn_append

# as_fn_arith ARG...
# ------------------
# Perform arithmetic evaluation on the ARGs, and store the result in the
# global $as_val. Take advantage of shells that can avoid forks. The arguments
# must be portable across $(()) and expr.
if (eval "test \$(( 1 + 1 )) = 2") 2>/dev/null
then :
  eval 'as_fn_arith ()
  {
    as_val=$(( $* ))
  }'
else $as_nop
  as_fn_arith ()
  {
    as_val=`expr "$@" || test $? -eq 1`
  }
fi # as_fn_arith

# as_fn_nop
# ---------
# Do nothing but, unlike ":", preserve the value of $?.
as_fn_nop ()
{
  return $?
}
as_nop=as_fn_nop

# as_fn_error STATUS ERROR [LINENO LOG_FD]
# ----------------------------------------
# Output "`basename $0`: error: ERROR" to stderr. If LINENO and LOG_FD are
# provided, also output the error to LOG_FD, referencing LINENO. Then exit the
# script with STATUS, using 1 if that was 0.
as_fn_error ()
{
  as_status=$1; test $as_status -eq 0 && as_status=1
  if test "$4"; then
    as_lineno=${as_lineno-"$3"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: $2" >&$4
  fi
  printf "%s\n" "$as_me: error: $2" >&2
  as_fn_exit $as_status
} # as_fn_error

if expr a : '\(a\)' >/dev/null 2>&1 &&
   test "X`expr 00001 : '.*\(...\)'`" = X001; then
  as_expr=expr
else
  as_expr=false
fi

if (basename -- /) >/dev/null 2>&1 && test "X`basename -- / 2>&1`" = "X/"; then
  as_basename=basename
else
  as_basename=false
fi

if (as_dir=`dirname -- /` && test "X$as_dir" = X/) >/dev/null 2>&1; then
  as_dirname=dirname
else
  as_dirname=false
fi

as_me=`$as_basename -- "$0" ||
$as_expr X/"$0" : '.*/\([^/][^/]*\)/*$' \| \
	 X"$0" : 'X\(//\)$' \| \
	 X"$0" : 'X\(/\)' \| . 2>/dev/null ||
printf "%s\n" X/"$0" |
    sed '/^.*\/\([^/][^/]*\)\/*$/{
	    s//\1/
	    q
	  }
	  /^X\/\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\/\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`

# Avoid depending upon Character Ranges.
as_cr_letters='abcdefghijklmnopqrstuvwxyz'
as_cr_LETTERS='ABCDEFGHIJKLMNOPQRSTUVWXYZ'
as_cr_Letters=$as_cr_letters$as_cr_LETTERS
as_cr_digits='0123456789'
as_cr_alnum=$as_cr_Letters$as_cr_digits


  as_lineno_1=$LINENO as_lineno_1a=$LINENO
  as_lineno_2=$LINENO as_lineno_2a=$LINENO
  eval 'test "x$as_lineno_1'$as_run'" != "x$as_lineno_2'$as_run'" &&
  test "x`expr $as_lineno_1'$as_run' + 1`" = "x$as_lineno_2'$as_run'"' || {
  # Blame Lee E. McMahon (1931-1989) for sed's syntax.  :-)
  sed -n '
    p
    /[$]LINENO/=
  ' <$as_myself |
    sed '
      s/[$]LINENO.*/&-/
      t lineno
      b
      :lineno
      N
      :loop
      s/[$]LINENO\([^'$as_cr_alnum'_].*\n\)\(.*\)/\2\1\2/
      t loop
      s/-\n.*//
    ' >$as_me.lineno &&
  chmod +x "$as_me.lineno" ||
    { printf "%s\n" "$as_me: error: cannot create $as_me.lineno; rerun with a POSIX shell" >&2; as_fn_exit 1; }

  # If we had to re-execute with $CONFIG_SHELL, we're ensured to have
  # already done that, so ensure we don't try to do so again and fall
  # in an infinite loop.  This has already happened in practice.
  _as_can_reexec=no; export _as_can_reexec
  # Don't try to exec as it changes $[0], causing all sort of problems
  # (the dirname of $[0] is not the place where we might find the
  # original and so on.  Autoconf is especially sensitive to this).
  . "./$as_me.lineno"
  # Exit status is that of the last command.
  exit
}


# Determine whether it's possible to make 'echo' print without a newline.
# These variables are no longer used directly by Autoconf, but are AC_SUBSTed
# for compatibility with existing Makefiles.
ECHO_C= ECHO_N= ECHO_T=
case `echo -n x` in #(((((
-n*)
  case `echo 'xy\c'` in
  *c*) ECHO_T='	';;	# ECHO_T is single tab character.
  xy)  ECHO_C='\c';;
  *)   echo `echo ksh88 bug on AIX 6.1` > /dev/null
       ECHO_T='	';;
  esac;;
*)
  ECHO_N='-n';;
esac

# For backward compatibility with old third-party macros, we provide
# the shell variables $as_echo and $as_echo_n.  New code should use
# AS_ECHO(["message"]) and AS_ECHO_N(["message"]), respectively.
as_echo='printf %s\n'
as_echo_n='printf %s'


rm -f conf$$ conf$$.exe conf$$.file
if test -d conf$$.dir; then
  rm -f conf$$.dir/conf$$.file
else
  rm -f conf$$.dir
  mkdir conf$$.dir 2>/dev/null
fi
if (echo >conf$$.file) 2>/dev/null; then
  if ln -s conf$$.file conf$$ 2>/dev/null; then
    as_ln_s='ln -s'
    # ... but there are two gotchas:
    # 1) On MSYS, both `ln -s file dir' and `ln file dir' fail.
    # 2) DJGPP < 2.04 has no symlinks; `ln -s' creates a wrapper executable.
    # In both cases, we have to default to `cp -pR'.
    ln -s conf$$.file conf$$.dir 2>/dev/null && test ! -f conf$$.exe ||
      as_ln_s='cp -pR'
  elif ln conf$$.file conf$$ 2>/dev/null; then
    as_ln_s=ln
  else
    as_ln_s='cp -pR'
  fi
else
  as_ln_s='cp -pR'
fi
rm -f conf$$ conf$$.exe conf$$.dir/conf$$.file conf$$.file
rmdir conf$$.dir 2>/dev/null

if mkdir -p . 2>/dev/null; then
  as_mkdir_p='mkdir -p "$as_dir"'
else
  test -d ./-p && rmdir ./-p
  as_mkdir_p=false
fi

as_test_x='test -x'
as_executable_p=as_fn_executable_p

# Sed expression to map a string onto a valid CPP name.
as_tr_cpp="eval sed 'y%*$as_cr_letters%P$as_cr_LETTERS%;s%[^_$as_cr_alnum]%_%g'"

# Sed expression to map a string onto a valid variable name.
as_tr_sh="eval sed 'y%*+%pp%;s%[^_$as_cr_alnum]%_%g'"


test -n "$DJDIR" || exec 7<&0 </dev/null
exec 6>&1

# Name of the host.
# hostname on some systems (SVR3.2, old GNU/Linux) returns a bogus exit status,
# so uname gets run too.
ac_hostname=`(hostname || uname -n) 2>/dev/null | sed 1q`

#
# Initializations.
#
ac_default_prefix=/usr/local
ac_clean_files=
ac_config_libobj_dir=.
LIBOBJS=
cross_compiling=no
subdirs=
MFLAGS=
MAKEFLAGS=

# Identity of this package.
PACKAGE_NAME='fltk'
PACKAGE_TARNAME='fltk'
PACKAGE_VERSION='1.4.4'
PACKAGE_STRING='fltk 1.4.4'
PACKAGE_BUGREPORT='https://github.com/fltk/fltk/issues'
PACKAGE_URL='https://www.fltk.org/'

ac_unique_file="src/Fl.cxx"
# Factoring default headers for most tests.
ac_includes_default="\
#include <stddef.h>
#ifdef HAVE_STDIO_H
# include <stdio.h>
#endif
#ifdef HAVE_STDLIB_H
# include <stdlib.h>
#endif
#ifdef HAVE_STRING_H
# include <string.h>
#endif
#ifdef HAVE_INTTYPES_H
# include <inttypes.h>
#endif
#ifdef HAVE_STDINT_H
# include <stdint.h>
#endif
#ifdef HAVE_STRINGS_H
# include <strings.h>
#endif
#ifdef HAVE_SYS_TYPES_H
# include <sys/types.h>
#endif
#ifdef HAVE_SYS_STAT_H
# include <sys/stat.h>
#endif
#ifdef HAVE_UNISTD_H
# include <unistd.h>
#endif"

ac_header_c_list=
ac_subst_vars='LTLIBOBJS
LIBOBJS
BINARY_DIR
MAKEDEPEND
CAT6EXT
CAT3EXT
CAT1EXT
BUILD
UNINSTALL_DESKTOP
INSTALL_DESKTOP
LIBDECORDBUS
LIBDECORDIR
FLUIDDIR
THREADS
OSX_ONLY
HLINKS
GLLIBS
FTCONFIG
X_EXTRA_LIBS
X_LIBS
X_PRE_LIBS
X_CFLAGS
CPP
XMKMF
HAVE_PTHREAD_MUTEX_RECURSIVE
PTHREAD_FLAGS
ZLIBINC
ZLIB
HAVE_PNG_H
PNGINC
PNG
JPEGINC
JPEG
STATICIMAGELIBS
IMAGELIBS
AUDIOLIBS
LARGEFILE
RC
LIBCOMMAND
AR
RANLIB
DOXYDOC
NROFF
INSTALL_DATA
INSTALL_SCRIPT
INSTALL_PROGRAM
FLTK_OPTIONS
FLUID_BUILD
FLUID
SHAREDSUFFIX
CAIRODSONAME
IMGDSONAME
GLDSONAME
DSONAME
DSOLINK
DSOFLAGS
DSOCOMMAND
LINKSHARED
LINKFLTKFORMS
FLDSONAME
FLLIBBASENAME
FLLIBNAME
CAIROLIBBASENAME
IMGLIBBASENAME
GLLIBBASENAME
LIBBASENAME
LINKFLTKIMG
LINKFLTKGL
LINKFLTK
LIBNAME
LIBEXT
CAIROLIBNAME
IMGLIBNAME
GLLIBNAME
GLDEMOS
FLTKCAIROOPTION
LINKFLTKCAIRO
CAIROLIBS
CAIROFLAGS
CAIRODIR
TESTDIR
OPTIM
ARCHFLAGS
PKGCONFIG
ac_ct_CXX
CXXFLAGS
CXX
OBJEXT
EXEEXT
ac_ct_CC
CPPFLAGS
LDFLAGS
CFLAGS
CC
host_os
host_vendor
host_cpu
host
build_os
build_vendor
build_cpu
build
FL_ABI_VERSION
FL_DSO_VERSION
FLTK_VERSION_PATCH
FLTK_VERSION_MINOR
FLTK_VERSION_MAJOR
FLTK_VERSION
target_alias
host_alias
build_alias
LIBS
ECHO_T
ECHO_N
ECHO_C
DEFS
mandir
localedir
libdir
psdir
pdfdir
dvidir
htmldir
infodir
docdir
oldincludedir
includedir
runstatedir
localstatedir
sharedstatedir
sysconfdir
datadir
datarootdir
libexecdir
sbindir
bindir
program_transform_name
prefix
exec_prefix
PACKAGE_URL
PACKAGE_BUGREPORT
PACKAGE_STRING
PACKAGE_VERSION
PACKAGE_TARNAME
PACKAGE_NAME
PATH_SEPARATOR
SHELL'
ac_subst_files=''
ac_user_opts='
enable_option_checking
with_archflags
with_optim
enable_cairo
enable_cairoext
enable_cp936
enable_cygwin
enable_debug
enable_test
enable_forms
enable_gl
enable_localjpeg
enable_localpng
enable_localzlib
enable_pango
enable_wayland
enable_usecairo
enable_use_std
enable_print
enable_shared
enable_svg
enable_threads
enable_x11
enable_xcursor
enable_xfixes
enable_xft
enable_xinerama
enable_xrender
enable_fluid
enable_gdiplus
with_abiversion
enable_largefile
with_links
with_x
'
      ac_precious_vars='build_alias
host_alias
target_alias
CC
CFLAGS
LDFLAGS
LIBS
CPPFLAGS
CXX
CXXFLAGS
CCC
XMKMF
CPP'


# Initialize some variables set by options.
ac_init_help=
ac_init_version=false
ac_unrecognized_opts=
ac_unrecognized_sep=
# The variables have the same names as the options, with
# dashes changed to underlines.
cache_file=/dev/null
exec_prefix=NONE
no_create=
no_recursion=
prefix=NONE
program_prefix=NONE
program_suffix=NONE
program_transform_name=s,x,x,
silent=
site=
srcdir=
verbose=
x_includes=NONE
x_libraries=NONE

# Installation directory options.
# These are left unexpanded so users can "make install exec_prefix=/foo"
# and all the variables that are supposed to be based on exec_prefix
# by default will actually change.
# Use braces instead of parens because sh, perl, etc. also accept them.
# (The list follows the same order as the GNU Coding Standards.)
bindir='${exec_prefix}/bin'
sbindir='${exec_prefix}/sbin'
libexecdir='${exec_prefix}/libexec'
datarootdir='${prefix}/share'
datadir='${datarootdir}'
sysconfdir='${prefix}/etc'
sharedstatedir='${prefix}/com'
localstatedir='${prefix}/var'
runstatedir='${localstatedir}/run'
includedir='${prefix}/include'
oldincludedir='/usr/include'
docdir='${datarootdir}/doc/${PACKAGE_TARNAME}'
infodir='${datarootdir}/info'
htmldir='${docdir}'
dvidir='${docdir}'
pdfdir='${docdir}'
psdir='${docdir}'
libdir='${exec_prefix}/lib'
localedir='${datarootdir}/locale'
mandir='${datarootdir}/man'

ac_prev=
ac_dashdash=
for ac_option
do
  # If the previous option needs an argument, assign it.
  if test -n "$ac_prev"; then
    eval $ac_prev=\$ac_option
    ac_prev=
    continue
  fi

  case $ac_option in
  *=?*) ac_optarg=`expr "X$ac_option" : '[^=]*=\(.*\)'` ;;
  *=)   ac_optarg= ;;
  *)    ac_optarg=yes ;;
  esac

  case $ac_dashdash$ac_option in
  --)
    ac_dashdash=yes ;;

  -bindir | --bindir | --bindi | --bind | --bin | --bi)
    ac_prev=bindir ;;
  -bindir=* | --bindir=* | --bindi=* | --bind=* | --bin=* | --bi=*)
    bindir=$ac_optarg ;;

  -build | --build | --buil | --bui | --bu)
    ac_prev=build_alias ;;
  -build=* | --build=* | --buil=* | --bui=* | --bu=*)
    build_alias=$ac_optarg ;;

  -cache-file | --cache-file | --cache-fil | --cache-fi \
  | --cache-f | --cache- | --cache | --cach | --cac | --ca | --c)
    ac_prev=cache_file ;;
  -cache-file=* | --cache-file=* | --cache-fil=* | --cache-fi=* \
  | --cache-f=* | --cache-=* | --cache=* | --cach=* | --cac=* | --ca=* | --c=*)
    cache_file=$ac_optarg ;;

  --config-cache | -C)
    cache_file=config.cache ;;

  -datadir | --datadir | --datadi | --datad)
    ac_prev=datadir ;;
  -datadir=* | --datadir=* | --datadi=* | --datad=*)
    datadir=$ac_optarg ;;

  -datarootdir | --datarootdir | --datarootdi | --datarootd | --dataroot \
  | --dataroo | --dataro | --datar)
    ac_prev=datarootdir ;;
  -datarootdir=* | --datarootdir=* | --datarootdi=* | --datarootd=* \
  | --dataroot=* | --dataroo=* | --dataro=* | --datar=*)
    datarootdir=$ac_optarg ;;

  -disable-* | --disable-*)
    ac_useropt=`expr "x$ac_option" : 'x-*disable-\(.*\)'`
    # Reject names that are not valid shell variable names.
    expr "x$ac_useropt" : ".*[^-+._$as_cr_alnum]" >/dev/null &&
      as_fn_error $? "invalid feature name: \`$ac_useropt'"
    ac_useropt_orig=$ac_useropt
    ac_useropt=`printf "%s\n" "$ac_useropt" | sed 's/[-+.]/_/g'`
    case $ac_user_opts in
      *"
"enable_$ac_useropt"
"*) ;;
      *) ac_unrecognized_opts="$ac_unrecognized_opts$ac_unrecognized_sep--disable-$ac_useropt_orig"
	 ac_unrecognized_sep=', ';;
    esac
    eval enable_$ac_useropt=no ;;

  -docdir | --docdir | --docdi | --doc | --do)
    ac_prev=docdir ;;
  -docdir=* | --docdir=* | --docdi=* | --doc=* | --do=*)
    docdir=$ac_optarg ;;

  -dvidir | --dvidir | --dvidi | --dvid | --dvi | --dv)
    ac_prev=dvidir ;;
  -dvidir=* | --dvidir=* | --dvidi=* | --dvid=* | --dvi=* | --dv=*)
    dvidir=$ac_optarg ;;

  -enable-* | --enable-*)
    ac_useropt=`expr "x$ac_option" : 'x-*enable-\([^=]*\)'`
    # Reject names that are not valid shell variable names.
    expr "x$ac_useropt" : ".*[^-+._$as_cr_alnum]" >/dev/null &&
      as_fn_error $? "invalid feature name: \`$ac_useropt'"
    ac_useropt_orig=$ac_useropt
    ac_useropt=`printf "%s\n" "$ac_useropt" | sed 's/[-+.]/_/g'`
    case $ac_user_opts in
      *"
"enable_$ac_useropt"
"*) ;;
      *) ac_unrecognized_opts="$ac_unrecognized_opts$ac_unrecognized_sep--enable-$ac_useropt_orig"
	 ac_unrecognized_sep=', ';;
    esac
    eval enable_$ac_useropt=\$ac_optarg ;;

  -exec-prefix | --exec_prefix | --exec-prefix | --exec-prefi \
  | --exec-pref | --exec-pre | --exec-pr | --exec-p | --exec- \
  | --exec | --exe | --ex)
    ac_prev=exec_prefix ;;
  -exec-prefix=* | --exec_prefix=* | --exec-prefix=* | --exec-prefi=* \
  | --exec-pref=* | --exec-pre=* | --exec-pr=* | --exec-p=* | --exec-=* \
  | --exec=* | --exe=* | --ex=*)
    exec_prefix=$ac_optarg ;;

  -gas | --gas | --ga | --g)
    # Obsolete; use --with-gas.
    with_gas=yes ;;

  -help | --help | --hel | --he | -h)
    ac_init_help=long ;;
  -help=r* | --help=r* | --hel=r* | --he=r* | -hr*)
    ac_init_help=recursive ;;
  -help=s* | --help=s* | --hel=s* | --he=s* | -hs*)
    ac_init_help=short ;;

  -host | --host | --hos | --ho)
    ac_prev=host_alias ;;
  -host=* | --host=* | --hos=* | --ho=*)
    host_alias=$ac_optarg ;;

  -htmldir | --htmldir | --htmldi | --htmld | --html | --htm | --ht)
    ac_prev=htmldir ;;
  -htmldir=* | --htmldir=* | --htmldi=* | --htmld=* | --html=* | --htm=* \
  | --ht=*)
    htmldir=$ac_optarg ;;

  -includedir | --includedir | --includedi | --included | --include \
  | --includ | --inclu | --incl | --inc)
    ac_prev=includedir ;;
  -includedir=* | --includedir=* | --includedi=* | --included=* | --include=* \
  | --includ=* | --inclu=* | --incl=* | --inc=*)
    includedir=$ac_optarg ;;

  -infodir | --infodir | --infodi | --infod | --info | --inf)
    ac_prev=infodir ;;
  -infodir=* | --infodir=* | --infodi=* | --infod=* | --info=* | --inf=*)
    infodir=$ac_optarg ;;

  -libdir | --libdir | --libdi | --libd)
    ac_prev=libdir ;;
  -libdir=* | --libdir=* | --libdi=* | --libd=*)
    libdir=$ac_optarg ;;

  -libexecdir | --libexecdir | --libexecdi | --libexecd | --libexec \
  | --libexe | --libex | --libe)
    ac_prev=libexecdir ;;
  -libexecdir=* | --libexecdir=* | --libexecdi=* | --libexecd=* | --libexec=* \
  | --libexe=* | --libex=* | --libe=*)
    libexecdir=$ac_optarg ;;

  -localedir | --localedir | --localedi | --localed | --locale)
    ac_prev=localedir ;;
  -localedir=* | --localedir=* | --localedi=* | --localed=* | --locale=*)
    localedir=$ac_optarg ;;

  -localstatedir | --localstatedir | --localstatedi | --localstated \
  | --localstate | --localstat | --localsta | --localst | --locals)
    ac_prev=localstatedir ;;
  -localstatedir=* | --localstatedir=* | --localstatedi=* | --localstated=* \
  | --localstate=* | --localstat=* | --localsta=* | --localst=* | --locals=*)
    localstatedir=$ac_optarg ;;

  -mandir | --mandir | --mandi | --mand | --man | --ma | --m)
    ac_prev=mandir ;;
  -mandir=* | --mandir=* | --mandi=* | --mand=* | --man=* | --ma=* | --m=*)
    mandir=$ac_optarg ;;

  -nfp | --nfp | --nf)
    # Obsolete; use --without-fp.
    with_fp=no ;;

  -no-create | --no-create | --no-creat | --no-crea | --no-cre \
  | --no-cr | --no-c | -n)
    no_create=yes ;;

  -no-recursion | --no-recursion | --no-recursio | --no-recursi \
  | --no-recurs | --no-recur | --no-recu | --no-rec | --no-re | --no-r)
    no_recursion=yes ;;

  -oldincludedir | --oldincludedir | --oldincludedi | --oldincluded \
  | --oldinclude | --oldinclud | --oldinclu | --oldincl | --oldinc \
  | --oldin | --oldi | --old | --ol | --o)
    ac_prev=oldincludedir ;;
  -oldincludedir=* | --oldincludedir=* | --oldincludedi=* | --oldincluded=* \
  | --oldinclude=* | --oldinclud=* | --oldinclu=* | --oldincl=* | --oldinc=* \
  | --oldin=* | --oldi=* | --old=* | --ol=* | --o=*)
    oldincludedir=$ac_optarg ;;

  -prefix | --prefix | --prefi | --pref | --pre | --pr | --p)
    ac_prev=prefix ;;
  -prefix=* | --prefix=* | --prefi=* | --pref=* | --pre=* | --pr=* | --p=*)
    prefix=$ac_optarg ;;

  -program-prefix | --program-prefix | --program-prefi | --program-pref \
  | --program-pre | --program-pr | --program-p)
    ac_prev=program_prefix ;;
  -program-prefix=* | --program-prefix=* | --program-prefi=* \
  | --program-pref=* | --program-pre=* | --program-pr=* | --program-p=*)
    program_prefix=$ac_optarg ;;

  -program-suffix | --program-suffix | --program-suffi | --program-suff \
  | --program-suf | --program-su | --program-s)
    ac_prev=program_suffix ;;
  -program-suffix=* | --program-suffix=* | --program-suffi=* \
  | --program-suff=* | --program-suf=* | --program-su=* | --program-s=*)
    program_suffix=$ac_optarg ;;

  -program-transform-name | --program-transform-name \
  | --program-transform-nam | --program-transform-na \
  | --program-transform-n | --program-transform- \
  | --program-transform | --program-transfor \
  | --program-transfo | --program-transf \
  | --program-trans | --program-tran \
  | --progr-tra | --program-tr | --program-t)
    ac_prev=program_transform_name ;;
  -program-transform-name=* | --program-transform-name=* \
  | --program-transform-nam=* | --program-transform-na=* \
  | --program-transform-n=* | --program-transform-=* \
  | --program-transform=* | --program-transfor=* \
  | --program-transfo=* | --program-transf=* \
  | --program-trans=* | --program-tran=* \
  | --progr-tra=* | --program-tr=* | --program-t=*)
    program_transform_name=$ac_optarg ;;

  -pdfdir | --pdfdir | --pdfdi | --pdfd | --pdf | --pd)
    ac_prev=pdfdir ;;
  -pdfdir=* | --pdfdir=* | --pdfdi=* | --pdfd=* | --pdf=* | --pd=*)
    pdfdir=$ac_optarg ;;

  -psdir | --psdir | --psdi | --psd | --ps)
    ac_prev=psdir ;;
  -psdir=* | --psdir=* | --psdi=* | --psd=* | --ps=*)
    psdir=$ac_optarg ;;

  -q | -quiet | --quiet | --quie | --qui | --qu | --q \
  | -silent | --silent | --silen | --sile | --sil)
    silent=yes ;;

  -runstatedir | --runstatedir | --runstatedi | --runstated \
  | --runstate | --runstat | --runsta | --runst | --runs \
  | --run | --ru | --r)
    ac_prev=runstatedir ;;
  -runstatedir=* | --runstatedir=* | --runstatedi=* | --runstated=* \
  | --runstate=* | --runstat=* | --runsta=* | --runst=* | --runs=* \
  | --run=* | --ru=* | --r=*)
    runstatedir=$ac_optarg ;;

  -sbindir | --sbindir | --sbindi | --sbind | --sbin | --sbi | --sb)
    ac_prev=sbindir ;;
  -sbindir=* | --sbindir=* | --sbindi=* | --sbind=* | --sbin=* \
  | --sbi=* | --sb=*)
    sbindir=$ac_optarg ;;

  -sharedstatedir | --sharedstatedir | --sharedstatedi \
  | --sharedstated | --sharedstate | --sharedstat | --sharedsta \
  | --sharedst | --shareds | --shared | --share | --shar \
  | --sha | --sh)
    ac_prev=sharedstatedir ;;
  -sharedstatedir=* | --sharedstatedir=* | --sharedstatedi=* \
  | --sharedstated=* | --sharedstate=* | --sharedstat=* | --sharedsta=* \
  | --sharedst=* | --shareds=* | --shared=* | --share=* | --shar=* \
  | --sha=* | --sh=*)
    sharedstatedir=$ac_optarg ;;

  -site | --site | --sit)
    ac_prev=site ;;
  -site=* | --site=* | --sit=*)
    site=$ac_optarg ;;

  -srcdir | --srcdir | --srcdi | --srcd | --src | --sr)
    ac_prev=srcdir ;;
  -srcdir=* | --srcdir=* | --srcdi=* | --srcd=* | --src=* | --sr=*)
    srcdir=$ac_optarg ;;

  -sysconfdir | --sysconfdir | --sysconfdi | --sysconfd | --sysconf \
  | --syscon | --sysco | --sysc | --sys | --sy)
    ac_prev=sysconfdir ;;
  -sysconfdir=* | --sysconfdir=* | --sysconfdi=* | --sysconfd=* | --sysconf=* \
  | --syscon=* | --sysco=* | --sysc=* | --sys=* | --sy=*)
    sysconfdir=$ac_optarg ;;

  -target | --target | --targe | --targ | --tar | --ta | --t)
    ac_prev=target_alias ;;
  -target=* | --target=* | --targe=* | --targ=* | --tar=* | --ta=* | --t=*)
    target_alias=$ac_optarg ;;

  -v | -verbose | --verbose | --verbos | --verbo | --verb)
    verbose=yes ;;

  -version | --version | --versio | --versi | --vers | -V)
    ac_init_version=: ;;

  -with-* | --with-*)
    ac_useropt=`expr "x$ac_option" : 'x-*with-\([^=]*\)'`
    # Reject names that are not valid shell variable names.
    expr "x$ac_useropt" : ".*[^-+._$as_cr_alnum]" >/dev/null &&
      as_fn_error $? "invalid package name: \`$ac_useropt'"
    ac_useropt_orig=$ac_useropt
    ac_useropt=`printf "%s\n" "$ac_useropt" | sed 's/[-+.]/_/g'`
    case $ac_user_opts in
      *"
"with_$ac_useropt"
"*) ;;
      *) ac_unrecognized_opts="$ac_unrecognized_opts$ac_unrecognized_sep--with-$ac_useropt_orig"
	 ac_unrecognized_sep=', ';;
    esac
    eval with_$ac_useropt=\$ac_optarg ;;

  -without-* | --without-*)
    ac_useropt=`expr "x$ac_option" : 'x-*without-\(.*\)'`
    # Reject names that are not valid shell variable names.
    expr "x$ac_useropt" : ".*[^-+._$as_cr_alnum]" >/dev/null &&
      as_fn_error $? "invalid package name: \`$ac_useropt'"
    ac_useropt_orig=$ac_useropt
    ac_useropt=`printf "%s\n" "$ac_useropt" | sed 's/[-+.]/_/g'`
    case $ac_user_opts in
      *"
"with_$ac_useropt"
"*) ;;
      *) ac_unrecognized_opts="$ac_unrecognized_opts$ac_unrecognized_sep--without-$ac_useropt_orig"
	 ac_unrecognized_sep=', ';;
    esac
    eval with_$ac_useropt=no ;;

  --x)
    # Obsolete; use --with-x.
    with_x=yes ;;

  -x-includes | --x-includes | --x-include | --x-includ | --x-inclu \
  | --x-incl | --x-inc | --x-in | --x-i)
    ac_prev=x_includes ;;
  -x-includes=* | --x-includes=* | --x-include=* | --x-includ=* | --x-inclu=* \
  | --x-incl=* | --x-inc=* | --x-in=* | --x-i=*)
    x_includes=$ac_optarg ;;

  -x-libraries | --x-libraries | --x-librarie | --x-librari \
  | --x-librar | --x-libra | --x-libr | --x-lib | --x-li | --x-l)
    ac_prev=x_libraries ;;
  -x-libraries=* | --x-libraries=* | --x-librarie=* | --x-librari=* \
  | --x-librar=* | --x-libra=* | --x-libr=* | --x-lib=* | --x-li=* | --x-l=*)
    x_libraries=$ac_optarg ;;

  -*) as_fn_error $? "unrecognized option: \`$ac_option'
Try \`$0 --help' for more information"
    ;;

  *=*)
    ac_envvar=`expr "x$ac_option" : 'x\([^=]*\)='`
    # Reject names that are not valid shell variable names.
    case $ac_envvar in #(
      '' | [0-9]* | *[!_$as_cr_alnum]* )
      as_fn_error $? "invalid variable name: \`$ac_envvar'" ;;
    esac
    eval $ac_envvar=\$ac_optarg
    export $ac_envvar ;;

  *)
    # FIXME: should be removed in autoconf 3.0.
    printf "%s\n" "$as_me: WARNING: you should use --build, --host, --target" >&2
    expr "x$ac_option" : ".*[^-._$as_cr_alnum]" >/dev/null &&
      printf "%s\n" "$as_me: WARNING: invalid host type: $ac_option" >&2
    : "${build_alias=$ac_option} ${host_alias=$ac_option} ${target_alias=$ac_option}"
    ;;

  esac
done

if test -n "$ac_prev"; then
  ac_option=--`echo $ac_prev | sed 's/_/-/g'`
  as_fn_error $? "missing argument to $ac_option"
fi

if test -n "$ac_unrecognized_opts"; then
  case $enable_option_checking in
    no) ;;
    fatal) as_fn_error $? "unrecognized options: $ac_unrecognized_opts" ;;
    *)     printf "%s\n" "$as_me: WARNING: unrecognized options: $ac_unrecognized_opts" >&2 ;;
  esac
fi

# Check all directory arguments for consistency.
for ac_var in	exec_prefix prefix bindir sbindir libexecdir datarootdir \
		datadir sysconfdir sharedstatedir localstatedir includedir \
		oldincludedir docdir infodir htmldir dvidir pdfdir psdir \
		libdir localedir mandir runstatedir
do
  eval ac_val=\$$ac_var
  # Remove trailing slashes.
  case $ac_val in
    */ )
      ac_val=`expr "X$ac_val" : 'X\(.*[^/]\)' \| "X$ac_val" : 'X\(.*\)'`
      eval $ac_var=\$ac_val;;
  esac
  # Be sure to have absolute directory names.
  case $ac_val in
    [\\/$]* | ?:[\\/]* )  continue;;
    NONE | '' ) case $ac_var in *prefix ) continue;; esac;;
  esac
  as_fn_error $? "expected an absolute directory name for --$ac_var: $ac_val"
done

# There might be people who depend on the old broken behavior: `$host'
# used to hold the argument of --host etc.
# FIXME: To remove some day.
build=$build_alias
host=$host_alias
target=$target_alias

# FIXME: To remove some day.
if test "x$host_alias" != x; then
  if test "x$build_alias" = x; then
    cross_compiling=maybe
  elif test "x$build_alias" != "x$host_alias"; then
    cross_compiling=yes
  fi
fi

ac_tool_prefix=
test -n "$host_alias" && ac_tool_prefix=$host_alias-

test "$silent" = yes && exec 6>/dev/null


ac_pwd=`pwd` && test -n "$ac_pwd" &&
ac_ls_di=`ls -di .` &&
ac_pwd_ls_di=`cd "$ac_pwd" && ls -di .` ||
  as_fn_error $? "working directory cannot be determined"
test "X$ac_ls_di" = "X$ac_pwd_ls_di" ||
  as_fn_error $? "pwd does not report name of working directory"


# Find the source files, if location was not specified.
if test -z "$srcdir"; then
  ac_srcdir_defaulted=yes
  # Try the directory containing this script, then the parent directory.
  ac_confdir=`$as_dirname -- "$as_myself" ||
$as_expr X"$as_myself" : 'X\(.*[^/]\)//*[^/][^/]*/*$' \| \
	 X"$as_myself" : 'X\(//\)[^/]' \| \
	 X"$as_myself" : 'X\(//\)$' \| \
	 X"$as_myself" : 'X\(/\)' \| . 2>/dev/null ||
printf "%s\n" X"$as_myself" |
    sed '/^X\(.*[^/]\)\/\/*[^/][^/]*\/*$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)[^/].*/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`
  srcdir=$ac_confdir
  if test ! -r "$srcdir/$ac_unique_file"; then
    srcdir=..
  fi
else
  ac_srcdir_defaulted=no
fi
if test ! -r "$srcdir/$ac_unique_file"; then
  test "$ac_srcdir_defaulted" = yes && srcdir="$ac_confdir or .."
  as_fn_error $? "cannot find sources ($ac_unique_file) in $srcdir"
fi
ac_msg="sources are in $srcdir, but \`cd $srcdir' does not work"
ac_abs_confdir=`(
	cd "$srcdir" && test -r "./$ac_unique_file" || as_fn_error $? "$ac_msg"
	pwd)`
# When building in place, set srcdir=.
if test "$ac_abs_confdir" = "$ac_pwd"; then
  srcdir=.
fi
# Remove unnecessary trailing slashes from srcdir.
# Double slashes in file names in object file debugging info
# mess up M-x gdb in Emacs.
case $srcdir in
*/) srcdir=`expr "X$srcdir" : 'X\(.*[^/]\)' \| "X$srcdir" : 'X\(.*\)'`;;
esac
for ac_var in $ac_precious_vars; do
  eval ac_env_${ac_var}_set=\${${ac_var}+set}
  eval ac_env_${ac_var}_value=\$${ac_var}
  eval ac_cv_env_${ac_var}_set=\${${ac_var}+set}
  eval ac_cv_env_${ac_var}_value=\$${ac_var}
done

#
# Report the --help message.
#
if test "$ac_init_help" = "long"; then
  # Omit some internal or obsolete options to make the list less imposing.
  # This message is too long to be a string in the A/UX 3.1 sh.
  cat <<_ACEOF
\`configure' configures fltk 1.4.4 to adapt to many kinds of systems.

Usage: $0 [OPTION]... [VAR=VALUE]...

To assign environment variables (e.g., CC, CFLAGS...), specify them as
VAR=VALUE.  See below for descriptions of some of the useful variables.

Defaults for the options are specified in brackets.

Configuration:
  -h, --help              display this help and exit
      --help=short        display options specific to this package
      --help=recursive    display the short help of all the included packages
  -V, --version           display version information and exit
  -q, --quiet, --silent   do not print \`checking ...' messages
      --cache-file=FILE   cache test results in FILE [disabled]
  -C, --config-cache      alias for \`--cache-file=config.cache'
  -n, --no-create         do not create output files
      --srcdir=DIR        find the sources in DIR [configure dir or \`..']

Installation directories:
  --prefix=PREFIX         install architecture-independent files in PREFIX
                          [$ac_default_prefix]
  --exec-prefix=EPREFIX   install architecture-dependent files in EPREFIX
                          [PREFIX]

By default, \`make install' will install all the files in
\`$ac_default_prefix/bin', \`$ac_default_prefix/lib' etc.  You can specify
an installation prefix other than \`$ac_default_prefix' using \`--prefix',
for instance \`--prefix=\$HOME'.

For better control, use the options below.

Fine tuning of the installation directories:
  --bindir=DIR            user executables [EPREFIX/bin]
  --sbindir=DIR           system admin executables [EPREFIX/sbin]
  --libexecdir=DIR        program executables [EPREFIX/libexec]
  --sysconfdir=DIR        read-only single-machine data [PREFIX/etc]
  --sharedstatedir=DIR    modifiable architecture-independent data [PREFIX/com]
  --localstatedir=DIR     modifiable single-machine data [PREFIX/var]
  --runstatedir=DIR       modifiable per-process data [LOCALSTATEDIR/run]
  --libdir=DIR            object code libraries [EPREFIX/lib]
  --includedir=DIR        C header files [PREFIX/include]
  --oldincludedir=DIR     C header files for non-gcc [/usr/include]
  --datarootdir=DIR       read-only arch.-independent data root [PREFIX/share]
  --datadir=DIR           read-only architecture-independent data [DATAROOTDIR]
  --infodir=DIR           info documentation [DATAROOTDIR/info]
  --localedir=DIR         locale-dependent data [DATAROOTDIR/locale]
  --mandir=DIR            man documentation [DATAROOTDIR/man]
  --docdir=DIR            documentation root [DATAROOTDIR/doc/fltk]
  --htmldir=DIR           html documentation [DOCDIR]
  --dvidir=DIR            dvi documentation [DOCDIR]
  --pdfdir=DIR            pdf documentation [DOCDIR]
  --psdir=DIR             ps documentation [DOCDIR]
_ACEOF

  cat <<\_ACEOF

X features:
  --x-includes=DIR    X include files are in DIR
  --x-libraries=DIR   X library files are in DIR

System types:
  --build=BUILD     configure for building on BUILD [guessed]
  --host=HOST       cross-compile to build programs to run on HOST [BUILD]
_ACEOF
fi

if test -n "$ac_init_help"; then
  case $ac_init_help in
     short | recursive ) echo "Configuration of fltk 1.4.4:";;
   esac
  cat <<\_ACEOF

Optional Features:
  --disable-option-checking  ignore unrecognized --enable/--with options
  --disable-FEATURE       do not include FEATURE (same as --enable-FEATURE=no)
  --enable-FEATURE[=ARG]  include FEATURE [ARG=yes]
  --enable-cairo          add support for Fl_Cairo_Window
  --enable-cairoext       use FLTK code instrumentation for Cairo extended use
  --enable-cp936          turn on CP936
  --enable-cygwin         use the Cygwin DLL (default=no)
  --enable-debug          turn on debugging
  --disable-test          build test programs (default=yes)
  --disable-forms         build Forms compatibility library (default=yes)
  --disable-gl            turn off OpenGL support
  --enable-localjpeg      use local JPEG library (default=auto)
  --enable-localpng       use local PNG library (default=auto)
  --enable-localzlib      use local ZLIB library (default=auto)
  --enable-pango          turn on Pango support
  --disable-wayland       turn off hybrid Wayland/X11 support
  --enable-usecairo       all drawing to X11 windows uses Cairo
  --enable-use_std        allow FLTK to use std::string etc.
  --disable-print         turn off print support (X11)
  --enable-shared         turn on shared libraries
  --disable-svg           disable SVG support
  --disable-threads       turn off multi-threading support
  --enable-x11            use X11 with Cygwin or macOS (default=no)
  --disable-xcursor       turn off Xcursor support
  --disable-xfixes        turn off Xfixes support
  --disable-xft           turn off Xft support
  --disable-xinerama      turn off Xinerama support
  --disable-xrender       turn off Xrender support
  --disable-fluid         turn off fluid building
  --disable-gdiplus       don't use GDI+ for antialiased graphics
  --disable-largefile     omit support for large files

Optional Packages:
  --with-PACKAGE[=ARG]    use PACKAGE [ARG=yes]
  --without-PACKAGE       do not use PACKAGE (same as --with-PACKAGE=no)
  --with-archflags="flags"
                          use custom architecture flags (default=none, macOS
                          values include "-arch arm64", "-arch i386", "-arch
                          ppc", and "-arch x86_64")
  --with-optim="flags"    use custom optimization flags
  --with-abiversion       Build with FL_ABI_VERSION, e.g. 10304 for FLTK 1.3.4
  --with-links            make header links for common misspellings
                          (default=no)
  --with-x                use the X Window System

Some influential environment variables:
  CC          C compiler command
  CFLAGS      C compiler flags
  LDFLAGS     linker flags, e.g. -L<lib dir> if you have libraries in a
              nonstandard directory <lib dir>
  LIBS        libraries to pass to the linker, e.g. -l<library>
  CPPFLAGS    (Objective) C/C++ preprocessor flags, e.g. -I<include dir> if
              you have headers in a nonstandard directory <include dir>
  CXX         C++ compiler command
  CXXFLAGS    C++ compiler flags
  XMKMF       Path to xmkmf, Makefile generator for X Window System
  CPP         C preprocessor

Use these variables to override the choices made by `configure' or to help
it to find libraries and programs with nonstandard names/locations.

Report bugs to <https://github.com/fltk/fltk/issues>.
fltk home page: <https://www.fltk.org/>.
_ACEOF
ac_status=$?
fi

if test "$ac_init_help" = "recursive"; then
  # If there are subdirs, report their specific --help.
  for ac_dir in : $ac_subdirs_all; do test "x$ac_dir" = x: && continue
    test -d "$ac_dir" ||
      { cd "$srcdir" && ac_pwd=`pwd` && srcdir=. && test -d "$ac_dir"; } ||
      continue
    ac_builddir=.

case "$ac_dir" in
.) ac_dir_suffix= ac_top_builddir_sub=. ac_top_build_prefix= ;;
*)
  ac_dir_suffix=/`printf "%s\n" "$ac_dir" | sed 's|^\.[\\/]||'`
  # A ".." for each directory in $ac_dir_suffix.
  ac_top_builddir_sub=`printf "%s\n" "$ac_dir_suffix" | sed 's|/[^\\/]*|/..|g;s|/||'`
  case $ac_top_builddir_sub in
  "") ac_top_builddir_sub=. ac_top_build_prefix= ;;
  *)  ac_top_build_prefix=$ac_top_builddir_sub/ ;;
  esac ;;
esac
ac_abs_top_builddir=$ac_pwd
ac_abs_builddir=$ac_pwd$ac_dir_suffix
# for backward compatibility:
ac_top_builddir=$ac_top_build_prefix

case $srcdir in
  .)  # We are building in place.
    ac_srcdir=.
    ac_top_srcdir=$ac_top_builddir_sub
    ac_abs_top_srcdir=$ac_pwd ;;
  [\\/]* | ?:[\\/]* )  # Absolute name.
    ac_srcdir=$srcdir$ac_dir_suffix;
    ac_top_srcdir=$srcdir
    ac_abs_top_srcdir=$srcdir ;;
  *) # Relative name.
    ac_srcdir=$ac_top_build_prefix$srcdir$ac_dir_suffix
    ac_top_srcdir=$ac_top_build_prefix$srcdir
    ac_abs_top_srcdir=$ac_pwd/$srcdir ;;
esac
ac_abs_srcdir=$ac_abs_top_srcdir$ac_dir_suffix

    cd "$ac_dir" || { ac_status=$?; continue; }
    # Check for configure.gnu first; this name is used for a wrapper for
    # Metaconfig's "Configure" on case-insensitive file systems.
    if test -f "$ac_srcdir/configure.gnu"; then
      echo &&
      $SHELL "$ac_srcdir/configure.gnu" --help=recursive
    elif test -f "$ac_srcdir/configure"; then
      echo &&
      $SHELL "$ac_srcdir/configure" --help=recursive
    else
      printf "%s\n" "$as_me: WARNING: no configuration information is in $ac_dir" >&2
    fi || ac_status=$?
    cd "$ac_pwd" || { ac_status=$?; break; }
  done
fi

test -n "$ac_init_help" && exit $ac_status
if $ac_init_version; then
  cat <<\_ACEOF
fltk configure 1.4.4
generated by GNU Autoconf 2.71

Copyright (C) 2021 Free Software Foundation, Inc.
This configure script is free software; the Free Software Foundation
gives unlimited permission to copy, distribute and modify it.
_ACEOF
  exit
fi

## ------------------------ ##
## Autoconf initialization. ##
## ------------------------ ##

# ac_fn_c_try_compile LINENO
# --------------------------
# Try to compile conftest.$ac_ext, and return whether this succeeded.
ac_fn_c_try_compile ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  rm -f conftest.$ac_objext conftest.beam
  if { { ac_try="$ac_compile"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_compile") 2>conftest.err
  ac_status=$?
  if test -s conftest.err; then
    grep -v '^ *+' conftest.err >conftest.er1
    cat conftest.er1 >&5
    mv -f conftest.er1 conftest.err
  fi
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; } && {
	 test -z "$ac_c_werror_flag" ||
	 test ! -s conftest.err
       } && test -s conftest.$ac_objext
then :
  ac_retval=0
else $as_nop
  printf "%s\n" "$as_me: failed program was:" >&5
sed 's/^/| /' conftest.$ac_ext >&5

	ac_retval=1
fi
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno
  as_fn_set_status $ac_retval

} # ac_fn_c_try_compile

# ac_fn_cxx_try_compile LINENO
# ----------------------------
# Try to compile conftest.$ac_ext, and return whether this succeeded.
ac_fn_cxx_try_compile ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  rm -f conftest.$ac_objext conftest.beam
  if { { ac_try="$ac_compile"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_compile") 2>conftest.err
  ac_status=$?
  if test -s conftest.err; then
    grep -v '^ *+' conftest.err >conftest.er1
    cat conftest.er1 >&5
    mv -f conftest.er1 conftest.err
  fi
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; } && {
	 test -z "$ac_cxx_werror_flag" ||
	 test ! -s conftest.err
       } && test -s conftest.$ac_objext
then :
  ac_retval=0
else $as_nop
  printf "%s\n" "$as_me: failed program was:" >&5
sed 's/^/| /' conftest.$ac_ext >&5

	ac_retval=1
fi
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno
  as_fn_set_status $ac_retval

} # ac_fn_cxx_try_compile

# ac_fn_c_check_header_compile LINENO HEADER VAR INCLUDES
# -------------------------------------------------------
# Tests whether HEADER exists and can be compiled using the include files in
# INCLUDES, setting the cache variable VAR accordingly.
ac_fn_c_check_header_compile ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $2" >&5
printf %s "checking for $2... " >&6; }
if eval test \${$3+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
#include <$2>
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  eval "$3=yes"
else $as_nop
  eval "$3=no"
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
eval ac_res=\$$3
	       { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_res" >&5
printf "%s\n" "$ac_res" >&6; }
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno

} # ac_fn_c_check_header_compile

# ac_fn_c_try_run LINENO
# ----------------------
# Try to run conftest.$ac_ext, and return whether this succeeded. Assumes that
# executables *can* be run.
ac_fn_c_try_run ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  if { { ac_try="$ac_link"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_link") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; } && { ac_try='./conftest$ac_exeext'
  { { case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_try") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }; }
then :
  ac_retval=0
else $as_nop
  printf "%s\n" "$as_me: program exited with status $ac_status" >&5
       printf "%s\n" "$as_me: failed program was:" >&5
sed 's/^/| /' conftest.$ac_ext >&5

       ac_retval=$ac_status
fi
  rm -rf conftest.dSYM conftest_ipa8_conftest.oo
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno
  as_fn_set_status $ac_retval

} # ac_fn_c_try_run

# ac_fn_c_compute_int LINENO EXPR VAR INCLUDES
# --------------------------------------------
# Tries to find the compile-time value of EXPR in a program that includes
# INCLUDES, setting VAR accordingly. Returns whether the value could be
# computed
ac_fn_c_compute_int ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  if test "$cross_compiling" = yes; then
    # Depending upon the size, compute the lo and hi bounds.
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
static int test_array [1 - 2 * !(($2) >= 0)];
test_array [0] = 0;
return test_array [0];

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_lo=0 ac_mid=0
  while :; do
    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
static int test_array [1 - 2 * !(($2) <= $ac_mid)];
test_array [0] = 0;
return test_array [0];

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_hi=$ac_mid; break
else $as_nop
  as_fn_arith $ac_mid + 1 && ac_lo=$as_val
			if test $ac_lo -le $ac_mid; then
			  ac_lo= ac_hi=
			  break
			fi
			as_fn_arith 2 '*' $ac_mid + 1 && ac_mid=$as_val
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
  done
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
static int test_array [1 - 2 * !(($2) < 0)];
test_array [0] = 0;
return test_array [0];

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_hi=-1 ac_mid=-1
  while :; do
    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
static int test_array [1 - 2 * !(($2) >= $ac_mid)];
test_array [0] = 0;
return test_array [0];

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_lo=$ac_mid; break
else $as_nop
  as_fn_arith '(' $ac_mid ')' - 1 && ac_hi=$as_val
			if test $ac_mid -le $ac_hi; then
			  ac_lo= ac_hi=
			  break
			fi
			as_fn_arith 2 '*' $ac_mid && ac_mid=$as_val
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
  done
else $as_nop
  ac_lo= ac_hi=
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
# Binary search between lo and hi bounds.
while test "x$ac_lo" != "x$ac_hi"; do
  as_fn_arith '(' $ac_hi - $ac_lo ')' / 2 + $ac_lo && ac_mid=$as_val
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
int
main (void)
{
static int test_array [1 - 2 * !(($2) <= $ac_mid)];
test_array [0] = 0;
return test_array [0];

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_hi=$ac_mid
else $as_nop
  as_fn_arith '(' $ac_mid ')' + 1 && ac_lo=$as_val
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
done
case $ac_lo in #((
?*) eval "$3=\$ac_lo"; ac_retval=0 ;;
'') ac_retval=1 ;;
esac
  else
    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$4
static long int longval (void) { return $2; }
static unsigned long int ulongval (void) { return $2; }
#include <stdio.h>
#include <stdlib.h>
int
main (void)
{

  FILE *f = fopen ("conftest.val", "w");
  if (! f)
    return 1;
  if (($2) < 0)
    {
      long int i = longval ();
      if (i != ($2))
	return 1;
      fprintf (f, "%ld", i);
    }
  else
    {
      unsigned long int i = ulongval ();
      if (i != ($2))
	return 1;
      fprintf (f, "%lu", i);
    }
  /* Do not output a trailing newline, as this causes \r\n confusion
     on some platforms.  */
  return ferror (f) || fclose (f) != 0;

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_run "$LINENO"
then :
  echo >>conftest.val; read $3 <conftest.val; ac_retval=0
else $as_nop
  ac_retval=1
fi
rm -f core *.core core.conftest.* gmon.out bb.out conftest$ac_exeext \
  conftest.$ac_objext conftest.beam conftest.$ac_ext
rm -f conftest.val

  fi
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno
  as_fn_set_status $ac_retval

} # ac_fn_c_compute_int

# ac_fn_c_try_link LINENO
# -----------------------
# Try to link conftest.$ac_ext, and return whether this succeeded.
ac_fn_c_try_link ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  rm -f conftest.$ac_objext conftest.beam conftest$ac_exeext
  if { { ac_try="$ac_link"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_link") 2>conftest.err
  ac_status=$?
  if test -s conftest.err; then
    grep -v '^ *+' conftest.err >conftest.er1
    cat conftest.er1 >&5
    mv -f conftest.er1 conftest.err
  fi
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; } && {
	 test -z "$ac_c_werror_flag" ||
	 test ! -s conftest.err
       } && test -s conftest$ac_exeext && {
	 test "$cross_compiling" = yes ||
	 test -x conftest$ac_exeext
       }
then :
  ac_retval=0
else $as_nop
  printf "%s\n" "$as_me: failed program was:" >&5
sed 's/^/| /' conftest.$ac_ext >&5

	ac_retval=1
fi
  # Delete the IPA/IPO (Inter Procedural Analysis/Optimization) information
  # created by the PGI compiler (conftest_ipa8_conftest.oo), as it would
  # interfere with the next link command; also delete a directory that is
  # left behind by Apple's compiler.  We do this before executing the actions.
  rm -rf conftest.dSYM conftest_ipa8_conftest.oo
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno
  as_fn_set_status $ac_retval

} # ac_fn_c_try_link

# ac_fn_c_check_func LINENO FUNC VAR
# ----------------------------------
# Tests whether FUNC exists, setting the cache variable VAR accordingly
ac_fn_c_check_func ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $2" >&5
printf %s "checking for $2... " >&6; }
if eval test \${$3+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
/* Define $2 to an innocuous variant, in case <limits.h> declares $2.
   For example, HP-UX 11i <limits.h> declares gettimeofday.  */
#define $2 innocuous_$2

/* System header to define __stub macros and hopefully few prototypes,
   which can conflict with char $2 (); below.  */

#include <limits.h>
#undef $2

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
#ifdef __cplusplus
extern "C"
#endif
char $2 ();
/* The GNU C library defines this for functions which it implements
    to always fail with ENOSYS.  Some functions are actually named
    something starting with __ and the normal name is an alias.  */
#if defined __stub_$2 || defined __stub___$2
choke me
#endif

int
main (void)
{
return $2 ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  eval "$3=yes"
else $as_nop
  eval "$3=no"
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
fi
eval ac_res=\$$3
	       { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_res" >&5
printf "%s\n" "$ac_res" >&6; }
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno

} # ac_fn_c_check_func

# ac_fn_c_try_cpp LINENO
# ----------------------
# Try to preprocess conftest.$ac_ext, and return whether this succeeded.
ac_fn_c_try_cpp ()
{
  as_lineno=${as_lineno-"$1"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
  if { { ac_try="$ac_cpp conftest.$ac_ext"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_cpp conftest.$ac_ext") 2>conftest.err
  ac_status=$?
  if test -s conftest.err; then
    grep -v '^ *+' conftest.err >conftest.er1
    cat conftest.er1 >&5
    mv -f conftest.er1 conftest.err
  fi
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; } > conftest.i && {
	 test -z "$ac_c_preproc_warn_flag$ac_c_werror_flag" ||
	 test ! -s conftest.err
       }
then :
  ac_retval=0
else $as_nop
  printf "%s\n" "$as_me: failed program was:" >&5
sed 's/^/| /' conftest.$ac_ext >&5

    ac_retval=1
fi
  eval $as_lineno_stack; ${as_lineno_stack:+:} unset as_lineno
  as_fn_set_status $ac_retval

} # ac_fn_c_try_cpp
ac_configure_args_raw=
for ac_arg
do
  case $ac_arg in
  *\'*)
    ac_arg=`printf "%s\n" "$ac_arg" | sed "s/'/'\\\\\\\\''/g"` ;;
  esac
  as_fn_append ac_configure_args_raw " '$ac_arg'"
done

case $ac_configure_args_raw in
  *$as_nl*)
    ac_safe_unquote= ;;
  *)
    ac_unsafe_z='|&;<>()$`\\"*?[ ''	' # This string ends in space, tab.
    ac_unsafe_a="$ac_unsafe_z#~"
    ac_safe_unquote="s/ '\\([^$ac_unsafe_a][^$ac_unsafe_z]*\\)'/ \\1/g"
    ac_configure_args_raw=`      printf "%s\n" "$ac_configure_args_raw" | sed "$ac_safe_unquote"`;;
esac

cat >config.log <<_ACEOF
This file contains any messages produced by compilers while
running configure, to aid debugging if configure makes a mistake.

It was created by fltk $as_me 1.4.4, which was
generated by GNU Autoconf 2.71.  Invocation command line was

  $ $0$ac_configure_args_raw

_ACEOF
exec 5>>config.log
{
cat <<_ASUNAME
## --------- ##
## Platform. ##
## --------- ##

hostname = `(hostname || uname -n) 2>/dev/null | sed 1q`
uname -m = `(uname -m) 2>/dev/null || echo unknown`
uname -r = `(uname -r) 2>/dev/null || echo unknown`
uname -s = `(uname -s) 2>/dev/null || echo unknown`
uname -v = `(uname -v) 2>/dev/null || echo unknown`

/usr/bin/uname -p = `(/usr/bin/uname -p) 2>/dev/null || echo unknown`
/bin/uname -X     = `(/bin/uname -X) 2>/dev/null     || echo unknown`

/bin/arch              = `(/bin/arch) 2>/dev/null              || echo unknown`
/usr/bin/arch -k       = `(/usr/bin/arch -k) 2>/dev/null       || echo unknown`
/usr/convex/getsysinfo = `(/usr/convex/getsysinfo) 2>/dev/null || echo unknown`
/usr/bin/hostinfo      = `(/usr/bin/hostinfo) 2>/dev/null      || echo unknown`
/bin/machine           = `(/bin/machine) 2>/dev/null           || echo unknown`
/usr/bin/oslevel       = `(/usr/bin/oslevel) 2>/dev/null       || echo unknown`
/bin/universe          = `(/bin/universe) 2>/dev/null          || echo unknown`

_ASUNAME

as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    printf "%s\n" "PATH: $as_dir"
  done
IFS=$as_save_IFS

} >&5

cat >&5 <<_ACEOF


## ----------- ##
## Core tests. ##
## ----------- ##

_ACEOF


# Keep a trace of the command line.
# Strip out --no-create and --no-recursion so they do not pile up.
# Strip out --silent because we don't want to record it for future runs.
# Also quote any args containing shell meta-characters.
# Make two passes to allow for proper duplicate-argument suppression.
ac_configure_args=
ac_configure_args0=
ac_configure_args1=
ac_must_keep_next=false
for ac_pass in 1 2
do
  for ac_arg
  do
    case $ac_arg in
    -no-create | --no-c* | -n | -no-recursion | --no-r*) continue ;;
    -q | -quiet | --quiet | --quie | --qui | --qu | --q \
    | -silent | --silent | --silen | --sile | --sil)
      continue ;;
    *\'*)
      ac_arg=`printf "%s\n" "$ac_arg" | sed "s/'/'\\\\\\\\''/g"` ;;
    esac
    case $ac_pass in
    1) as_fn_append ac_configure_args0 " '$ac_arg'" ;;
    2)
      as_fn_append ac_configure_args1 " '$ac_arg'"
      if test $ac_must_keep_next = true; then
	ac_must_keep_next=false # Got value, back to normal.
      else
	case $ac_arg in
	  *=* | --config-cache | -C | -disable-* | --disable-* \
	  | -enable-* | --enable-* | -gas | --g* | -nfp | --nf* \
	  | -q | -quiet | --q* | -silent | --sil* | -v | -verb* \
	  | -with-* | --with-* | -without-* | --without-* | --x)
	    case "$ac_configure_args0 " in
	      "$ac_configure_args1"*" '$ac_arg' "* ) continue ;;
	    esac
	    ;;
	  -* ) ac_must_keep_next=true ;;
	esac
      fi
      as_fn_append ac_configure_args " '$ac_arg'"
      ;;
    esac
  done
done
{ ac_configure_args0=; unset ac_configure_args0;}
{ ac_configure_args1=; unset ac_configure_args1;}

# When interrupted or exit'd, cleanup temporary files, and complete
# config.log.  We remove comments because anyway the quotes in there
# would cause problems or look ugly.
# WARNING: Use '\'' to represent an apostrophe within the trap.
# WARNING: Do not start the trap code with a newline, due to a FreeBSD 4.0 bug.
trap 'exit_status=$?
  # Sanitize IFS.
  IFS=" ""	$as_nl"
  # Save into config.log some information that might help in debugging.
  {
    echo

    printf "%s\n" "## ---------------- ##
## Cache variables. ##
## ---------------- ##"
    echo
    # The following way of writing the cache mishandles newlines in values,
(
  for ac_var in `(set) 2>&1 | sed -n '\''s/^\([a-zA-Z_][a-zA-Z0-9_]*\)=.*/\1/p'\''`; do
    eval ac_val=\$$ac_var
    case $ac_val in #(
    *${as_nl}*)
      case $ac_var in #(
      *_cv_*) { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: cache variable $ac_var contains a newline" >&5
printf "%s\n" "$as_me: WARNING: cache variable $ac_var contains a newline" >&2;} ;;
      esac
      case $ac_var in #(
      _ | IFS | as_nl) ;; #(
      BASH_ARGV | BASH_SOURCE) eval $ac_var= ;; #(
      *) { eval $ac_var=; unset $ac_var;} ;;
      esac ;;
    esac
  done
  (set) 2>&1 |
    case $as_nl`(ac_space='\'' '\''; set) 2>&1` in #(
    *${as_nl}ac_space=\ *)
      sed -n \
	"s/'\''/'\''\\\\'\'''\''/g;
	  s/^\\([_$as_cr_alnum]*_cv_[_$as_cr_alnum]*\\)=\\(.*\\)/\\1='\''\\2'\''/p"
      ;; #(
    *)
      sed -n "/^[_$as_cr_alnum]*_cv_[_$as_cr_alnum]*=/p"
      ;;
    esac |
    sort
)
    echo

    printf "%s\n" "## ----------------- ##
## Output variables. ##
## ----------------- ##"
    echo
    for ac_var in $ac_subst_vars
    do
      eval ac_val=\$$ac_var
      case $ac_val in
      *\'\''*) ac_val=`printf "%s\n" "$ac_val" | sed "s/'\''/'\''\\\\\\\\'\'''\''/g"`;;
      esac
      printf "%s\n" "$ac_var='\''$ac_val'\''"
    done | sort
    echo

    if test -n "$ac_subst_files"; then
      printf "%s\n" "## ------------------- ##
## File substitutions. ##
## ------------------- ##"
      echo
      for ac_var in $ac_subst_files
      do
	eval ac_val=\$$ac_var
	case $ac_val in
	*\'\''*) ac_val=`printf "%s\n" "$ac_val" | sed "s/'\''/'\''\\\\\\\\'\'''\''/g"`;;
	esac
	printf "%s\n" "$ac_var='\''$ac_val'\''"
      done | sort
      echo
    fi

    if test -s confdefs.h; then
      printf "%s\n" "## ----------- ##
## confdefs.h. ##
## ----------- ##"
      echo
      cat confdefs.h
      echo
    fi
    test "$ac_signal" != 0 &&
      printf "%s\n" "$as_me: caught signal $ac_signal"
    printf "%s\n" "$as_me: exit $exit_status"
  } >&5
  rm -f core *.core core.conftest.* &&
    rm -f -r conftest* confdefs* conf$$* $ac_clean_files &&
    exit $exit_status
' 0
for ac_signal in 1 2 13 15; do
  trap 'ac_signal='$ac_signal'; as_fn_exit 1' $ac_signal
done
ac_signal=0

# confdefs.h avoids OS command line length limits that DEFS can exceed.
rm -f -r conftest* confdefs.h

printf "%s\n" "/* confdefs.h */" > confdefs.h

# Predefined preprocessor variables.

printf "%s\n" "#define PACKAGE_NAME \"$PACKAGE_NAME\"" >>confdefs.h

printf "%s\n" "#define PACKAGE_TARNAME \"$PACKAGE_TARNAME\"" >>confdefs.h

printf "%s\n" "#define PACKAGE_VERSION \"$PACKAGE_VERSION\"" >>confdefs.h

printf "%s\n" "#define PACKAGE_STRING \"$PACKAGE_STRING\"" >>confdefs.h

printf "%s\n" "#define PACKAGE_BUGREPORT \"$PACKAGE_BUGREPORT\"" >>confdefs.h

printf "%s\n" "#define PACKAGE_URL \"$PACKAGE_URL\"" >>confdefs.h


# Let the site file select an alternate cache file if it wants to.
# Prefer an explicitly selected file to automatically selected ones.
if test -n "$CONFIG_SITE"; then
  ac_site_files="$CONFIG_SITE"
elif test "x$prefix" != xNONE; then
  ac_site_files="$prefix/share/config.site $prefix/etc/config.site"
else
  ac_site_files="$ac_default_prefix/share/config.site $ac_default_prefix/etc/config.site"
fi

for ac_site_file in $ac_site_files
do
  case $ac_site_file in #(
  */*) :
     ;; #(
  *) :
    ac_site_file=./$ac_site_file ;;
esac
  if test -f "$ac_site_file" && test -r "$ac_site_file"; then
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: loading site script $ac_site_file" >&5
printf "%s\n" "$as_me: loading site script $ac_site_file" >&6;}
    sed 's/^/| /' "$ac_site_file" >&5
    . "$ac_site_file" \
      || { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error $? "failed to load site script $ac_site_file
See \`config.log' for more details" "$LINENO" 5; }
  fi
done

if test -r "$cache_file"; then
  # Some versions of bash will fail to source /dev/null (special files
  # actually), so we avoid doing that.  DJGPP emulates it as a regular file.
  if test /dev/null != "$cache_file" && test -f "$cache_file"; then
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: loading cache $cache_file" >&5
printf "%s\n" "$as_me: loading cache $cache_file" >&6;}
    case $cache_file in
      [\\/]* | ?:[\\/]* ) . "$cache_file";;
      *)                      . "./$cache_file";;
    esac
  fi
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: creating cache $cache_file" >&5
printf "%s\n" "$as_me: creating cache $cache_file" >&6;}
  >$cache_file
fi

# Test code for whether the C compiler supports C89 (global declarations)
ac_c_conftest_c89_globals='
/* Does the compiler advertise C89 conformance?
   Do not test the value of __STDC__, because some compilers set it to 0
   while being otherwise adequately conformant. */
#if !defined __STDC__
# error "Compiler does not advertise C89 conformance"
#endif

#include <stddef.h>
#include <stdarg.h>
struct stat;
/* Most of the following tests are stolen from RCS 5.7 src/conf.sh.  */
struct buf { int x; };
struct buf * (*rcsopen) (struct buf *, struct stat *, int);
static char *e (p, i)
     char **p;
     int i;
{
  return p[i];
}
static char *f (char * (*g) (char **, int), char **p, ...)
{
  char *s;
  va_list v;
  va_start (v,p);
  s = g (p, va_arg (v,int));
  va_end (v);
  return s;
}

/* OSF 4.0 Compaq cc is some sort of almost-ANSI by default.  It has
   function prototypes and stuff, but not \xHH hex character constants.
   These do not provoke an error unfortunately, instead are silently treated
   as an "x".  The following induces an error, until -std is added to get
   proper ANSI mode.  Curiously \x00 != x always comes out true, for an
   array size at least.  It is necessary to write \x00 == 0 to get something
   that is true only with -std.  */
int osf4_cc_array ['\''\x00'\'' == 0 ? 1 : -1];

/* IBM C 6 for AIX is almost-ANSI by default, but it replaces macro parameters
   inside strings and character constants.  */
#define FOO(x) '\''x'\''
int xlc6_cc_array[FOO(a) == '\''x'\'' ? 1 : -1];

int test (int i, double x);
struct s1 {int (*f) (int a);};
struct s2 {int (*f) (double a);};
int pairnames (int, char **, int *(*)(struct buf *, struct stat *, int),
               int, int);'

# Test code for whether the C compiler supports C89 (body of main).
ac_c_conftest_c89_main='
ok |= (argc == 0 || f (e, argv, 0) != argv[0] || f (e, argv, 1) != argv[1]);
'

# Test code for whether the C compiler supports C99 (global declarations)
ac_c_conftest_c99_globals='
// Does the compiler advertise C99 conformance?
#if !defined __STDC_VERSION__ || __STDC_VERSION__ < 199901L
# error "Compiler does not advertise C99 conformance"
#endif

#include <stdbool.h>
extern int puts (const char *);
extern int printf (const char *, ...);
extern int dprintf (int, const char *, ...);
extern void *malloc (size_t);

// Check varargs macros.  These examples are taken from C99 6.10.3.5.
// dprintf is used instead of fprintf to avoid needing to declare
// FILE and stderr.
#define debug(...) dprintf (2, __VA_ARGS__)
#define showlist(...) puts (#__VA_ARGS__)
#define report(test,...) ((test) ? puts (#test) : printf (__VA_ARGS__))
static void
test_varargs_macros (void)
{
  int x = 1234;
  int y = 5678;
  debug ("Flag");
  debug ("X = %d\n", x);
  showlist (The first, second, and third items.);
  report (x>y, "x is %d but y is %d", x, y);
}

// Check long long types.
#define BIG64 18446744073709551615ull
#define BIG32 4294967295ul
#define BIG_OK (BIG64 / BIG32 == 4294967297ull && BIG64 % BIG32 == 0)
#if !BIG_OK
  #error "your preprocessor is broken"
#endif
#if BIG_OK
#else
  #error "your preprocessor is broken"
#endif
static long long int bignum = -9223372036854775807LL;
static unsigned long long int ubignum = BIG64;

struct incomplete_array
{
  int datasize;
  double data[];
};

struct named_init {
  int number;
  const wchar_t *name;
  double average;
};

typedef const char *ccp;

static inline int
test_restrict (ccp restrict text)
{
  // See if C++-style comments work.
  // Iterate through items via the restricted pointer.
  // Also check for declarations in for loops.
  for (unsigned int i = 0; *(text+i) != '\''\0'\''; ++i)
    continue;
  return 0;
}

// Check varargs and va_copy.
static bool
test_varargs (const char *format, ...)
{
  va_list args;
  va_start (args, format);
  va_list args_copy;
  va_copy (args_copy, args);

  const char *str = "";
  int number = 0;
  float fnumber = 0;

  while (*format)
    {
      switch (*format++)
	{
	case '\''s'\'': // string
	  str = va_arg (args_copy, const char *);
	  break;
	case '\''d'\'': // int
	  number = va_arg (args_copy, int);
	  break;
	case '\''f'\'': // float
	  fnumber = va_arg (args_copy, double);
	  break;
	default:
	  break;
	}
    }
  va_end (args_copy);
  va_end (args);

  return *str && number && fnumber;
}
'

# Test code for whether the C compiler supports C99 (body of main).
ac_c_conftest_c99_main='
  // Check bool.
  _Bool success = false;
  success |= (argc != 0);

  // Check restrict.
  if (test_restrict ("String literal") == 0)
    success = true;
  char *restrict newvar = "Another string";

  // Check varargs.
  success &= test_varargs ("s, d'\'' f .", "string", 65, 34.234);
  test_varargs_macros ();

  // Check flexible array members.
  struct incomplete_array *ia =
    malloc (sizeof (struct incomplete_array) + (sizeof (double) * 10));
  ia->datasize = 10;
  for (int i = 0; i < ia->datasize; ++i)
    ia->data[i] = i * 1.234;

  // Check named initializers.
  struct named_init ni = {
    .number = 34,
    .name = L"Test wide string",
    .average = 543.34343,
  };

  ni.number = 58;

  int dynamic_array[ni.number];
  dynamic_array[0] = argv[0][0];
  dynamic_array[ni.number - 1] = 543;

  // work around unused variable warnings
  ok |= (!success || bignum == 0LL || ubignum == 0uLL || newvar[0] == '\''x'\''
	 || dynamic_array[ni.number - 1] != 543);
'

# Test code for whether the C compiler supports C11 (global declarations)
ac_c_conftest_c11_globals='
// Does the compiler advertise C11 conformance?
#if !defined __STDC_VERSION__ || __STDC_VERSION__ < 201112L
# error "Compiler does not advertise C11 conformance"
#endif

// Check _Alignas.
char _Alignas (double) aligned_as_double;
char _Alignas (0) no_special_alignment;
extern char aligned_as_int;
char _Alignas (0) _Alignas (int) aligned_as_int;

// Check _Alignof.
enum
{
  int_alignment = _Alignof (int),
  int_array_alignment = _Alignof (int[100]),
  char_alignment = _Alignof (char)
};
_Static_assert (0 < -_Alignof (int), "_Alignof is signed");

// Check _Noreturn.
int _Noreturn does_not_return (void) { for (;;) continue; }

// Check _Static_assert.
struct test_static_assert
{
  int x;
  _Static_assert (sizeof (int) <= sizeof (long int),
                  "_Static_assert does not work in struct");
  long int y;
};

// Check UTF-8 literals.
#define u8 syntax error!
char const utf8_literal[] = u8"happens to be ASCII" "another string";

// Check duplicate typedefs.
typedef long *long_ptr;
typedef long int *long_ptr;
typedef long_ptr long_ptr;

// Anonymous structures and unions -- taken from C11 6.7.2.1 Example 1.
struct anonymous
{
  union {
    struct { int i; int j; };
    struct { int k; long int l; } w;
  };
  int m;
} v1;
'

# Test code for whether the C compiler supports C11 (body of main).
ac_c_conftest_c11_main='
  _Static_assert ((offsetof (struct anonymous, i)
		   == offsetof (struct anonymous, w.k)),
		  "Anonymous union alignment botch");
  v1.i = 2;
  v1.w.k = 5;
  ok |= v1.i != 5;
'

# Test code for whether the C compiler supports C11 (complete).
ac_c_conftest_c11_program="${ac_c_conftest_c89_globals}
${ac_c_conftest_c99_globals}
${ac_c_conftest_c11_globals}

int
main (int argc, char **argv)
{
  int ok = 0;
  ${ac_c_conftest_c89_main}
  ${ac_c_conftest_c99_main}
  ${ac_c_conftest_c11_main}
  return ok;
}
"

# Test code for whether the C compiler supports C99 (complete).
ac_c_conftest_c99_program="${ac_c_conftest_c89_globals}
${ac_c_conftest_c99_globals}

int
main (int argc, char **argv)
{
  int ok = 0;
  ${ac_c_conftest_c89_main}
  ${ac_c_conftest_c99_main}
  return ok;
}
"

# Test code for whether the C compiler supports C89 (complete).
ac_c_conftest_c89_program="${ac_c_conftest_c89_globals}

int
main (int argc, char **argv)
{
  int ok = 0;
  ${ac_c_conftest_c89_main}
  return ok;
}
"

# Test code for whether the C++ compiler supports C++98 (global declarations)
ac_cxx_conftest_cxx98_globals='
// Does the compiler advertise C++98 conformance?
#if !defined __cplusplus || __cplusplus < 199711L
# error "Compiler does not advertise C++98 conformance"
#endif

// These inclusions are to reject old compilers that
// lack the unsuffixed header files.
#include <cstdlib>
#include <exception>

// <cassert> and <cstring> are *not* freestanding headers in C++98.
extern void assert (int);
namespace std {
  extern int strcmp (const char *, const char *);
}

// Namespaces, exceptions, and templates were all added after "C++ 2.0".
using std::exception;
using std::strcmp;

namespace {

void test_exception_syntax()
{
  try {
    throw "test";
  } catch (const char *s) {
    // Extra parentheses suppress a warning when building autoconf itself,
    // due to lint rules shared with more typical C programs.
    assert (!(strcmp) (s, "test"));
  }
}

template <typename T> struct test_template
{
  T const val;
  explicit test_template(T t) : val(t) {}
  template <typename U> T add(U u) { return static_cast<T>(u) + val; }
};

} // anonymous namespace
'

# Test code for whether the C++ compiler supports C++98 (body of main)
ac_cxx_conftest_cxx98_main='
  assert (argc);
  assert (! argv[0]);
{
  test_exception_syntax ();
  test_template<double> tt (2.0);
  assert (tt.add (4) == 6.0);
  assert (true && !false);
}
'

# Test code for whether the C++ compiler supports C++11 (global declarations)
ac_cxx_conftest_cxx11_globals='
// Does the compiler advertise C++ 2011 conformance?
#if !defined __cplusplus || __cplusplus < 201103L
# error "Compiler does not advertise C++11 conformance"
#endif

namespace cxx11test
{
  constexpr int get_val() { return 20; }

  struct testinit
  {
    int i;
    double d;
  };

  class delegate
  {
  public:
    delegate(int n) : n(n) {}
    delegate(): delegate(2354) {}

    virtual int getval() { return this->n; };
  protected:
    int n;
  };

  class overridden : public delegate
  {
  public:
    overridden(int n): delegate(n) {}
    virtual int getval() override final { return this->n * 2; }
  };

  class nocopy
  {
  public:
    nocopy(int i): i(i) {}
    nocopy() = default;
    nocopy(const nocopy&) = delete;
    nocopy & operator=(const nocopy&) = delete;
  private:
    int i;
  };

  // for testing lambda expressions
  template <typename Ret, typename Fn> Ret eval(Fn f, Ret v)
  {
    return f(v);
  }

  // for testing variadic templates and trailing return types
  template <typename V> auto sum(V first) -> V
  {
    return first;
  }
  template <typename V, typename... Args> auto sum(V first, Args... rest) -> V
  {
    return first + sum(rest...);
  }
}
'

# Test code for whether the C++ compiler supports C++11 (body of main)
ac_cxx_conftest_cxx11_main='
{
  // Test auto and decltype
  auto a1 = 6538;
  auto a2 = 48573953.4;
  auto a3 = "String literal";

  int total = 0;
  for (auto i = a3; *i; ++i) { total += *i; }

  decltype(a2) a4 = 34895.034;
}
{
  // Test constexpr
  short sa[cxx11test::get_val()] = { 0 };
}
{
  // Test initializer lists
  cxx11test::testinit il = { 4323, 435234.23544 };
}
{
  // Test range-based for
  int array[] = {9, 7, 13, 15, 4, 18, 12, 10, 5, 3,
                 14, 19, 17, 8, 6, 20, 16, 2, 11, 1};
  for (auto &x : array) { x += 23; }
}
{
  // Test lambda expressions
  using cxx11test::eval;
  assert (eval ([](int x) { return x*2; }, 21) == 42);
  double d = 2.0;
  assert (eval ([&](double x) { return d += x; }, 3.0) == 5.0);
  assert (d == 5.0);
  assert (eval ([=](double x) mutable { return d += x; }, 4.0) == 9.0);
  assert (d == 5.0);
}
{
  // Test use of variadic templates
  using cxx11test::sum;
  auto a = sum(1);
  auto b = sum(1, 2);
  auto c = sum(1.0, 2.0, 3.0);
}
{
  // Test constructor delegation
  cxx11test::delegate d1;
  cxx11test::delegate d2();
  cxx11test::delegate d3(45);
}
{
  // Test override and final
  cxx11test::overridden o1(55464);
}
{
  // Test nullptr
  char *c = nullptr;
}
{
  // Test template brackets
  test_template<::test_template<int>> v(test_template<int>(12));
}
{
  // Unicode literals
  char const *utf8 = u8"UTF-8 string \u2500";
  char16_t const *utf16 = u"UTF-8 string \u2500";
  char32_t const *utf32 = U"UTF-32 string \u2500";
}
'

# Test code for whether the C compiler supports C++11 (complete).
ac_cxx_conftest_cxx11_program="${ac_cxx_conftest_cxx98_globals}
${ac_cxx_conftest_cxx11_globals}

int
main (int argc, char **argv)
{
  int ok = 0;
  ${ac_cxx_conftest_cxx98_main}
  ${ac_cxx_conftest_cxx11_main}
  return ok;
}
"

# Test code for whether the C compiler supports C++98 (complete).
ac_cxx_conftest_cxx98_program="${ac_cxx_conftest_cxx98_globals}
int
main (int argc, char **argv)
{
  int ok = 0;
  ${ac_cxx_conftest_cxx98_main}
  return ok;
}
"

as_fn_append ac_header_c_list " stdio.h stdio_h HAVE_STDIO_H"
as_fn_append ac_header_c_list " stdlib.h stdlib_h HAVE_STDLIB_H"
as_fn_append ac_header_c_list " string.h string_h HAVE_STRING_H"
as_fn_append ac_header_c_list " inttypes.h inttypes_h HAVE_INTTYPES_H"
as_fn_append ac_header_c_list " stdint.h stdint_h HAVE_STDINT_H"
as_fn_append ac_header_c_list " strings.h strings_h HAVE_STRINGS_H"
as_fn_append ac_header_c_list " sys/stat.h sys_stat_h HAVE_SYS_STAT_H"
as_fn_append ac_header_c_list " sys/types.h sys_types_h HAVE_SYS_TYPES_H"
as_fn_append ac_header_c_list " unistd.h unistd_h HAVE_UNISTD_H"

# Auxiliary files required by this configure script.
ac_aux_files="install-sh config.guess config.sub"

# Locations in which to look for auxiliary files.
ac_aux_dir_candidates="${srcdir}${PATH_SEPARATOR}${srcdir}/..${PATH_SEPARATOR}${srcdir}/../.."

# Search for a directory containing all of the required auxiliary files,
# $ac_aux_files, from the $PATH-style list $ac_aux_dir_candidates.
# If we don't find one directory that contains all the files we need,
# we report the set of missing files from the *first* directory in
# $ac_aux_dir_candidates and give up.
ac_missing_aux_files=""
ac_first_candidate=:
printf "%s\n" "$as_me:${as_lineno-$LINENO}: looking for aux files: $ac_aux_files" >&5
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
as_found=false
for as_dir in $ac_aux_dir_candidates
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
  as_found=:

  printf "%s\n" "$as_me:${as_lineno-$LINENO}:  trying $as_dir" >&5
  ac_aux_dir_found=yes
  ac_install_sh=
  for ac_aux in $ac_aux_files
  do
    # As a special case, if "install-sh" is required, that requirement
    # can be satisfied by any of "install-sh", "install.sh", or "shtool",
    # and $ac_install_sh is set appropriately for whichever one is found.
    if test x"$ac_aux" = x"install-sh"
    then
      if test -f "${as_dir}install-sh"; then
        printf "%s\n" "$as_me:${as_lineno-$LINENO}:   ${as_dir}install-sh found" >&5
        ac_install_sh="${as_dir}install-sh -c"
      elif test -f "${as_dir}install.sh"; then
        printf "%s\n" "$as_me:${as_lineno-$LINENO}:   ${as_dir}install.sh found" >&5
        ac_install_sh="${as_dir}install.sh -c"
      elif test -f "${as_dir}shtool"; then
        printf "%s\n" "$as_me:${as_lineno-$LINENO}:   ${as_dir}shtool found" >&5
        ac_install_sh="${as_dir}shtool install -c"
      else
        ac_aux_dir_found=no
        if $ac_first_candidate; then
          ac_missing_aux_files="${ac_missing_aux_files} install-sh"
        else
          break
        fi
      fi
    else
      if test -f "${as_dir}${ac_aux}"; then
        printf "%s\n" "$as_me:${as_lineno-$LINENO}:   ${as_dir}${ac_aux} found" >&5
      else
        ac_aux_dir_found=no
        if $ac_first_candidate; then
          ac_missing_aux_files="${ac_missing_aux_files} ${ac_aux}"
        else
          break
        fi
      fi
    fi
  done
  if test "$ac_aux_dir_found" = yes; then
    ac_aux_dir="$as_dir"
    break
  fi
  ac_first_candidate=false

  as_found=false
done
IFS=$as_save_IFS
if $as_found
then :

else $as_nop
  as_fn_error $? "cannot find required auxiliary files:$ac_missing_aux_files" "$LINENO" 5
fi


# These three variables are undocumented and unsupported,
# and are intended to be withdrawn in a future Autoconf release.
# They can cause serious problems if a builder's source tree is in a directory
# whose full name contains unusual characters.
if test -f "${ac_aux_dir}config.guess"; then
  ac_config_guess="$SHELL ${ac_aux_dir}config.guess"
fi
if test -f "${ac_aux_dir}config.sub"; then
  ac_config_sub="$SHELL ${ac_aux_dir}config.sub"
fi
if test -f "$ac_aux_dir/configure"; then
  ac_configure="$SHELL ${ac_aux_dir}configure"
fi

# Check that the precious variables saved in the cache have kept the same
# value.
ac_cache_corrupted=false
for ac_var in $ac_precious_vars; do
  eval ac_old_set=\$ac_cv_env_${ac_var}_set
  eval ac_new_set=\$ac_env_${ac_var}_set
  eval ac_old_val=\$ac_cv_env_${ac_var}_value
  eval ac_new_val=\$ac_env_${ac_var}_value
  case $ac_old_set,$ac_new_set in
    set,)
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: \`$ac_var' was set to \`$ac_old_val' in the previous run" >&5
printf "%s\n" "$as_me: error: \`$ac_var' was set to \`$ac_old_val' in the previous run" >&2;}
      ac_cache_corrupted=: ;;
    ,set)
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: \`$ac_var' was not set in the previous run" >&5
printf "%s\n" "$as_me: error: \`$ac_var' was not set in the previous run" >&2;}
      ac_cache_corrupted=: ;;
    ,);;
    *)
      if test "x$ac_old_val" != "x$ac_new_val"; then
	# differences in whitespace do not lead to failure.
	ac_old_val_w=`echo x $ac_old_val`
	ac_new_val_w=`echo x $ac_new_val`
	if test "$ac_old_val_w" != "$ac_new_val_w"; then
	  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: \`$ac_var' has changed since the previous run:" >&5
printf "%s\n" "$as_me: error: \`$ac_var' has changed since the previous run:" >&2;}
	  ac_cache_corrupted=:
	else
	  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: warning: ignoring whitespace changes in \`$ac_var' since the previous run:" >&5
printf "%s\n" "$as_me: warning: ignoring whitespace changes in \`$ac_var' since the previous run:" >&2;}
	  eval $ac_var=\$ac_old_val
	fi
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}:   former value:  \`$ac_old_val'" >&5
printf "%s\n" "$as_me:   former value:  \`$ac_old_val'" >&2;}
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}:   current value: \`$ac_new_val'" >&5
printf "%s\n" "$as_me:   current value: \`$ac_new_val'" >&2;}
      fi;;
  esac
  # Pass precious variables to config.status.
  if test "$ac_new_set" = set; then
    case $ac_new_val in
    *\'*) ac_arg=$ac_var=`printf "%s\n" "$ac_new_val" | sed "s/'/'\\\\\\\\''/g"` ;;
    *) ac_arg=$ac_var=$ac_new_val ;;
    esac
    case " $ac_configure_args " in
      *" '$ac_arg' "*) ;; # Avoid dups.  Use of quotes ensures accuracy.
      *) as_fn_append ac_configure_args " '$ac_arg'" ;;
    esac
  fi
done
if $ac_cache_corrupted; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: changes in the environment can compromise the build" >&5
printf "%s\n" "$as_me: error: changes in the environment can compromise the build" >&2;}
  as_fn_error $? "run \`${MAKE-make} distclean' and/or \`rm $cache_file'
	    and start over" "$LINENO" 5
fi
## -------------------- ##
## Main body of script. ##
## -------------------- ##

ac_ext=c
ac_cpp='$CPP $CPPFLAGS'
ac_compile='$CC -c $CFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CC -o conftest$ac_exeext $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_c_compiler_gnu



FLTK_VERSION="1.4.4"
FLTK_VERSION_MAJOR=$(echo 1.4.4 | awk -F. '{print $1}')
FLTK_VERSION_MINOR=$(echo 1.4.4 | awk -F. '{print $2}')
FLTK_VERSION_PATCH=$(echo 1.4.4 | awk -F. '{print $3}')

FL_DSO_VERSION="${FLTK_VERSION_MAJOR}.${FLTK_VERSION_MINOR}"
FL_ABI_VERSION="${FLTK_VERSION_MAJOR}.${FLTK_VERSION_MINOR}.0"















  # Make sure we can run config.sub.
$SHELL "${ac_aux_dir}config.sub" sun4 >/dev/null 2>&1 ||
  as_fn_error $? "cannot run $SHELL ${ac_aux_dir}config.sub" "$LINENO" 5

{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking build system type" >&5
printf %s "checking build system type... " >&6; }
if test ${ac_cv_build+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_build_alias=$build_alias
test "x$ac_build_alias" = x &&
  ac_build_alias=`$SHELL "${ac_aux_dir}config.guess"`
test "x$ac_build_alias" = x &&
  as_fn_error $? "cannot guess build type; you must specify one" "$LINENO" 5
ac_cv_build=`$SHELL "${ac_aux_dir}config.sub" $ac_build_alias` ||
  as_fn_error $? "$SHELL ${ac_aux_dir}config.sub $ac_build_alias failed" "$LINENO" 5

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_build" >&5
printf "%s\n" "$ac_cv_build" >&6; }
case $ac_cv_build in
*-*-*) ;;
*) as_fn_error $? "invalid value of canonical build" "$LINENO" 5;;
esac
build=$ac_cv_build
ac_save_IFS=$IFS; IFS='-'
set x $ac_cv_build
shift
build_cpu=$1
build_vendor=$2
shift; shift
# Remember, the first character of IFS is used to create $*,
# except with old shells:
build_os=$*
IFS=$ac_save_IFS
case $build_os in *\ *) build_os=`echo "$build_os" | sed 's/ /-/g'`;; esac


{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking host system type" >&5
printf %s "checking host system type... " >&6; }
if test ${ac_cv_host+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test "x$host_alias" = x; then
  ac_cv_host=$ac_cv_build
else
  ac_cv_host=`$SHELL "${ac_aux_dir}config.sub" $host_alias` ||
    as_fn_error $? "$SHELL ${ac_aux_dir}config.sub $host_alias failed" "$LINENO" 5
fi

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_host" >&5
printf "%s\n" "$ac_cv_host" >&6; }
case $ac_cv_host in
*-*-*) ;;
*) as_fn_error $? "invalid value of canonical host" "$LINENO" 5;;
esac
host=$ac_cv_host
ac_save_IFS=$IFS; IFS='-'
set x $ac_cv_host
shift
host_cpu=$1
host_vendor=$2
shift; shift
# Remember, the first character of IFS is used to create $*,
# except with old shells:
host_os=$*
IFS=$ac_save_IFS
case $host_os in *\ *) host_os=`echo "$host_os" | sed 's/ /-/g'`;; esac




if test "$host" != "$build"
then :

    fltk_cross_compiling="yes"

else $as_nop

    fltk_cross_compiling="no"

fi


CFLAGS="${CFLAGS:=}"
CPPFLAGS="${CPPFLAGS:=}"
CXXFLAGS="${CXXFLAGS:=}"
DSOFLAGS="${DSOFLAGS:=}"
LDFLAGS="${LDFLAGS:=}"
LIBS="${LDFLAGS:=}"











ac_ext=c
ac_cpp='$CPP $CPPFLAGS'
ac_compile='$CC -c $CFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CC -o conftest$ac_exeext $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_c_compiler_gnu
if test -n "$ac_tool_prefix"; then
  # Extract the first word of "${ac_tool_prefix}gcc", so it can be a program name with args.
set dummy ${ac_tool_prefix}gcc; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$CC"; then
  ac_cv_prog_CC="$CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_CC="${ac_tool_prefix}gcc"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
CC=$ac_cv_prog_CC
if test -n "$CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $CC" >&5
printf "%s\n" "$CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$ac_cv_prog_CC"; then
  ac_ct_CC=$CC
  # Extract the first word of "gcc", so it can be a program name with args.
set dummy gcc; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_ac_ct_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$ac_ct_CC"; then
  ac_cv_prog_ac_ct_CC="$ac_ct_CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_ac_ct_CC="gcc"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
ac_ct_CC=$ac_cv_prog_ac_ct_CC
if test -n "$ac_ct_CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_ct_CC" >&5
printf "%s\n" "$ac_ct_CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi

  if test "x$ac_ct_CC" = x; then
    CC=""
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    CC=$ac_ct_CC
  fi
else
  CC="$ac_cv_prog_CC"
fi

if test -z "$CC"; then
          if test -n "$ac_tool_prefix"; then
    # Extract the first word of "${ac_tool_prefix}cc", so it can be a program name with args.
set dummy ${ac_tool_prefix}cc; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$CC"; then
  ac_cv_prog_CC="$CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_CC="${ac_tool_prefix}cc"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
CC=$ac_cv_prog_CC
if test -n "$CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $CC" >&5
printf "%s\n" "$CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


  fi
fi
if test -z "$CC"; then
  # Extract the first word of "cc", so it can be a program name with args.
set dummy cc; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$CC"; then
  ac_cv_prog_CC="$CC" # Let the user override the test.
else
  ac_prog_rejected=no
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    if test "$as_dir$ac_word$ac_exec_ext" = "/usr/ucb/cc"; then
       ac_prog_rejected=yes
       continue
     fi
    ac_cv_prog_CC="cc"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

if test $ac_prog_rejected = yes; then
  # We found a bogon in the path, so make sure we never use it.
  set dummy $ac_cv_prog_CC
  shift
  if test $# != 0; then
    # We chose a different compiler from the bogus one.
    # However, it has the same basename, so the bogon will be chosen
    # first if we set CC to just the basename; use the full file name.
    shift
    ac_cv_prog_CC="$as_dir$ac_word${1+' '}$@"
  fi
fi
fi
fi
CC=$ac_cv_prog_CC
if test -n "$CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $CC" >&5
printf "%s\n" "$CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$CC"; then
  if test -n "$ac_tool_prefix"; then
  for ac_prog in cl.exe
  do
    # Extract the first word of "$ac_tool_prefix$ac_prog", so it can be a program name with args.
set dummy $ac_tool_prefix$ac_prog; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$CC"; then
  ac_cv_prog_CC="$CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_CC="$ac_tool_prefix$ac_prog"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
CC=$ac_cv_prog_CC
if test -n "$CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $CC" >&5
printf "%s\n" "$CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


    test -n "$CC" && break
  done
fi
if test -z "$CC"; then
  ac_ct_CC=$CC
  for ac_prog in cl.exe
do
  # Extract the first word of "$ac_prog", so it can be a program name with args.
set dummy $ac_prog; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_ac_ct_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$ac_ct_CC"; then
  ac_cv_prog_ac_ct_CC="$ac_ct_CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_ac_ct_CC="$ac_prog"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
ac_ct_CC=$ac_cv_prog_ac_ct_CC
if test -n "$ac_ct_CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_ct_CC" >&5
printf "%s\n" "$ac_ct_CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


  test -n "$ac_ct_CC" && break
done

  if test "x$ac_ct_CC" = x; then
    CC=""
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    CC=$ac_ct_CC
  fi
fi

fi
if test -z "$CC"; then
  if test -n "$ac_tool_prefix"; then
  # Extract the first word of "${ac_tool_prefix}clang", so it can be a program name with args.
set dummy ${ac_tool_prefix}clang; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$CC"; then
  ac_cv_prog_CC="$CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_CC="${ac_tool_prefix}clang"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
CC=$ac_cv_prog_CC
if test -n "$CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $CC" >&5
printf "%s\n" "$CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$ac_cv_prog_CC"; then
  ac_ct_CC=$CC
  # Extract the first word of "clang", so it can be a program name with args.
set dummy clang; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_ac_ct_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$ac_ct_CC"; then
  ac_cv_prog_ac_ct_CC="$ac_ct_CC" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_ac_ct_CC="clang"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
ac_ct_CC=$ac_cv_prog_ac_ct_CC
if test -n "$ac_ct_CC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_ct_CC" >&5
printf "%s\n" "$ac_ct_CC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi

  if test "x$ac_ct_CC" = x; then
    CC=""
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    CC=$ac_ct_CC
  fi
else
  CC="$ac_cv_prog_CC"
fi

fi


test -z "$CC" && { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error $? "no acceptable C compiler found in \$PATH
See \`config.log' for more details" "$LINENO" 5; }

# Provide some information about the compiler.
printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for C compiler version" >&5
set X $ac_compile
ac_compiler=$2
for ac_option in --version -v -V -qversion -version; do
  { { ac_try="$ac_compiler $ac_option >&5"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_compiler $ac_option >&5") 2>conftest.err
  ac_status=$?
  if test -s conftest.err; then
    sed '10a\
... rest of stderr output deleted ...
         10q' conftest.err >conftest.er1
    cat conftest.er1 >&5
  fi
  rm -f conftest.er1 conftest.err
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }
done

cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
ac_clean_files_save=$ac_clean_files
ac_clean_files="$ac_clean_files a.out a.out.dSYM a.exe b.out"
# Try to create an executable without -o first, disregard a.out.
# It will help us diagnose broken compilers, and finding out an intuition
# of exeext.
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the C compiler works" >&5
printf %s "checking whether the C compiler works... " >&6; }
ac_link_default=`printf "%s\n" "$ac_link" | sed 's/ -o *conftest[^ ]*//'`

# The possible output files:
ac_files="a.out conftest.exe conftest a.exe a_out.exe b.out conftest.*"

ac_rmfiles=
for ac_file in $ac_files
do
  case $ac_file in
    *.$ac_ext | *.xcoff | *.tds | *.d | *.pdb | *.xSYM | *.bb | *.bbg | *.map | *.inf | *.dSYM | *.o | *.obj ) ;;
    * ) ac_rmfiles="$ac_rmfiles $ac_file";;
  esac
done
rm -f $ac_rmfiles

if { { ac_try="$ac_link_default"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_link_default") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }
then :
  # Autoconf-2.13 could set the ac_cv_exeext variable to `no'.
# So ignore a value of `no', otherwise this would lead to `EXEEXT = no'
# in a Makefile.  We should not override ac_cv_exeext if it was cached,
# so that the user can short-circuit this test for compilers unknown to
# Autoconf.
for ac_file in $ac_files ''
do
  test -f "$ac_file" || continue
  case $ac_file in
    *.$ac_ext | *.xcoff | *.tds | *.d | *.pdb | *.xSYM | *.bb | *.bbg | *.map | *.inf | *.dSYM | *.o | *.obj )
	;;
    [ab].out )
	# We found the default executable, but exeext='' is most
	# certainly right.
	break;;
    *.* )
	if test ${ac_cv_exeext+y} && test "$ac_cv_exeext" != no;
	then :; else
	   ac_cv_exeext=`expr "$ac_file" : '[^.]*\(\..*\)'`
	fi
	# We set ac_cv_exeext here because the later test for it is not
	# safe: cross compilers may not add the suffix if given an `-o'
	# argument, so we may need to know it at that point already.
	# Even if this section looks crufty: it has the advantage of
	# actually working.
	break;;
    * )
	break;;
  esac
done
test "$ac_cv_exeext" = no && ac_cv_exeext=

else $as_nop
  ac_file=''
fi
if test -z "$ac_file"
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
printf "%s\n" "$as_me: failed program was:" >&5
sed 's/^/| /' conftest.$ac_ext >&5

{ { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error 77 "C compiler cannot create executables
See \`config.log' for more details" "$LINENO" 5; }
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for C compiler default output file name" >&5
printf %s "checking for C compiler default output file name... " >&6; }
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_file" >&5
printf "%s\n" "$ac_file" >&6; }
ac_exeext=$ac_cv_exeext

rm -f -r a.out a.out.dSYM a.exe conftest$ac_cv_exeext b.out
ac_clean_files=$ac_clean_files_save
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for suffix of executables" >&5
printf %s "checking for suffix of executables... " >&6; }
if { { ac_try="$ac_link"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_link") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }
then :
  # If both `conftest.exe' and `conftest' are `present' (well, observable)
# catch `conftest.exe'.  For instance with Cygwin, `ls conftest' will
# work properly (i.e., refer to `conftest.exe'), while it won't with
# `rm'.
for ac_file in conftest.exe conftest conftest.*; do
  test -f "$ac_file" || continue
  case $ac_file in
    *.$ac_ext | *.xcoff | *.tds | *.d | *.pdb | *.xSYM | *.bb | *.bbg | *.map | *.inf | *.dSYM | *.o | *.obj ) ;;
    *.* ) ac_cv_exeext=`expr "$ac_file" : '[^.]*\(\..*\)'`
	  break;;
    * ) break;;
  esac
done
else $as_nop
  { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error $? "cannot compute suffix of executables: cannot compile and link
See \`config.log' for more details" "$LINENO" 5; }
fi
rm -f conftest conftest$ac_cv_exeext
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_exeext" >&5
printf "%s\n" "$ac_cv_exeext" >&6; }

rm -f conftest.$ac_ext
EXEEXT=$ac_cv_exeext
ac_exeext=$EXEEXT
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <stdio.h>
int
main (void)
{
FILE *f = fopen ("conftest.out", "w");
 return ferror (f) || fclose (f) != 0;

  ;
  return 0;
}
_ACEOF
ac_clean_files="$ac_clean_files conftest.out"
# Check that the compiler produces executables we can run.  If not, either
# the compiler is broken, or we cross compile.
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether we are cross compiling" >&5
printf %s "checking whether we are cross compiling... " >&6; }
if test "$cross_compiling" != yes; then
  { { ac_try="$ac_link"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_link") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }
  if { ac_try='./conftest$ac_cv_exeext'
  { { case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_try") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }; }; then
    cross_compiling=no
  else
    if test "$cross_compiling" = maybe; then
	cross_compiling=yes
    else
	{ { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error 77 "cannot run C compiled programs.
If you meant to cross compile, use \`--host'.
See \`config.log' for more details" "$LINENO" 5; }
    fi
  fi
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $cross_compiling" >&5
printf "%s\n" "$cross_compiling" >&6; }

rm -f conftest.$ac_ext conftest$ac_cv_exeext conftest.out
ac_clean_files=$ac_clean_files_save
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for suffix of object files" >&5
printf %s "checking for suffix of object files... " >&6; }
if test ${ac_cv_objext+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
rm -f conftest.o conftest.obj
if { { ac_try="$ac_compile"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_compile") 2>&5
  ac_status=$?
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }
then :
  for ac_file in conftest.o conftest.obj conftest.*; do
  test -f "$ac_file" || continue;
  case $ac_file in
    *.$ac_ext | *.xcoff | *.tds | *.d | *.pdb | *.xSYM | *.bb | *.bbg | *.map | *.inf | *.dSYM ) ;;
    *) ac_cv_objext=`expr "$ac_file" : '.*\.\(.*\)'`
       break;;
  esac
done
else $as_nop
  printf "%s\n" "$as_me: failed program was:" >&5
sed 's/^/| /' conftest.$ac_ext >&5

{ { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error $? "cannot compute suffix of object files: cannot compile
See \`config.log' for more details" "$LINENO" 5; }
fi
rm -f conftest.$ac_cv_objext conftest.$ac_ext
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_objext" >&5
printf "%s\n" "$ac_cv_objext" >&6; }
OBJEXT=$ac_cv_objext
ac_objext=$OBJEXT
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports GNU C" >&5
printf %s "checking whether the compiler supports GNU C... " >&6; }
if test ${ac_cv_c_compiler_gnu+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{
#ifndef __GNUC__
       choke me
#endif

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_compiler_gnu=yes
else $as_nop
  ac_compiler_gnu=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
ac_cv_c_compiler_gnu=$ac_compiler_gnu

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_c_compiler_gnu" >&5
printf "%s\n" "$ac_cv_c_compiler_gnu" >&6; }
ac_compiler_gnu=$ac_cv_c_compiler_gnu

if test $ac_compiler_gnu = yes; then
  GCC=yes
else
  GCC=
fi
ac_test_CFLAGS=${CFLAGS+y}
ac_save_CFLAGS=$CFLAGS
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether $CC accepts -g" >&5
printf %s "checking whether $CC accepts -g... " >&6; }
if test ${ac_cv_prog_cc_g+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_save_c_werror_flag=$ac_c_werror_flag
   ac_c_werror_flag=yes
   ac_cv_prog_cc_g=no
   CFLAGS="-g"
   cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_prog_cc_g=yes
else $as_nop
  CFLAGS=""
      cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :

else $as_nop
  ac_c_werror_flag=$ac_save_c_werror_flag
	 CFLAGS="-g"
	 cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_prog_cc_g=yes
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
   ac_c_werror_flag=$ac_save_c_werror_flag
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_prog_cc_g" >&5
printf "%s\n" "$ac_cv_prog_cc_g" >&6; }
if test $ac_test_CFLAGS; then
  CFLAGS=$ac_save_CFLAGS
elif test $ac_cv_prog_cc_g = yes; then
  if test "$GCC" = yes; then
    CFLAGS="-g -O2"
  else
    CFLAGS="-g"
  fi
else
  if test "$GCC" = yes; then
    CFLAGS="-O2"
  else
    CFLAGS=
  fi
fi
ac_prog_cc_stdc=no
if test x$ac_prog_cc_stdc = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $CC option to enable C11 features" >&5
printf %s "checking for $CC option to enable C11 features... " >&6; }
if test ${ac_cv_prog_cc_c11+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_cv_prog_cc_c11=no
ac_save_CC=$CC
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$ac_c_conftest_c11_program
_ACEOF
for ac_arg in '' -std=gnu11
do
  CC="$ac_save_CC $ac_arg"
  if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_prog_cc_c11=$ac_arg
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam
  test "x$ac_cv_prog_cc_c11" != "xno" && break
done
rm -f conftest.$ac_ext
CC=$ac_save_CC
fi

if test "x$ac_cv_prog_cc_c11" = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: unsupported" >&5
printf "%s\n" "unsupported" >&6; }
else $as_nop
  if test "x$ac_cv_prog_cc_c11" = x
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: none needed" >&5
printf "%s\n" "none needed" >&6; }
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_prog_cc_c11" >&5
printf "%s\n" "$ac_cv_prog_cc_c11" >&6; }
     CC="$CC $ac_cv_prog_cc_c11"
fi
  ac_cv_prog_cc_stdc=$ac_cv_prog_cc_c11
  ac_prog_cc_stdc=c11
fi
fi
if test x$ac_prog_cc_stdc = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $CC option to enable C99 features" >&5
printf %s "checking for $CC option to enable C99 features... " >&6; }
if test ${ac_cv_prog_cc_c99+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_cv_prog_cc_c99=no
ac_save_CC=$CC
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$ac_c_conftest_c99_program
_ACEOF
for ac_arg in '' -std=gnu99 -std=c99 -c99 -qlanglvl=extc1x -qlanglvl=extc99 -AC99 -D_STDC_C99=
do
  CC="$ac_save_CC $ac_arg"
  if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_prog_cc_c99=$ac_arg
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam
  test "x$ac_cv_prog_cc_c99" != "xno" && break
done
rm -f conftest.$ac_ext
CC=$ac_save_CC
fi

if test "x$ac_cv_prog_cc_c99" = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: unsupported" >&5
printf "%s\n" "unsupported" >&6; }
else $as_nop
  if test "x$ac_cv_prog_cc_c99" = x
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: none needed" >&5
printf "%s\n" "none needed" >&6; }
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_prog_cc_c99" >&5
printf "%s\n" "$ac_cv_prog_cc_c99" >&6; }
     CC="$CC $ac_cv_prog_cc_c99"
fi
  ac_cv_prog_cc_stdc=$ac_cv_prog_cc_c99
  ac_prog_cc_stdc=c99
fi
fi
if test x$ac_prog_cc_stdc = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $CC option to enable C89 features" >&5
printf %s "checking for $CC option to enable C89 features... " >&6; }
if test ${ac_cv_prog_cc_c89+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_cv_prog_cc_c89=no
ac_save_CC=$CC
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$ac_c_conftest_c89_program
_ACEOF
for ac_arg in '' -qlanglvl=extc89 -qlanglvl=ansi -std -Ae "-Aa -D_HPUX_SOURCE" "-Xc -D__EXTENSIONS__"
do
  CC="$ac_save_CC $ac_arg"
  if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_prog_cc_c89=$ac_arg
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam
  test "x$ac_cv_prog_cc_c89" != "xno" && break
done
rm -f conftest.$ac_ext
CC=$ac_save_CC
fi

if test "x$ac_cv_prog_cc_c89" = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: unsupported" >&5
printf "%s\n" "unsupported" >&6; }
else $as_nop
  if test "x$ac_cv_prog_cc_c89" = x
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: none needed" >&5
printf "%s\n" "none needed" >&6; }
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_prog_cc_c89" >&5
printf "%s\n" "$ac_cv_prog_cc_c89" >&6; }
     CC="$CC $ac_cv_prog_cc_c89"
fi
  ac_cv_prog_cc_stdc=$ac_cv_prog_cc_c89
  ac_prog_cc_stdc=c89
fi
fi

ac_ext=c
ac_cpp='$CPP $CPPFLAGS'
ac_compile='$CC -c $CFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CC -o conftest$ac_exeext $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_c_compiler_gnu







ac_ext=cpp
ac_cpp='$CXXCPP $CPPFLAGS'
ac_compile='$CXX -c $CXXFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CXX -o conftest$ac_exeext $CXXFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_cxx_compiler_gnu
if test -z "$CXX"; then
  if test -n "$CCC"; then
    CXX=$CCC
  else
    if test -n "$ac_tool_prefix"; then
  for ac_prog in g++ c++ gpp aCC CC cxx cc++ cl.exe FCC KCC RCC xlC_r xlC clang++
  do
    # Extract the first word of "$ac_tool_prefix$ac_prog", so it can be a program name with args.
set dummy $ac_tool_prefix$ac_prog; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_CXX+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$CXX"; then
  ac_cv_prog_CXX="$CXX" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_CXX="$ac_tool_prefix$ac_prog"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
CXX=$ac_cv_prog_CXX
if test -n "$CXX"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $CXX" >&5
printf "%s\n" "$CXX" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


    test -n "$CXX" && break
  done
fi
if test -z "$CXX"; then
  ac_ct_CXX=$CXX
  for ac_prog in g++ c++ gpp aCC CC cxx cc++ cl.exe FCC KCC RCC xlC_r xlC clang++
do
  # Extract the first word of "$ac_prog", so it can be a program name with args.
set dummy $ac_prog; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_ac_ct_CXX+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$ac_ct_CXX"; then
  ac_cv_prog_ac_ct_CXX="$ac_ct_CXX" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_ac_ct_CXX="$ac_prog"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
ac_ct_CXX=$ac_cv_prog_ac_ct_CXX
if test -n "$ac_ct_CXX"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_ct_CXX" >&5
printf "%s\n" "$ac_ct_CXX" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


  test -n "$ac_ct_CXX" && break
done

  if test "x$ac_ct_CXX" = x; then
    CXX="g++"
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    CXX=$ac_ct_CXX
  fi
fi

  fi
fi
# Provide some information about the compiler.
printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for C++ compiler version" >&5
set X $ac_compile
ac_compiler=$2
for ac_option in --version -v -V -qversion; do
  { { ac_try="$ac_compiler $ac_option >&5"
case "(($ac_try" in
  *\"* | *\`* | *\\*) ac_try_echo=\$ac_try;;
  *) ac_try_echo=$ac_try;;
esac
eval ac_try_echo="\"\$as_me:${as_lineno-$LINENO}: $ac_try_echo\""
printf "%s\n" "$ac_try_echo"; } >&5
  (eval "$ac_compiler $ac_option >&5") 2>conftest.err
  ac_status=$?
  if test -s conftest.err; then
    sed '10a\
... rest of stderr output deleted ...
         10q' conftest.err >conftest.er1
    cat conftest.er1 >&5
  fi
  rm -f conftest.er1 conftest.err
  printf "%s\n" "$as_me:${as_lineno-$LINENO}: \$? = $ac_status" >&5
  test $ac_status = 0; }
done

{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether the compiler supports GNU C++" >&5
printf %s "checking whether the compiler supports GNU C++... " >&6; }
if test ${ac_cv_cxx_compiler_gnu+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{
#ifndef __GNUC__
       choke me
#endif

  ;
  return 0;
}
_ACEOF
if ac_fn_cxx_try_compile "$LINENO"
then :
  ac_compiler_gnu=yes
else $as_nop
  ac_compiler_gnu=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
ac_cv_cxx_compiler_gnu=$ac_compiler_gnu

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_cxx_compiler_gnu" >&5
printf "%s\n" "$ac_cv_cxx_compiler_gnu" >&6; }
ac_compiler_gnu=$ac_cv_cxx_compiler_gnu

if test $ac_compiler_gnu = yes; then
  GXX=yes
else
  GXX=
fi
ac_test_CXXFLAGS=${CXXFLAGS+y}
ac_save_CXXFLAGS=$CXXFLAGS
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether $CXX accepts -g" >&5
printf %s "checking whether $CXX accepts -g... " >&6; }
if test ${ac_cv_prog_cxx_g+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_save_cxx_werror_flag=$ac_cxx_werror_flag
   ac_cxx_werror_flag=yes
   ac_cv_prog_cxx_g=no
   CXXFLAGS="-g"
   cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_cxx_try_compile "$LINENO"
then :
  ac_cv_prog_cxx_g=yes
else $as_nop
  CXXFLAGS=""
      cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_cxx_try_compile "$LINENO"
then :

else $as_nop
  ac_cxx_werror_flag=$ac_save_cxx_werror_flag
	 CXXFLAGS="-g"
	 cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_cxx_try_compile "$LINENO"
then :
  ac_cv_prog_cxx_g=yes
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
   ac_cxx_werror_flag=$ac_save_cxx_werror_flag
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_prog_cxx_g" >&5
printf "%s\n" "$ac_cv_prog_cxx_g" >&6; }
if test $ac_test_CXXFLAGS; then
  CXXFLAGS=$ac_save_CXXFLAGS
elif test $ac_cv_prog_cxx_g = yes; then
  if test "$GXX" = yes; then
    CXXFLAGS="-g -O2"
  else
    CXXFLAGS="-g"
  fi
else
  if test "$GXX" = yes; then
    CXXFLAGS="-O2"
  else
    CXXFLAGS=
  fi
fi
ac_prog_cxx_stdcxx=no
if test x$ac_prog_cxx_stdcxx = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $CXX option to enable C++11 features" >&5
printf %s "checking for $CXX option to enable C++11 features... " >&6; }
if test ${ac_cv_prog_cxx_cxx11+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_cv_prog_cxx_cxx11=no
ac_save_CXX=$CXX
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$ac_cxx_conftest_cxx11_program
_ACEOF
for ac_arg in '' -std=gnu++11 -std=gnu++0x -std=c++11 -std=c++0x -qlanglvl=extended0x -AA
do
  CXX="$ac_save_CXX $ac_arg"
  if ac_fn_cxx_try_compile "$LINENO"
then :
  ac_cv_prog_cxx_cxx11=$ac_arg
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam
  test "x$ac_cv_prog_cxx_cxx11" != "xno" && break
done
rm -f conftest.$ac_ext
CXX=$ac_save_CXX
fi

if test "x$ac_cv_prog_cxx_cxx11" = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: unsupported" >&5
printf "%s\n" "unsupported" >&6; }
else $as_nop
  if test "x$ac_cv_prog_cxx_cxx11" = x
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: none needed" >&5
printf "%s\n" "none needed" >&6; }
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_prog_cxx_cxx11" >&5
printf "%s\n" "$ac_cv_prog_cxx_cxx11" >&6; }
     CXX="$CXX $ac_cv_prog_cxx_cxx11"
fi
  ac_cv_prog_cxx_stdcxx=$ac_cv_prog_cxx_cxx11
  ac_prog_cxx_stdcxx=cxx11
fi
fi
if test x$ac_prog_cxx_stdcxx = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $CXX option to enable C++98 features" >&5
printf %s "checking for $CXX option to enable C++98 features... " >&6; }
if test ${ac_cv_prog_cxx_cxx98+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_cv_prog_cxx_cxx98=no
ac_save_CXX=$CXX
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$ac_cxx_conftest_cxx98_program
_ACEOF
for ac_arg in '' -std=gnu++98 -std=c++98 -qlanglvl=extended -AA
do
  CXX="$ac_save_CXX $ac_arg"
  if ac_fn_cxx_try_compile "$LINENO"
then :
  ac_cv_prog_cxx_cxx98=$ac_arg
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam
  test "x$ac_cv_prog_cxx_cxx98" != "xno" && break
done
rm -f conftest.$ac_ext
CXX=$ac_save_CXX
fi

if test "x$ac_cv_prog_cxx_cxx98" = xno
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: unsupported" >&5
printf "%s\n" "unsupported" >&6; }
else $as_nop
  if test "x$ac_cv_prog_cxx_cxx98" = x
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: none needed" >&5
printf "%s\n" "none needed" >&6; }
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_prog_cxx_cxx98" >&5
printf "%s\n" "$ac_cv_prog_cxx_cxx98" >&6; }
     CXX="$CXX $ac_cv_prog_cxx_cxx98"
fi
  ac_cv_prog_cxx_stdcxx=$ac_cv_prog_cxx_cxx98
  ac_prog_cxx_stdcxx=cxx98
fi
fi

ac_ext=c
ac_cpp='$CPP $CPPFLAGS'
ac_compile='$CC -c $CFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CC -o conftest$ac_exeext $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_c_compiler_gnu

if test -n "$ac_tool_prefix"; then
  # Extract the first word of "${ac_tool_prefix}pkg-config", so it can be a program name with args.
set dummy ${ac_tool_prefix}pkg-config; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_path_PKGCONFIG+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  case $PKGCONFIG in
  [\\/]* | ?:[\\/]*)
  ac_cv_path_PKGCONFIG="$PKGCONFIG" # Let the user override the test with a path.
  ;;
  *)
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_path_PKGCONFIG="$as_dir$ac_word$ac_exec_ext"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

  ;;
esac
fi
PKGCONFIG=$ac_cv_path_PKGCONFIG
if test -n "$PKGCONFIG"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $PKGCONFIG" >&5
printf "%s\n" "$PKGCONFIG" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$ac_cv_path_PKGCONFIG"; then
  ac_pt_PKGCONFIG=$PKGCONFIG
  # Extract the first word of "pkg-config", so it can be a program name with args.
set dummy pkg-config; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_path_ac_pt_PKGCONFIG+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  case $ac_pt_PKGCONFIG in
  [\\/]* | ?:[\\/]*)
  ac_cv_path_ac_pt_PKGCONFIG="$ac_pt_PKGCONFIG" # Let the user override the test with a path.
  ;;
  *)
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_path_ac_pt_PKGCONFIG="$as_dir$ac_word$ac_exec_ext"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

  ;;
esac
fi
ac_pt_PKGCONFIG=$ac_cv_path_ac_pt_PKGCONFIG
if test -n "$ac_pt_PKGCONFIG"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_pt_PKGCONFIG" >&5
printf "%s\n" "$ac_pt_PKGCONFIG" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi

  if test "x$ac_pt_PKGCONFIG" = x; then
    PKGCONFIG=""
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    PKGCONFIG=$ac_pt_PKGCONFIG
  fi
else
  PKGCONFIG="$ac_cv_path_PKGCONFIG"
fi



ARCHFLAGS="${ARCHFLAGS:=}"

# Check whether --with-archflags was given.
if test ${with_archflags+y}
then :
  withval=$with_archflags;
    ARCHFLAGS="$withval"

fi



OPTIM="${OPTIM:=}"

# Check whether --with-optim was given.
if test ${with_optim+y}
then :
  withval=$with_optim;
    OPTIM="$withval"

fi




# Check whether --enable-cairo was given.
if test ${enable_cairo+y}
then :
  enableval=$enable_cairo;
fi

# Check whether --enable-cairoext was given.
if test ${enable_cairoext+y}
then :
  enableval=$enable_cairoext;
fi


# Check whether --enable-cp936 was given.
if test ${enable_cp936+y}
then :
  enableval=$enable_cp936;
fi

if test x$enable_cp936 = xyes
then :

    CFLAGS="$CFLAGS -DCP936"

fi

# Check whether --enable-cygwin was given.
if test ${enable_cygwin+y}
then :
  enableval=$enable_cygwin;
fi


# Check whether --enable-debug was given.
if test ${enable_debug+y}
then :
  enableval=$enable_debug;
fi

if test x$enable_debug = xyes
then :

    DEBUGFLAG="-g "

else $as_nop

    DEBUGFLAG=""

fi

# Check whether --enable-test was given.
if test ${enable_test+y}
then :
  enableval=$enable_test;
fi

if test x$enable_test = xno
then :

    TESTDIR=""

else $as_nop

    TESTDIR="test"

fi


# Check whether --enable-forms was given.
if test ${enable_forms+y}
then :
  enableval=$enable_forms;
fi


# Check whether --enable-gl was given.
if test ${enable_gl+y}
then :
  enableval=$enable_gl;
fi


# Check whether --enable-localjpeg was given.
if test ${enable_localjpeg+y}
then :
  enableval=$enable_localjpeg;
fi

# Check whether --enable-localpng was given.
if test ${enable_localpng+y}
then :
  enableval=$enable_localpng;
fi

# Check whether --enable-localzlib was given.
if test ${enable_localzlib+y}
then :
  enableval=$enable_localzlib;
fi


# Check whether --enable-pango was given.
if test ${enable_pango+y}
then :
  enableval=$enable_pango;
fi


# Check whether --enable-wayland was given.
if test ${enable_wayland+y}
then :
  enableval=$enable_wayland;
fi


# Check whether --enable-usecairo was given.
if test ${enable_usecairo+y}
then :
  enableval=$enable_usecairo;
fi


# Check whether --enable-use_std was given.
if test ${enable_use_std+y}
then :
  enableval=$enable_use_std;
fi


# Check whether --enable-print was given.
if test ${enable_print+y}
then :
  enableval=$enable_print;
fi

if test x$enable_print = xno
then :

    printf "%s\n" "#define FL_NO_PRINT_SUPPORT Disable X11 print support?" >>confdefs.h


fi

# Check whether --enable-shared was given.
if test ${enable_shared+y}
then :
  enableval=$enable_shared;
fi


# Check whether --enable-svg was given.
if test ${enable_svg+y}
then :
  enableval=$enable_svg;
fi

if test x$enable_svg != xno
then :


printf "%s\n" "#define FLTK_USE_SVG 1" >>confdefs.h


fi

# Check whether --enable-threads was given.
if test ${enable_threads+y}
then :
  enableval=$enable_threads;
fi


# Check whether --enable-x11 was given.
if test ${enable_x11+y}
then :
  enableval=$enable_x11;
fi


# Check whether --enable-xcursor was given.
if test ${enable_xcursor+y}
then :
  enableval=$enable_xcursor;
fi


# Check whether --enable-xfixes was given.
if test ${enable_xfixes+y}
then :
  enableval=$enable_xfixes;
fi


# Check whether --enable-xft was given.
if test ${enable_xft+y}
then :
  enableval=$enable_xft;
fi


# Check whether --enable-xinerama was given.
if test ${enable_xinerama+y}
then :
  enableval=$enable_xinerama;
fi


# Check whether --enable-xrender was given.
if test ${enable_xrender+y}
then :
  enableval=$enable_xrender;
fi


# Check whether --enable-fluid was given.
if test ${enable_fluid+y}
then :
  enableval=$enable_fluid;
fi



case $host_os in #(
  cygwin* | mingw*) :

    # Check whether --enable-gdiplus was given.
if test ${enable_gdiplus+y}
then :
  enableval=$enable_gdiplus;
fi


    gdiplus_found=no
    if test x$enable_gdiplus != xno
then :

               for ac_header in wtypes.h gdiplus.h
do :
  as_ac_Header=`printf "%s\n" "ac_cv_header_$ac_header" | $as_tr_sh`
ac_fn_c_check_header_compile "$LINENO" "$ac_header" "$as_ac_Header" "#include <wtypes.h>
"
if eval test \"x\$"$as_ac_Header"\" = x"yes"
then :
  cat >>confdefs.h <<_ACEOF
#define `printf "%s\n" "HAVE_$ac_header" | $as_tr_cpp` 1
_ACEOF

            printf "%s\n" "#define USE_GDIPLUS 1" >>confdefs.h

            LIBS="-lgdiplus $LIBS"
            gdiplus_found=yes

fi

done

fi

 ;; #(
  *) :
     ;;
esac

if test x$enable_pango = xyes -a x$enable_xft = xno
then :

    as_fn_error $? "--disable-xft and --enable-pango are incompatible because Xft is necessary for Pango." "$LINENO" 5

fi


# TODO: Fix long long test
if test "x$with_archflags" != x
then :

    CFLAGS="$CFLAGS $with_archflags"

fi

BUILD=""


host_os_gui=$host_os
case $host_os in #(
  cygwin*) :

    # Handle Cygwin option *first*, before all other tests.
    if test x$enable_cygwin = xyes
then :

        # we target Cygwin in combination with X11
        if test x$enable_x11 = xyes
then :

            host_os_gui="X11$host_os"

fi

fi
 ;; #(
  darwin*) :

    if test x$enable_x11 = xyes
then :

        host_os_gui="X11"
        macosversion=$(sw_vers -productVersion | cut -d. -f2)
        macosversion_maj=$(sw_vers -productVersion | cut -d. -f1)
        if test $macosversion_maj -ge 11 -o $macosversion -ge 13
then :

            CXXFLAGS="$CXXFLAGS -mmacosx-version-min=10.9 -D_LIBCPP_HAS_THREAD_API_PTHREAD"

fi

fi
 ;; #(
  *) :
     ;;
esac


LINKFLTK="../lib/libfltk.a"
LINKFLTKGL="../lib/libfltk_gl.a"
LINKFLTKIMG="../lib/libfltk_images.a"
GLDEMOS="gldemos"

LIBEXT=".a"
LIBNAME="../lib/libfltk.a"
GLLIBNAME="../lib/libfltk_gl.a"
IMGLIBNAME="../lib/libfltk_images.a"
CAIROLIBNAME="../lib/libfltk_cairo.a"

LIBBASENAME="libfltk.a"
GLLIBBASENAME="libfltk_gl.a"
IMGLIBBASENAME="libfltk_images.a"
CAIROLIBBASENAME="libfltk_cairo.a"

if test x$enable_forms != xno
then :

    build_forms="yes"
    LINKFLTKFORMS="../lib/libfltk_forms.a"
    FLLIBNAME="../lib/libfltk_forms.a"
    FLLIBBASENAME="libfltk_forms.a"
    printf "%s\n" "#define FLTK_HAVE_FORMS 1" >>confdefs.h


else $as_nop

    build_forms="no"
    LINKFLTKFORMS=""
    FLLIBNAME=""
    FLLIBBASENAME=""

fi


CAIRODIR=""
CAIROFLAGS=""
LINKFLTKCAIRO=""
FLTKCAIROOPTION=""
CAIROLIBS=""

if test x$enable_cairoext = xyes
then :

    if $PKGCONFIG --exists cairo
then :

        printf "%s\n" "#define FLTK_HAVE_CAIROEXT 1" >>confdefs.h

        printf "%s\n" "#define FLTK_HAVE_CAIRO 1" >>confdefs.h

        CAIRODIR="cairo"
        CAIROFLAGS="$($PKGCONFIG --cflags cairo)"
        CAIROLIBS="$($PKGCONFIG --libs cairo)"
        CXXFLAGS="$CAIROFLAGS $CXXFLAGS"
        LINKFLTKCAIRO="../lib/libfltk_cairo.a"
        FLTKCAIROOPTION="-L ../cairo -lfltk_cairo$SHAREDSUFFIX"
        LIBS="$CAIROLIBS $LIBS"
        LINKFLTK="$LINKFLTK $LINKFLTKCAIRO"

else $as_nop

        as_fn_error $? "Cairo requested but not found." "$LINENO" 5

fi

elif test x$enable_cairo = xyes
then :

    if $PKGCONFIG --exists cairo
then :

        printf "%s\n" "#define FLTK_HAVE_CAIRO 1" >>confdefs.h

        CAIRODIR="cairo"
        CAIROFLAGS="$($PKGCONFIG --cflags cairo)"
        CAIROLIBS="$($PKGCONFIG --libs cairo)"
        CXXFLAGS="$CAIROFLAGS $CXXFLAGS"
        LINKFLTKCAIRO="../lib/libfltk_cairo.a"
        FLTKCAIROOPTION="-L ../cairo -lfltk_cairo$SHAREDSUFFIX"

else $as_nop

        as_fn_error $? "Cairo requested but not found." "$LINENO" 5

fi

fi























# Check whether --with-abiversion was given.
if test ${with_abiversion+y}
then :
  withval=$with_abiversion;
fi

has_abiversion="$with_abiversion"
if test "x$has_abiversion" = xyes -o "x$has_abiversion" = xno
then :

    has_abiversion=""

fi
if test "x$has_abiversion" != x
then :


printf "%s\n" "#define FL_ABI_VERSION $has_abiversion" >>confdefs.h


fi



if test "x$enable_shared" = xyes
then :

    PICFLAG=1
    SHAREDSUFFIX=""
    FLUID="fluid-shared"
    FLTK_OPTIONS="fltk-options-shared"

    case $host_os in #(
  darwin*) :

        DSONAME="libfltk.$FL_DSO_VERSION.dylib"
        FLDSONAME="libfltk_forms.$FL_DSO_VERSION.dylib"
        GLDSONAME="libfltk_gl.$FL_DSO_VERSION.dylib"
        IMGDSONAME="libfltk_images.$FL_DSO_VERSION.dylib"
        CAIRODSONAME="libfltk_cairo.$FL_DSO_VERSION.dylib"
        DSOCOMMAND="\$(CXX) \$(ARCHFLAGS) \$(DSOFLAGS) -dynamiclib -lc -o"
     ;; #(
  solaris*) :

        DSONAME="libfltk.so.$FL_DSO_VERSION"
        FLDSONAME="libfltk_forms.so.$FL_DSO_VERSION"
        GLDSONAME="libfltk_gl.so.$FL_DSO_VERSION"
        IMGDSONAME="libfltk_images.so.$FL_DSO_VERSION"
        CAIRODSONAME="libfltk_cairo.so.$FL_DSO_VERSION"
        DSOCOMMAND="\$(CXX) \$(DSOFLAGS) -Wl,-soname,\$@ \$(LDLIBS) -shared -fPIC $DEBUGFLAG -o"
        if test "x$libdir" != "x/usr/lib"
then :

            DSOLINK="-R$libdir"

fi
     ;; #(
  hpux*) :

        DSONAME="libfltk.sl.$FL_DSO_VERSION"
        FLDSONAME="libfltk_forms.sl.$FL_DSO_VERSION"
        GLDSONAME="libfltk_gl.sl.$FL_DSO_VERSION"
        IMGDSONAME="libfltk_images.sl.$FL_DSO_VERSION"
        CAIRODSONAME="libfltk_cairo.sl.$FL_DSO_VERSION"
        DSOCOMMAND="ld \$(DSOFLAGS) -b -z +h \$@ $DEBUGFLAG -o"
        if test "x$libdir" != "x/usr/lib"
then :

            DSOLINK="-Wl,-rpath,$libdir"

fi
     ;; #(
  irix*) :

        DSONAME="libfltk.so.$FL_DSO_VERSION"
        FLDSONAME="libfltk_forms.so.$FL_DSO_VERSION"
        GLDSONAME="libfltk_gl.so.$FL_DSO_VERSION"
        IMGDSONAME="libfltk_images.so.$FL_DSO_VERSION"
        CAIRODSONAME="libfltk_cairo.so.$FL_DSO_VERSION"
        DSOCOMMAND="\$(CXX) \$(DSOFLAGS) -Wl,-soname,\$@,-set_version,sgi1.1 \$(LDLIBS) -shared $DEBUGFLAG -o"
        if test "x$libdir" != "x/usr/lib" -a "x$libdir" != "x/usr/lib32" -a "x$libdir" != "x/usr/lib64"
then :

            DSOLINK="-Wl,-rpath,$libdir"

fi
     ;; #(
  osf*) :

        DSONAME="libfltk.so.$FL_DSO_VERSION"
        FLDSONAME="libfltk_forms.so.$FL_DSO_VERSION"
        GLDSONAME="libfltk_gl.so.$FL_DSO_VERSION"
        IMGDSONAME="libfltk_images.so.$FL_DSO_VERSION"
        CAIRODSONAME="libfltk_cairo.so.$FL_DSO_VERSION"
        DSOCOMMAND="\$(CXX) \$(DSOFLAGS) -Wl,-soname,\$@ \$(LDLIBS) -shared $DEBUGFLAG -o"
        if test "x$libdir" != "x/usr/lib" -a "x$libdir" != "x/usr/lib32"
then :

            DSOLINK="-Wl,-rpath,$libdir"

fi
     ;; #(
  linux*|*bsd*) :

        DSONAME="libfltk.so.$FL_DSO_VERSION"
        FLDSONAME="libfltk_forms.so.$FL_DSO_VERSION"
        GLDSONAME="libfltk_gl.so.$FL_DSO_VERSION"
        IMGDSONAME="libfltk_images.so.$FL_DSO_VERSION"
        CAIRODSONAME="libfltk_cairo.so.$FL_DSO_VERSION"
        DSOCOMMAND="\$(CXX) \$(DSOFLAGS) -Wl,-soname,\$@ \$(LDLIBS) -shared -fPIC $DEBUGFLAG -o"

        # See if the compiler supports -fvisibility...
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking if the compiler supports -fvisibility" >&5
printf %s "checking if the compiler supports -fvisibility... " >&6; }
if test ${ac_cv_cxx_fvisibility+y}
then :
  printf %s "(cached) " >&6
else $as_nop

            OLDCXXFLAGS="$CXXFLAGS"
            CXXFLAGS="$CXXFLAGS -fvisibility=hidden"
            ac_ext=cpp
ac_cpp='$CXXCPP $CPPFLAGS'
ac_compile='$CXX -c $CXXFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CXX -o conftest$ac_exeext $CXXFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_cxx_compiler_gnu

            cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_cxx_try_compile "$LINENO"
then :
  ac_cv_cxx_fvisibility=yes
else $as_nop
  ac_cv_cxx_fvisibility=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
            CXXFLAGS="$OLDCXXFLAGS"
            ac_ext=c
ac_cpp='$CPP $CPPFLAGS'
ac_compile='$CC -c $CFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CC -o conftest$ac_exeext $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_c_compiler_gnu


fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_cxx_fvisibility" >&5
printf "%s\n" "$ac_cv_cxx_fvisibility" >&6; }
        if test x"$ac_cv_cxx_fvisibility" = xyes
then :

            OPTIM="$OPTIM -fvisibility=hidden"

fi

        # See if the compiler supports -fvisibility-inlines-hidden...
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking if the compiler supports -fvisibility-inlines-hidden" >&5
printf %s "checking if the compiler supports -fvisibility-inlines-hidden... " >&6; }
if test ${ac_cv_cxx_fvisibility_inlines+y}
then :
  printf %s "(cached) " >&6
else $as_nop

            OLDCXXFLAGS="$CXXFLAGS"
            CXXFLAGS="$CXXFLAGS -fvisibility-inlines-hidden"
            ac_ext=cpp
ac_cpp='$CXXCPP $CPPFLAGS'
ac_compile='$CXX -c $CXXFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CXX -o conftest$ac_exeext $CXXFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_cxx_compiler_gnu

            cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_cxx_try_compile "$LINENO"
then :
  ac_cv_cxx_fvisibility_inlines=yes
else $as_nop
  ac_cv_cxx_fvisibility_inlines=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
            CXXFLAGS="$OLDCXXFLAGS"
            ac_ext=c
ac_cpp='$CPP $CPPFLAGS'
ac_compile='$CC -c $CFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CC -o conftest$ac_exeext $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_c_compiler_gnu


fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_cxx_fvisibility_inlines" >&5
printf "%s\n" "$ac_cv_cxx_fvisibility_inlines" >&6; }
        if test x"$ac_cv_cxx_fvisibility_inlines" = xyes
then :

            CXXFLAGS="$CXXFLAGS -fvisibility-inlines-hidden"

fi

        if test "x$libdir" != "x/usr/lib" -a "x$libdir" != "x/usr/lib64"
then :

            DSOLINK="-Wl,-rpath,$libdir"

fi
     ;; #(
  aix*) :

        DSONAME="libfltk_s.a"
        FLDSONAME="libfltk_forms_s.a"
        GLDSONAME="libfltk_gl_s.a"
        IMGDSONAME="libfltk_images_s.a"
        CAIRODSONAME="libfltk_cairo_s.a"
        DSOCOMMAND="\$(CXX) \$(DSOFLAGS) -Wl,-bexpall,-bM:SRE,-bnoentry -o"
        SHAREDSUFFIX="_s"
     ;; #(
  cygwin* | mingw*) :

        PICFLAG=0
        if test x$enable_cygwin != xyes
then :

            DSONAME="mgwfltknox-$FL_DSO_VERSION.dll"
            FLDSONAME="mgwfltknox_forms-$FL_DSO_VERSION.dll"
            GLDSONAME="mgwfltknox_gl-$FL_DSO_VERSION.dll"
            IMGDSONAME="mgwfltknox_images-$FL_DSO_VERSION.dll"
            CAIRODSONAME="mgwfltknox_cairo-$FL_DSO_VERSION.dll"

elif test x$enable_x11 = xyes
then :

            DSONAME="cygfltk-$FL_DSO_VERSION.dll"
            FLDSONAME="cygfltk_forms-$FL_DSO_VERSION.dll"
            GLDSONAME="cygfltk_gl-$FL_DSO_VERSION.dll"
            IMGDSONAME="cygfltk_images-$FL_DSO_VERSION.dll"
            CAIRODSONAME="cygfltk_cairo-$FL_DSO_VERSION.dll"

else $as_nop

            DSONAME="cygfltknox-$FL_DSO_VERSION.dll"
            FLDSONAME="cygfltknox_forms-$FL_DSO_VERSION.dll"
            GLDSONAME="cygfltknox_gl-$FL_DSO_VERSION.dll"
            IMGDSONAME="cygfltknox_images-$FL_DSO_VERSION.dll"
            CAIRODSONAME="cygfltknox_cairo-$FL_DSO_VERSION.dll"

fi

        #-----------------------------------------------------------
        # -Wl,--enable-runtime-pseudo-reloc: See str 1585
        # appears to be necessary for older binutils versions < 2.16
        #-----------------------------------------------------------
        LDFLAGS="$LDFLAGS -Wl,--enable-auto-import -Wl,--enable-runtime-pseudo-reloc"
        DSOCOMMAND="\$(CXX) \$(DSOFLAGS) -shared -Wl,--whole-archive -Wl,--export-all-symbols  -Wl,--enable-runtime-pseudo-reloc -Wl,--enable-auto-import -Wl,--enable-auto-image-base -o \$@"
     ;; #(
  *) :

        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: Shared libraries may not be supported. Trying -shared option with compiler." >&5
printf "%s\n" "$as_me: WARNING: Shared libraries may not be supported. Trying -shared option with compiler." >&2;}
        DSONAME="libfltk.so.$FL_DSO_VERSION"
        FLDSONAME="libfltk_forms.so.$FL_DSO_VERSION"
        GLDSONAME="libfltk_gl.so.$FL_DSO_VERSION"
        IMGDSONAME="libfltk_images.so.$FL_DSO_VERSION"
        CAIRODSONAME="libfltk_cairo.so.$FL_DSO_VERSION"
        DSOCOMMAND="\$(CXX) \$(DSOFLAGS) -Wl,-soname,\$@ \$(LDLIBS) -shared $DEBUGFLAG -o"
     ;; #(
  *) :
     ;;
esac

    LINKSHARED="-L../src $FLTKCAIROOPTION -lfltk_images$SHAREDSUFFIX -lfltk$SHAREDSUFFIX"

else $as_nop

    DSOCOMMAND="echo"
    DSOLINK=""
    DSONAME=""
    FLDSONAME=""
    GLDSONAME=""
    IMGDSONAME=""
    CAIRODSONAME=""
    PICFLAG=0
    SHAREDSUFFIX=""
    FLUID="fluid"
    FLTK_OPTIONS="fltk-options"
    LINKSHARED="$LINKFLTKCAIRO ../lib/libfltk_images.a ../lib/libfltk.a"

fi

if test x$build_forms = xno
then :

    FLDSONAME=""

fi








if test "x$fltk_cross_compiling" = xyes
then :

    FLUID_BUILD="fluid"

else $as_nop

    FLUID_BUILD="../fluid/fluid$EXEEXT"

fi















  # Find a good install program.  We prefer a C program (faster),
# so one script is as good as another.  But avoid the broken or
# incompatible versions:
# SysV /etc/install, /usr/sbin/install
# SunOS /usr/etc/install
# IRIX /sbin/install
# AIX /bin/install
# AmigaOS /C/install, which installs bootblocks on floppy discs
# AIX 4 /usr/bin/installbsd, which doesn't work without a -g flag
# AFS /usr/afsws/bin/install, which mishandles nonexistent args
# SVR4 /usr/ucb/install, which tries to use the nonexistent group "staff"
# OS/2's system install, which has a completely different semantic
# ./install, which can be erroneously created by make from ./install.sh.
# Reject install programs that cannot install multiple files.
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for a BSD-compatible install" >&5
printf %s "checking for a BSD-compatible install... " >&6; }
if test -z "$INSTALL"; then
if test ${ac_cv_path_install+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    # Account for fact that we put trailing slashes in our PATH walk.
case $as_dir in #((
  ./ | /[cC]/* | \
  /etc/* | /usr/sbin/* | /usr/etc/* | /sbin/* | /usr/afsws/bin/* | \
  ?:[\\/]os2[\\/]install[\\/]* | ?:[\\/]OS2[\\/]INSTALL[\\/]* | \
  /usr/ucb/* ) ;;
  *)
    # OSF1 and SCO ODT 3.0 have their own names for install.
    # Don't use installbsd from OSF since it installs stuff as root
    # by default.
    for ac_prog in ginstall scoinst install; do
      for ac_exec_ext in '' $ac_executable_extensions; do
	if as_fn_executable_p "$as_dir$ac_prog$ac_exec_ext"; then
	  if test $ac_prog = install &&
	    grep dspmsg "$as_dir$ac_prog$ac_exec_ext" >/dev/null 2>&1; then
	    # AIX install.  It has an incompatible calling convention.
	    :
	  elif test $ac_prog = install &&
	    grep pwplus "$as_dir$ac_prog$ac_exec_ext" >/dev/null 2>&1; then
	    # program-specific install script used by HP pwplus--don't use.
	    :
	  else
	    rm -rf conftest.one conftest.two conftest.dir
	    echo one > conftest.one
	    echo two > conftest.two
	    mkdir conftest.dir
	    if "$as_dir$ac_prog$ac_exec_ext" -c conftest.one conftest.two "`pwd`/conftest.dir/" &&
	      test -s conftest.one && test -s conftest.two &&
	      test -s conftest.dir/conftest.one &&
	      test -s conftest.dir/conftest.two
	    then
	      ac_cv_path_install="$as_dir$ac_prog$ac_exec_ext -c"
	      break 3
	    fi
	  fi
	fi
      done
    done
    ;;
esac

  done
IFS=$as_save_IFS

rm -rf conftest.one conftest.two conftest.dir

fi
  if test ${ac_cv_path_install+y}; then
    INSTALL=$ac_cv_path_install
  else
    # As a last resort, use the slow shell script.  Don't cache a
    # value for INSTALL within a source directory, because that will
    # break other packages using the cache if that directory is
    # removed, or if the value is a relative name.
    INSTALL=$ac_install_sh
  fi
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $INSTALL" >&5
printf "%s\n" "$INSTALL" >&6; }

# Use test -z because SunOS4 sh mishandles braces in ${var-val}.
# It thinks the first close brace ends the variable substitution.
test -z "$INSTALL_PROGRAM" && INSTALL_PROGRAM='${INSTALL}'

test -z "$INSTALL_SCRIPT" && INSTALL_SCRIPT='${INSTALL}'

test -z "$INSTALL_DATA" && INSTALL_DATA='${INSTALL} -m 644'

case $host_os in #(
  osf*) :

    INSTALL="$(pwd)/install-sh -c"
 ;; #(
  *) :
     ;;
esac

if test "x$INSTALL" = "x$ac_install_sh"
then :

    # Use full path to install-sh script...
    INSTALL="$(pwd)/install-sh -c"

fi

for ac_prog in nroff groff
do
  # Extract the first word of "$ac_prog", so it can be a program name with args.
set dummy $ac_prog; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_path_NROFF+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  case $NROFF in
  [\\/]* | ?:[\\/]*)
  ac_cv_path_NROFF="$NROFF" # Let the user override the test with a path.
  ;;
  *)
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_path_NROFF="$as_dir$ac_word$ac_exec_ext"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

  ;;
esac
fi
NROFF=$ac_cv_path_NROFF
if test -n "$NROFF"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $NROFF" >&5
printf "%s\n" "$NROFF" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


  test -n "$NROFF" && break
done
test -n "$NROFF" || NROFF="echo"

# Extract the first word of "doxygen", so it can be a program name with args.
set dummy doxygen; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_path_DOXYDOC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  case $DOXYDOC in
  [\\/]* | ?:[\\/]*)
  ac_cv_path_DOXYDOC="$DOXYDOC" # Let the user override the test with a path.
  ;;
  *)
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_path_DOXYDOC="$as_dir$ac_word$ac_exec_ext"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

  ;;
esac
fi
DOXYDOC=$ac_cv_path_DOXYDOC
if test -n "$DOXYDOC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $DOXYDOC" >&5
printf "%s\n" "$DOXYDOC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi



if test -n "$ac_tool_prefix"; then
  # Extract the first word of "${ac_tool_prefix}ranlib", so it can be a program name with args.
set dummy ${ac_tool_prefix}ranlib; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_RANLIB+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$RANLIB"; then
  ac_cv_prog_RANLIB="$RANLIB" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_RANLIB="${ac_tool_prefix}ranlib"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
RANLIB=$ac_cv_prog_RANLIB
if test -n "$RANLIB"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $RANLIB" >&5
printf "%s\n" "$RANLIB" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$ac_cv_prog_RANLIB"; then
  ac_ct_RANLIB=$RANLIB
  # Extract the first word of "ranlib", so it can be a program name with args.
set dummy ranlib; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_prog_ac_ct_RANLIB+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if test -n "$ac_ct_RANLIB"; then
  ac_cv_prog_ac_ct_RANLIB="$ac_ct_RANLIB" # Let the user override the test.
else
as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_prog_ac_ct_RANLIB="ranlib"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

fi
fi
ac_ct_RANLIB=$ac_cv_prog_ac_ct_RANLIB
if test -n "$ac_ct_RANLIB"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_ct_RANLIB" >&5
printf "%s\n" "$ac_ct_RANLIB" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi

  if test "x$ac_ct_RANLIB" = x; then
    RANLIB=":"
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    RANLIB=$ac_ct_RANLIB
  fi
else
  RANLIB="$ac_cv_prog_RANLIB"
fi

if test -n "$ac_tool_prefix"; then
  # Extract the first word of "${ac_tool_prefix}ar", so it can be a program name with args.
set dummy ${ac_tool_prefix}ar; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_path_AR+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  case $AR in
  [\\/]* | ?:[\\/]*)
  ac_cv_path_AR="$AR" # Let the user override the test with a path.
  ;;
  *)
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_path_AR="$as_dir$ac_word$ac_exec_ext"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

  ;;
esac
fi
AR=$ac_cv_path_AR
if test -n "$AR"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $AR" >&5
printf "%s\n" "$AR" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$ac_cv_path_AR"; then
  ac_pt_AR=$AR
  # Extract the first word of "ar", so it can be a program name with args.
set dummy ar; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_path_ac_pt_AR+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  case $ac_pt_AR in
  [\\/]* | ?:[\\/]*)
  ac_cv_path_ac_pt_AR="$ac_pt_AR" # Let the user override the test with a path.
  ;;
  *)
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_path_ac_pt_AR="$as_dir$ac_word$ac_exec_ext"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

  ;;
esac
fi
ac_pt_AR=$ac_cv_path_ac_pt_AR
if test -n "$ac_pt_AR"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_pt_AR" >&5
printf "%s\n" "$ac_pt_AR" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi

  if test "x$ac_pt_AR" = x; then
    AR=""
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    AR=$ac_pt_AR
  fi
else
  AR="$ac_cv_path_AR"
fi


if test "x$AR" = "x:"
then :

    as_fn_error $? "Configure could not find the library archiver, aborting." "$LINENO" 5

fi

if test "x$RANLIB" != "x:"
then :

    LIBCOMMAND="$AR cr"

else $as_nop

    LIBCOMMAND="$AR crs"

fi



if test -n "$ac_tool_prefix"; then
  # Extract the first word of "${ac_tool_prefix}windres", so it can be a program name with args.
set dummy ${ac_tool_prefix}windres; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_path_RC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  case $RC in
  [\\/]* | ?:[\\/]*)
  ac_cv_path_RC="$RC" # Let the user override the test with a path.
  ;;
  *)
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_path_RC="$as_dir$ac_word$ac_exec_ext"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

  ;;
esac
fi
RC=$ac_cv_path_RC
if test -n "$RC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $RC" >&5
printf "%s\n" "$RC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


fi
if test -z "$ac_cv_path_RC"; then
  ac_pt_RC=$RC
  # Extract the first word of "windres", so it can be a program name with args.
set dummy windres; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_path_ac_pt_RC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  case $ac_pt_RC in
  [\\/]* | ?:[\\/]*)
  ac_cv_path_ac_pt_RC="$ac_pt_RC" # Let the user override the test with a path.
  ;;
  *)
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_path_ac_pt_RC="$as_dir$ac_word$ac_exec_ext"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

  ;;
esac
fi
ac_pt_RC=$ac_cv_path_ac_pt_RC
if test -n "$ac_pt_RC"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_pt_RC" >&5
printf "%s\n" "$ac_pt_RC" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi

  if test "x$ac_pt_RC" = x; then
    RC=""
  else
    case $cross_compiling:$ac_tool_warned in
yes:)
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: using cross tools not prefixed with host triplet" >&5
printf "%s\n" "$as_me: WARNING: using cross tools not prefixed with host triplet" >&2;}
ac_tool_warned=yes ;;
esac
    RC=$ac_pt_RC
  fi
else
  RC="$ac_cv_path_RC"
fi


ac_header= ac_cache=
for ac_item in $ac_header_c_list
do
  if test $ac_cache; then
    ac_fn_c_check_header_compile "$LINENO" $ac_header ac_cv_header_$ac_cache "$ac_includes_default"
    if eval test \"x\$ac_cv_header_$ac_cache\" = xyes; then
      printf "%s\n" "#define $ac_item 1" >> confdefs.h
    fi
    ac_header= ac_cache=
  elif test $ac_header; then
    ac_cache=$ac_item
  else
    ac_header=$ac_item
  fi
done








if test $ac_cv_header_stdlib_h = yes && test $ac_cv_header_string_h = yes
then :

printf "%s\n" "#define STDC_HEADERS 1" >>confdefs.h

fi
# The cast to long int works around a bug in the HP C Compiler
# version HP92453-01 B.11.11.23709.GP, which incorrectly rejects
# declarations like `int a3[[(sizeof (unsigned char)) >= 0]];'.
# This bug is HP SR number 8606223364.
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking size of short" >&5
printf %s "checking size of short... " >&6; }
if test ${ac_cv_sizeof_short+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if ac_fn_c_compute_int "$LINENO" "(long int) (sizeof (short))" "ac_cv_sizeof_short"        "$ac_includes_default"
then :

else $as_nop
  if test "$ac_cv_type_short" = yes; then
     { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error 77 "cannot compute sizeof (short)
See \`config.log' for more details" "$LINENO" 5; }
   else
     ac_cv_sizeof_short=0
   fi
fi

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_sizeof_short" >&5
printf "%s\n" "$ac_cv_sizeof_short" >&6; }



printf "%s\n" "#define SIZEOF_SHORT $ac_cv_sizeof_short" >>confdefs.h


# The cast to long int works around a bug in the HP C Compiler
# version HP92453-01 B.11.11.23709.GP, which incorrectly rejects
# declarations like `int a3[[(sizeof (unsigned char)) >= 0]];'.
# This bug is HP SR number 8606223364.
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking size of int" >&5
printf %s "checking size of int... " >&6; }
if test ${ac_cv_sizeof_int+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if ac_fn_c_compute_int "$LINENO" "(long int) (sizeof (int))" "ac_cv_sizeof_int"        "$ac_includes_default"
then :

else $as_nop
  if test "$ac_cv_type_int" = yes; then
     { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error 77 "cannot compute sizeof (int)
See \`config.log' for more details" "$LINENO" 5; }
   else
     ac_cv_sizeof_int=0
   fi
fi

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_sizeof_int" >&5
printf "%s\n" "$ac_cv_sizeof_int" >&6; }



printf "%s\n" "#define SIZEOF_INT $ac_cv_sizeof_int" >>confdefs.h


# The cast to long int works around a bug in the HP C Compiler
# version HP92453-01 B.11.11.23709.GP, which incorrectly rejects
# declarations like `int a3[[(sizeof (unsigned char)) >= 0]];'.
# This bug is HP SR number 8606223364.
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking size of long" >&5
printf %s "checking size of long... " >&6; }
if test ${ac_cv_sizeof_long+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  if ac_fn_c_compute_int "$LINENO" "(long int) (sizeof (long))" "ac_cv_sizeof_long"        "$ac_includes_default"
then :

else $as_nop
  if test "$ac_cv_type_long" = yes; then
     { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error 77 "cannot compute sizeof (long)
See \`config.log' for more details" "$LINENO" 5; }
   else
     ac_cv_sizeof_long=0
   fi
fi

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_sizeof_long" >&5
printf "%s\n" "$ac_cv_sizeof_long" >&6; }



printf "%s\n" "#define SIZEOF_LONG $ac_cv_sizeof_long" >>confdefs.h


if test $ac_cv_sizeof_short -eq 2
then :

    printf "%s\n" "#define U16 unsigned short" >>confdefs.h


fi
if test $ac_cv_sizeof_int -eq 4
then :

    printf "%s\n" "#define U32 unsigned" >>confdefs.h


elif test $ac_cv_sizeof_long -eq 4
then :

    printf "%s\n" "#define U32 unsigned long" >>confdefs.h


fi

case $host_os in #(
  darwin*) :

 ;; #(
  *) :

     { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether byte ordering is bigendian" >&5
printf %s "checking whether byte ordering is bigendian... " >&6; }
if test ${ac_cv_c_bigendian+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_cv_c_bigendian=unknown
    # See if we're dealing with a universal compiler.
    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#ifndef __APPLE_CC__
	       not a universal capable compiler
	     #endif
	     typedef int dummy;

_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :

	# Check for potential -arch flags.  It is not universal unless
	# there are at least two -arch flags with different values.
	ac_arch=
	ac_prev=
	for ac_word in $CC $CFLAGS $CPPFLAGS $LDFLAGS; do
	 if test -n "$ac_prev"; then
	   case $ac_word in
	     i?86 | x86_64 | ppc | ppc64)
	       if test -z "$ac_arch" || test "$ac_arch" = "$ac_word"; then
		 ac_arch=$ac_word
	       else
		 ac_cv_c_bigendian=universal
		 break
	       fi
	       ;;
	   esac
	   ac_prev=
	 elif test "x$ac_word" = "x-arch"; then
	   ac_prev=arch
	 fi
       done
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
    if test $ac_cv_c_bigendian = unknown; then
      # See if sys/param.h defines the BYTE_ORDER macro.
      cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <sys/types.h>
	     #include <sys/param.h>

int
main (void)
{
#if ! (defined BYTE_ORDER && defined BIG_ENDIAN \
		     && defined LITTLE_ENDIAN && BYTE_ORDER && BIG_ENDIAN \
		     && LITTLE_ENDIAN)
	      bogus endian macros
	     #endif

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  # It does; now see whether it defined to BIG_ENDIAN or not.
	 cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <sys/types.h>
		#include <sys/param.h>

int
main (void)
{
#if BYTE_ORDER != BIG_ENDIAN
		 not big endian
		#endif

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_c_bigendian=yes
else $as_nop
  ac_cv_c_bigendian=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
    fi
    if test $ac_cv_c_bigendian = unknown; then
      # See if <limits.h> defines _LITTLE_ENDIAN or _BIG_ENDIAN (e.g., Solaris).
      cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <limits.h>

int
main (void)
{
#if ! (defined _LITTLE_ENDIAN || defined _BIG_ENDIAN)
	      bogus endian macros
	     #endif

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  # It does; now see whether it defined to _BIG_ENDIAN or not.
	 cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <limits.h>

int
main (void)
{
#ifndef _BIG_ENDIAN
		 not big endian
		#endif

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_c_bigendian=yes
else $as_nop
  ac_cv_c_bigendian=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
    fi
    if test $ac_cv_c_bigendian = unknown; then
      # Compile a test program.
      if test "$cross_compiling" = yes
then :
  # Try to guess by grepping values from an object file.
	 cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
unsigned short int ascii_mm[] =
		  { 0x4249, 0x4765, 0x6E44, 0x6961, 0x6E53, 0x7953, 0 };
		unsigned short int ascii_ii[] =
		  { 0x694C, 0x5454, 0x656C, 0x6E45, 0x6944, 0x6E61, 0 };
		int use_ascii (int i) {
		  return ascii_mm[i] + ascii_ii[i];
		}
		unsigned short int ebcdic_ii[] =
		  { 0x89D3, 0xE3E3, 0x8593, 0x95C5, 0x89C4, 0x9581, 0 };
		unsigned short int ebcdic_mm[] =
		  { 0xC2C9, 0xC785, 0x95C4, 0x8981, 0x95E2, 0xA8E2, 0 };
		int use_ebcdic (int i) {
		  return ebcdic_mm[i] + ebcdic_ii[i];
		}
		extern int foo;

int
main (void)
{
return use_ascii (foo) == use_ebcdic (foo);
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  if grep BIGenDianSyS conftest.$ac_objext >/dev/null; then
	      ac_cv_c_bigendian=yes
	    fi
	    if grep LiTTleEnDian conftest.$ac_objext >/dev/null ; then
	      if test "$ac_cv_c_bigendian" = unknown; then
		ac_cv_c_bigendian=no
	      else
		# finding both strings is unlikely to happen, but who knows?
		ac_cv_c_bigendian=unknown
	      fi
	    fi
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
$ac_includes_default
int
main (void)
{

	     /* Are we little or big endian?  From Harbison&Steele.  */
	     union
	     {
	       long int l;
	       char c[sizeof (long int)];
	     } u;
	     u.l = 1;
	     return u.c[sizeof (long int) - 1] == 1;

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_run "$LINENO"
then :
  ac_cv_c_bigendian=no
else $as_nop
  ac_cv_c_bigendian=yes
fi
rm -f core *.core core.conftest.* gmon.out bb.out conftest$ac_exeext \
  conftest.$ac_objext conftest.beam conftest.$ac_ext
fi

    fi
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_c_bigendian" >&5
printf "%s\n" "$ac_cv_c_bigendian" >&6; }
 case $ac_cv_c_bigendian in #(
   yes)
     printf "%s\n" "#define WORDS_BIGENDIAN 1" >>confdefs.h
;; #(
   no)
      ;; #(
   universal)

printf "%s\n" "#define AC_APPLE_UNIVERSAL_BUILD 1" >>confdefs.h

     ;; #(
   *)
     as_fn_error $? "unknown endianness
 presetting ac_cv_c_bigendian=no (or yes) will help" "$LINENO" 5 ;;
 esac


    if test $ac_cv_sizeof_int -eq 8
then :

        printf "%s\n" "#define U64 unsigned" >>confdefs.h


elif test $ac_cv_sizeof_long -eq 8
then :

        printf "%s\n" "#define U64 unsigned long" >>confdefs.h


fi
 ;; #(
  *) :
     ;;
esac


ac_header_dirent=no
for ac_hdr in dirent.h sys/ndir.h sys/dir.h ndir.h; do
  as_ac_Header=`printf "%s\n" "ac_cv_header_dirent_$ac_hdr" | $as_tr_sh`
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_hdr that defines DIR" >&5
printf %s "checking for $ac_hdr that defines DIR... " >&6; }
if eval test \${$as_ac_Header+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <sys/types.h>
#include <$ac_hdr>

int
main (void)
{
if ((DIR *) 0)
return 0;
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  eval "$as_ac_Header=yes"
else $as_nop
  eval "$as_ac_Header=no"
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
fi
eval ac_res=\$$as_ac_Header
	       { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_res" >&5
printf "%s\n" "$ac_res" >&6; }
if eval test \"x\$"$as_ac_Header"\" = x"yes"
then :
  cat >>confdefs.h <<_ACEOF
#define `printf "%s\n" "HAVE_$ac_hdr" | $as_tr_cpp` 1
_ACEOF

ac_header_dirent=$ac_hdr; break
fi

done
# Two versions of opendir et al. are in -ldir and -lx on SCO Xenix.
if test $ac_header_dirent = dirent.h; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for library containing opendir" >&5
printf %s "checking for library containing opendir... " >&6; }
if test ${ac_cv_search_opendir+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_func_search_save_LIBS=$LIBS
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char opendir ();
int
main (void)
{
return opendir ();
  ;
  return 0;
}
_ACEOF
for ac_lib in '' dir
do
  if test -z "$ac_lib"; then
    ac_res="none required"
  else
    ac_res=-l$ac_lib
    LIBS="-l$ac_lib  $ac_func_search_save_LIBS"
  fi
  if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_search_opendir=$ac_res
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext
  if test ${ac_cv_search_opendir+y}
then :
  break
fi
done
if test ${ac_cv_search_opendir+y}
then :

else $as_nop
  ac_cv_search_opendir=no
fi
rm conftest.$ac_ext
LIBS=$ac_func_search_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_search_opendir" >&5
printf "%s\n" "$ac_cv_search_opendir" >&6; }
ac_res=$ac_cv_search_opendir
if test "$ac_res" != no
then :
  test "$ac_res" = "none required" || LIBS="$ac_res $LIBS"

fi

else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for library containing opendir" >&5
printf %s "checking for library containing opendir... " >&6; }
if test ${ac_cv_search_opendir+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_func_search_save_LIBS=$LIBS
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char opendir ();
int
main (void)
{
return opendir ();
  ;
  return 0;
}
_ACEOF
for ac_lib in '' x
do
  if test -z "$ac_lib"; then
    ac_res="none required"
  else
    ac_res=-l$ac_lib
    LIBS="-l$ac_lib  $ac_func_search_save_LIBS"
  fi
  if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_search_opendir=$ac_res
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext
  if test ${ac_cv_search_opendir+y}
then :
  break
fi
done
if test ${ac_cv_search_opendir+y}
then :

else $as_nop
  ac_cv_search_opendir=no
fi
rm conftest.$ac_ext
LIBS=$ac_func_search_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_search_opendir" >&5
printf "%s\n" "$ac_cv_search_opendir" >&6; }
ac_res=$ac_cv_search_opendir
if test "$ac_res" != no
then :
  test "$ac_res" = "none required" || LIBS="$ac_res $LIBS"

fi

fi

ac_fn_c_check_header_compile "$LINENO" "sys/select.h" "ac_cv_header_sys_select_h" "$ac_includes_default"
if test "x$ac_cv_header_sys_select_h" = xyes
then :
  printf "%s\n" "#define HAVE_SYS_SELECT_H 1" >>confdefs.h

fi
ac_fn_c_check_header_compile "$LINENO" "sys/stdtypes.h" "ac_cv_header_sys_stdtypes_h" "$ac_includes_default"
if test "x$ac_cv_header_sys_stdtypes_h" = xyes
then :
  printf "%s\n" "#define HAVE_SYS_STDTYPES_H 1" >>confdefs.h

fi


{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether we have the POSIX compatible scandir() prototype" >&5
printf %s "checking whether we have the POSIX compatible scandir() prototype... " >&6; }
if test ${ac_cv_cxx_scandir_posix+y}
then :
  printf %s "(cached) " >&6
else $as_nop

    ac_ext=cpp
ac_cpp='$CXXCPP $CPPFLAGS'
ac_compile='$CXX -c $CXXFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CXX -o conftest$ac_exeext $CXXFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_cxx_compiler_gnu

    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <dirent.h>
              int func (const char *d, dirent ***list, void *sort) {
                int n = scandir(d, list, 0, (int(*)(const dirent **, const dirent **))sort);
                return n;
              }

int
main (void)
{


  ;
  return 0;
}
_ACEOF
if ac_fn_cxx_try_compile "$LINENO"
then :
  ac_cv_cxx_scandir_posix=yes
else $as_nop
  ac_cv_cxx_scandir_posix=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
    ac_ext=c
ac_cpp='$CPP $CPPFLAGS'
ac_compile='$CC -c $CFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CC -o conftest$ac_exeext $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_c_compiler_gnu


fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_cxx_scandir_posix" >&5
printf "%s\n" "$ac_cv_cxx_scandir_posix" >&6; }

if test "x$ac_cv_cxx_scandir_posix" = xyes
then :

    printf "%s\n" "#define HAVE_SCANDIR 1" >>confdefs.h

    printf "%s\n" "#define HAVE_SCANDIR_POSIX 1" >>confdefs.h


else $as_nop

    ac_fn_c_check_func "$LINENO" "scandir" "ac_cv_func_scandir"
if test "x$ac_cv_func_scandir" = xyes
then :

        case $host_os in #(
  solaris* | *qnx*) :

            { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: Not using $host_os scandir emulation function." >&5
printf "%s\n" "$as_me: WARNING: Not using $host_os scandir emulation function." >&2;}
         ;; #(
  *) :

            printf "%s\n" "#define HAVE_SCANDIR 1" >>confdefs.h

         ;; #(
  *) :
     ;;
esac

fi


fi

ac_fn_c_check_func "$LINENO" "vsnprintf" "ac_cv_func_vsnprintf"
if test "x$ac_cv_func_vsnprintf" = xyes
then :

    case $host_os in #(
  hpux1020) :

        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: Not using built-in vsnprintf function because you are running HP-UX 10.20." >&5
printf "%s\n" "$as_me: WARNING: Not using built-in vsnprintf function because you are running HP-UX 10.20." >&2;}
     ;; #(
  osf4) :

        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: Not using built-in vsnprintf function because you are running Tru64 4.0." >&5
printf "%s\n" "$as_me: WARNING: Not using built-in vsnprintf function because you are running Tru64 4.0." >&2;}
     ;; #(
  *) :

        printf "%s\n" "#define HAVE_VSNPRINTF 1" >>confdefs.h

     ;; #(
  *) :
     ;;
esac

fi


ac_fn_c_check_func "$LINENO" "snprintf" "ac_cv_func_snprintf"
if test "x$ac_cv_func_snprintf" = xyes
then :

    case $host_os in #(
  hpux1020) :

        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: Not using built-in snprintf function because you are running HP-UX 10.20." >&5
printf "%s\n" "$as_me: WARNING: Not using built-in snprintf function because you are running HP-UX 10.20." >&2;}
     ;; #(
  osf4) :

        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: Not using built-in snprintf function because you are running Tru64 4.0." >&5
printf "%s\n" "$as_me: WARNING: Not using built-in snprintf function because you are running Tru64 4.0." >&2;}
     ;; #(
  *) :

        printf "%s\n" "#define HAVE_SNPRINTF 1" >>confdefs.h

     ;; #(
  *) :
     ;;
esac

fi


ac_fn_c_check_header_compile "$LINENO" "strings.h" "ac_cv_header_strings_h" "$ac_includes_default"
if test "x$ac_cv_header_strings_h" = xyes
then :
  printf "%s\n" "#define HAVE_STRINGS_H 1" >>confdefs.h

fi

ac_fn_c_check_func "$LINENO" "strcasecmp" "ac_cv_func_strcasecmp"
if test "x$ac_cv_func_strcasecmp" = xyes
then :
  printf "%s\n" "#define HAVE_STRCASECMP 1" >>confdefs.h

fi
ac_fn_c_check_func "$LINENO" "strlcat" "ac_cv_func_strlcat"
if test "x$ac_cv_func_strlcat" = xyes
then :
  printf "%s\n" "#define HAVE_STRLCAT 1" >>confdefs.h

fi
ac_fn_c_check_func "$LINENO" "strlcpy" "ac_cv_func_strlcpy"
if test "x$ac_cv_func_strlcpy" = xyes
then :
  printf "%s\n" "#define HAVE_STRLCPY 1" >>confdefs.h

fi


ac_fn_c_check_header_compile "$LINENO" "locale.h" "ac_cv_header_locale_h" "$ac_includes_default"
if test "x$ac_cv_header_locale_h" = xyes
then :
  printf "%s\n" "#define HAVE_LOCALE_H 1" >>confdefs.h

fi

ac_fn_c_check_func "$LINENO" "localeconv" "ac_cv_func_localeconv"
if test "x$ac_cv_func_localeconv" = xyes
then :
  printf "%s\n" "#define HAVE_LOCALECONV 1" >>confdefs.h

fi


ac_fn_c_check_func "$LINENO" "setenv" "ac_cv_func_setenv"
if test "x$ac_cv_func_setenv" = xyes
then :
  printf "%s\n" "#define HAVE_SETENV 1" >>confdefs.h

fi



{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for library containing pow" >&5
printf %s "checking for library containing pow... " >&6; }
if test ${ac_cv_search_pow+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_func_search_save_LIBS=$LIBS
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char pow ();
int
main (void)
{
return pow ();
  ;
  return 0;
}
_ACEOF
for ac_lib in '' m
do
  if test -z "$ac_lib"; then
    ac_res="none required"
  else
    ac_res=-l$ac_lib
    LIBS="-l$ac_lib  $ac_func_search_save_LIBS"
  fi
  if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_search_pow=$ac_res
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext
  if test ${ac_cv_search_pow+y}
then :
  break
fi
done
if test ${ac_cv_search_pow+y}
then :

else $as_nop
  ac_cv_search_pow=no
fi
rm conftest.$ac_ext
LIBS=$ac_func_search_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_search_pow" >&5
printf "%s\n" "$ac_cv_search_pow" >&6; }
ac_res=$ac_cv_search_pow
if test "$ac_res" != no
then :
  test "$ac_res" = "none required" || LIBS="$ac_res $LIBS"

fi


ac_fn_c_check_header_compile "$LINENO" "math.h" "ac_cv_header_math_h" "$ac_includes_default"
if test "x$ac_cv_header_math_h" = xyes
then :
  printf "%s\n" "#define HAVE_MATH_H 1" >>confdefs.h

fi

ac_fn_c_check_func "$LINENO" "trunc" "ac_cv_func_trunc"
if test "x$ac_cv_func_trunc" = xyes
then :
  printf "%s\n" "#define HAVE_TRUNC 1" >>confdefs.h

fi



# Check whether --enable-largefile was given.
if test ${enable_largefile+y}
then :
  enableval=$enable_largefile;
fi

if test "$enable_largefile" != no; then

  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for special C compiler options needed for large files" >&5
printf %s "checking for special C compiler options needed for large files... " >&6; }
if test ${ac_cv_sys_largefile_CC+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_cv_sys_largefile_CC=no
     if test "$GCC" != yes; then
       ac_save_CC=$CC
       while :; do
	 # IRIX 6.2 and later do not support large files by default,
	 # so use the C compiler's -n32 option if that helps.
	 cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <sys/types.h>
 /* Check that off_t can represent 2**63 - 1 correctly.
    We can't simply define LARGE_OFF_T to be 9223372036854775807,
    since some C++ compilers masquerading as C compilers
    incorrectly reject 9223372036854775807.  */
#define LARGE_OFF_T (((off_t) 1 << 31 << 31) - 1 + ((off_t) 1 << 31 << 31))
  int off_t_is_large[(LARGE_OFF_T % 2147483629 == 721
		       && LARGE_OFF_T % 2147483647 == 1)
		      ? 1 : -1];
int
main (void)
{

  ;
  return 0;
}
_ACEOF
	 if ac_fn_c_try_compile "$LINENO"
then :
  break
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam
	 CC="$CC -n32"
	 if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_sys_largefile_CC=' -n32'; break
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam
	 break
       done
       CC=$ac_save_CC
       rm -f conftest.$ac_ext
    fi
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_sys_largefile_CC" >&5
printf "%s\n" "$ac_cv_sys_largefile_CC" >&6; }
  if test "$ac_cv_sys_largefile_CC" != no; then
    CC=$CC$ac_cv_sys_largefile_CC
  fi

  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for _FILE_OFFSET_BITS value needed for large files" >&5
printf %s "checking for _FILE_OFFSET_BITS value needed for large files... " >&6; }
if test ${ac_cv_sys_file_offset_bits+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  while :; do
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <sys/types.h>
 /* Check that off_t can represent 2**63 - 1 correctly.
    We can't simply define LARGE_OFF_T to be 9223372036854775807,
    since some C++ compilers masquerading as C compilers
    incorrectly reject 9223372036854775807.  */
#define LARGE_OFF_T (((off_t) 1 << 31 << 31) - 1 + ((off_t) 1 << 31 << 31))
  int off_t_is_large[(LARGE_OFF_T % 2147483629 == 721
		       && LARGE_OFF_T % 2147483647 == 1)
		      ? 1 : -1];
int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_sys_file_offset_bits=no; break
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#define _FILE_OFFSET_BITS 64
#include <sys/types.h>
 /* Check that off_t can represent 2**63 - 1 correctly.
    We can't simply define LARGE_OFF_T to be 9223372036854775807,
    since some C++ compilers masquerading as C compilers
    incorrectly reject 9223372036854775807.  */
#define LARGE_OFF_T (((off_t) 1 << 31 << 31) - 1 + ((off_t) 1 << 31 << 31))
  int off_t_is_large[(LARGE_OFF_T % 2147483629 == 721
		       && LARGE_OFF_T % 2147483647 == 1)
		      ? 1 : -1];
int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_sys_file_offset_bits=64; break
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
  ac_cv_sys_file_offset_bits=unknown
  break
done
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_sys_file_offset_bits" >&5
printf "%s\n" "$ac_cv_sys_file_offset_bits" >&6; }
case $ac_cv_sys_file_offset_bits in #(
  no | unknown) ;;
  *)
printf "%s\n" "#define _FILE_OFFSET_BITS $ac_cv_sys_file_offset_bits" >>confdefs.h
;;
esac
rm -rf conftest*
  if test $ac_cv_sys_file_offset_bits = unknown; then
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for _LARGE_FILES value needed for large files" >&5
printf %s "checking for _LARGE_FILES value needed for large files... " >&6; }
if test ${ac_cv_sys_large_files+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  while :; do
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <sys/types.h>
 /* Check that off_t can represent 2**63 - 1 correctly.
    We can't simply define LARGE_OFF_T to be 9223372036854775807,
    since some C++ compilers masquerading as C compilers
    incorrectly reject 9223372036854775807.  */
#define LARGE_OFF_T (((off_t) 1 << 31 << 31) - 1 + ((off_t) 1 << 31 << 31))
  int off_t_is_large[(LARGE_OFF_T % 2147483629 == 721
		       && LARGE_OFF_T % 2147483647 == 1)
		      ? 1 : -1];
int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_sys_large_files=no; break
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#define _LARGE_FILES 1
#include <sys/types.h>
 /* Check that off_t can represent 2**63 - 1 correctly.
    We can't simply define LARGE_OFF_T to be 9223372036854775807,
    since some C++ compilers masquerading as C compilers
    incorrectly reject 9223372036854775807.  */
#define LARGE_OFF_T (((off_t) 1 << 31 << 31) - 1 + ((off_t) 1 << 31 << 31))
  int off_t_is_large[(LARGE_OFF_T % 2147483629 == 721
		       && LARGE_OFF_T % 2147483647 == 1)
		      ? 1 : -1];
int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_sys_large_files=1; break
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
  ac_cv_sys_large_files=unknown
  break
done
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_sys_large_files" >&5
printf "%s\n" "$ac_cv_sys_large_files" >&6; }
case $ac_cv_sys_large_files in #(
  no | unknown) ;;
  *)
printf "%s\n" "#define _LARGE_FILES $ac_cv_sys_large_files" >>confdefs.h
;;
esac
rm -rf conftest*
  fi
fi


LARGEFILE=""
if test x$enable_largefile != xno
then :

    LARGEFILE="-D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE"

    if test x$ac_cv_sys_large_files = x1
then :

        LARGEFILE="$LARGEFILE -D_LARGE_FILES"

fi

    if test x$ac_cv_sys_file_offset_bits = x64
then :

        LARGEFILE="$LARGEFILE -D_FILE_OFFSET_BITS=64"

fi

fi



{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for long long int" >&5
printf %s "checking for long long int... " >&6; }
if test ${ac_cv_c_long_long+y}
then :
  printf %s "(cached) " >&6
else $as_nop

    if test "$GCC" = yes
then :

        ac_cv_c_long_long=yes

else $as_nop

        cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{
long long int i;
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_c_long_long=yes
else $as_nop
  ac_cv_c_long_long=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext

fi

fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_c_long_long" >&5
printf "%s\n" "$ac_cv_c_long_long" >&6; }

if test $ac_cv_c_long_long = yes
then :

    printf "%s\n" "#define HAVE_LONG_LONG 1" >>confdefs.h


fi


{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for library containing dlsym" >&5
printf %s "checking for library containing dlsym... " >&6; }
if test ${ac_cv_search_dlsym+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_func_search_save_LIBS=$LIBS
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char dlsym ();
int
main (void)
{
return dlsym ();
  ;
  return 0;
}
_ACEOF
for ac_lib in '' dl
do
  if test -z "$ac_lib"; then
    ac_res="none required"
  else
    ac_res=-l$ac_lib
    LIBS="-l$ac_lib  $ac_func_search_save_LIBS"
  fi
  if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_search_dlsym=$ac_res
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext
  if test ${ac_cv_search_dlsym+y}
then :
  break
fi
done
if test ${ac_cv_search_dlsym+y}
then :

else $as_nop
  ac_cv_search_dlsym=no
fi
rm conftest.$ac_ext
LIBS=$ac_func_search_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_search_dlsym" >&5
printf "%s\n" "$ac_cv_search_dlsym" >&6; }
ac_res=$ac_cv_search_dlsym
if test "$ac_res" != no
then :
  test "$ac_res" = "none required" || LIBS="$ac_res $LIBS"
  printf "%s\n" "#define HAVE_DLSYM 1" >>confdefs.h

fi

ac_fn_c_check_header_compile "$LINENO" "dlfcn.h" "ac_cv_header_dlfcn_h" "$ac_includes_default"
if test "x$ac_cv_header_dlfcn_h" = xyes
then :
  printf "%s\n" "#define HAVE_DLFCN_H 1" >>confdefs.h

fi



AUDIOLIBS=""

case $host_os in #(
  cygwin* | mingw*) :

        AUDIOLIBS="-lwinmm"
 ;; #(
  darwin*) :

    AUDIOLIBS="-framework CoreAudio"
 ;; #(
  *) :

    ac_fn_c_check_header_compile "$LINENO" "alsa/asoundlib.h" "ac_cv_header_alsa_asoundlib_h" "$ac_includes_default"
if test "x$ac_cv_header_alsa_asoundlib_h" = xyes
then :

        printf "%s\n" "#define HAVE_ALSA_ASOUNDLIB_H 1" >>confdefs.h

        AUDIOLIBS="-lasound"

fi

 ;; #(
  *) :
     ;;
esac




SAVELIBS="$LIBS"
IMAGELIBS=""
STATICIMAGELIBS=""





# Handle the JPEG lib linking mode (use fltk local or system lib)
# If --enable-(resp. --disable-)localjpeg parameter is not set by user
# Then we check the JPEG lib usability, with result in sysjpeglib_ok variable
# Check for System lib use if automatic mode or --disable-localjpeg is requested
sysjpeglib_ok=no
sysjpeginc_ok=no
if test x$enable_localjpeg != xyes
then :

    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for jpeg_CreateCompress in -ljpeg" >&5
printf %s "checking for jpeg_CreateCompress in -ljpeg... " >&6; }
if test ${ac_cv_lib_jpeg_jpeg_CreateCompress+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-ljpeg  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char jpeg_CreateCompress ();
int
main (void)
{
return jpeg_CreateCompress ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_jpeg_jpeg_CreateCompress=yes
else $as_nop
  ac_cv_lib_jpeg_jpeg_CreateCompress=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_jpeg_jpeg_CreateCompress" >&5
printf "%s\n" "$ac_cv_lib_jpeg_jpeg_CreateCompress" >&6; }
if test "x$ac_cv_lib_jpeg_jpeg_CreateCompress" = xyes
then :

        ac_fn_c_check_header_compile "$LINENO" "jpeglib.h" "ac_cv_header_jpeglib_h" "$ac_includes_default"
if test "x$ac_cv_header_jpeglib_h" = xyes
then :
  sysjpeginc_ok=yes
fi

        if test x$sysjpeginc_ok = xyes
then :

            sysjpeglib_ok=yes

fi

fi


fi

# Now set the jpeg lib and include flags according to the requested mode and availability
if test x$enable_localjpeg = xyes -o x$sysjpeglib_ok = xno
then :

    JPEGINC="-I../jpeg"
    JPEG="jpeg"
    IMAGELIBS="-lfltk_jpeg $IMAGELIBS"
    STATICIMAGELIBS="\$libdir/libfltk_jpeg.a $STATICIMAGELIBS"
    printf "%s\n" "#define HAVE_LIBJPEG 1" >>confdefs.h

    # Finally, warn user if system lib was requested but not found
    if test x$enable_localjpeg = xno
then :

        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: Cannot find system jpeg lib or header: choosing the local lib mode." >&5
printf "%s\n" "$as_me: WARNING: Cannot find system jpeg lib or header: choosing the local lib mode." >&2;}

fi

else $as_nop

    JPEGINC=""
    JPEG=""
    IMAGELIBS="-ljpeg $IMAGELIBS"
    STATICIMAGELIBS="-ljpeg $STATICIMAGELIBS"
    printf "%s\n" "#define HAVE_LIBJPEG 1" >>confdefs.h


fi


# Handle the ZLIB lib linking mode (use fltk local or system lib)
# If --enable-(resp. --disable-)localzlib parameter is not set by user
# Then we check the ZLIB lib usability, with result in syszlib_ok variable
# Check for System lib use if automatic mode or --disable-localzlib is requested
syszlib_ok=no
syszinc_ok=no
if test x$enable_localzlib != xyes
then :

    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for gzgets in -lz" >&5
printf %s "checking for gzgets in -lz... " >&6; }
if test ${ac_cv_lib_z_gzgets+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lz  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char gzgets ();
int
main (void)
{
return gzgets ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_z_gzgets=yes
else $as_nop
  ac_cv_lib_z_gzgets=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_z_gzgets" >&5
printf "%s\n" "$ac_cv_lib_z_gzgets" >&6; }
if test "x$ac_cv_lib_z_gzgets" = xyes
then :

        ac_fn_c_check_header_compile "$LINENO" "zlib.h" "ac_cv_header_zlib_h" "$ac_includes_default"
if test "x$ac_cv_header_zlib_h" = xyes
then :
  syszinc_ok=yes
fi

        if test x$syszinc_ok = xyes
then :

            syszlib_ok=yes

fi

fi


fi


# Handle the PNG lib linking mode (use fltk local or system lib)
# If --enable-(resp. --disable-)localpng parameter is not set by user
# Then we check the png lib usability with result in syspng_lib variable

# Now check if system lib is usable, we check Lib AND include availability with inc variant,
# but only, if the builtin lib is not requested
syspnglib_ok=no
syspnginc_ok=no
if test x$enable_localpng != xyes -a x$PKGCONFIG != x
then :

    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for libpng-1.6.x" >&5
printf %s "checking for libpng-1.6.x... " >&6; }
    if $PKGCONFIG --exists libpng16
then :

        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }

printf "%s\n" "#define HAVE_PNG_H 1" >>confdefs.h

        syspnginc_ok=yes
        syspnglib_ok=yes
        PNGINC="$($PKGCONFIG --cflags libpng16)"
        IMAGELIBS="$($PKGCONFIG --libs libpng16) $IMAGELIBS"
        STATICIMAGELIBS="$($PKGCONFIG --libs libpng16) $STATICIMAGELIBS"

else $as_nop

        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }

fi

fi

if test x$enable_localpng != xyes -a $syspnglib_ok = no
then :

    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for png_read_info in -lpng" >&5
printf %s "checking for png_read_info in -lpng... " >&6; }
if test ${ac_cv_lib_png_png_read_info+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lpng  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char png_read_info ();
int
main (void)
{
return png_read_info ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_png_png_read_info=yes
else $as_nop
  ac_cv_lib_png_png_read_info=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_png_png_read_info" >&5
printf "%s\n" "$ac_cv_lib_png_png_read_info" >&6; }
if test "x$ac_cv_lib_png_png_read_info" = xyes
then :

        ac_fn_c_check_header_compile "$LINENO" "png.h" "ac_cv_header_png_h" "$ac_includes_default"
if test "x$ac_cv_header_png_h" = xyes
then :

            printf "%s\n" "#define HAVE_PNG_H 1" >>confdefs.h

            syspnginc_ok=yes

fi

        ac_fn_c_check_header_compile "$LINENO" "libpng/png.h" "ac_cv_header_libpng_png_h" "$ac_includes_default"
if test "x$ac_cv_header_libpng_png_h" = xyes
then :

            syspnginc_ok=yes

fi

        if test x$syspnginc_ok = xyes
then :

            syspnglib_ok=yes

fi

fi


fi


# If we use the system zlib, we must also use the system png lib and vice versa.
# If either of them is not available, we fall back to using both local libraries
if test x$syspnglib_ok = xyes -a x$syszlib_ok != xyes
then :

    syspnglib_ok=no
    enable_localpng=yes
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: Local z lib selected: overriding png lib to local for compatibility." >&5
printf "%s\n" "$as_me: WARNING: Local z lib selected: overriding png lib to local for compatibility." >&2;}

fi
if test x$syszlib_ok = xyes -a x$syspnglib_ok != xyes
then :

    syszlib_ok=no
    enable_localzlib=yes
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: Local png lib selected: overriding z lib to local for compatibility." >&5
printf "%s\n" "$as_me: WARNING: Local png lib selected: overriding z lib to local for compatibility." >&2;}

fi


# Now set the Z lib and include flags according to the requested mode and availability
if test x$enable_localzlib = xyes -o x$syszlib_ok = xno
then :

    ZLIBINC="-I../zlib"
    ZLIB="zlib"
    LIBS="-lfltk_z $LIBS"
    IMAGELIBS="-lfltk_z $IMAGELIBS"
    STATICIMAGELIBS="\$libdir/libfltk_z.a $STATICIMAGELIBS"
    printf "%s\n" "#define HAVE_LIBZ 1" >>confdefs.h

    ac_cv_lib_z_gzgets=no # fc: is still necessary ?
    # Finally, warn user if system lib was requested but not found
    if test x$enable_localzlib = xno
then :

        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: Cannot find system z lib or header: choosing the local lib mode." >&5
printf "%s\n" "$as_me: WARNING: Cannot find system z lib or header: choosing the local lib mode." >&2;}

fi

else $as_nop

    ZLIBINC=""
    ZLIB=""
    LIBS="-lz $LIBS"
    IMAGELIBS="-lz $IMAGELIBS"
    STATICIMAGELIBS="-lz $STATICIMAGELIBS"
    printf "%s\n" "#define HAVE_LIBZ 1" >>confdefs.h


fi


# The following is executed if the png lib was not found usable or if local lib is required explicitly
if test x$enable_localpng = xyes -o x$syspnglib_ok = xno
then :

    PNGINC="-I../png"
    PNG="png"
    IMAGELIBS="-lfltk_png $IMAGELIBS"
    STATICIMAGELIBS="\$libdir/libfltk_png.a $STATICIMAGELIBS"
    printf "%s\n" "#define HAVE_LIBPNG 1" >>confdefs.h

    printf "%s\n" "#define HAVE_PNG_H 1" >>confdefs.h

    printf "%s\n" "#define HAVE_PNG_GET_VALID 1" >>confdefs.h

    printf "%s\n" "#define HAVE_PNG_SET_TRNS_TO_ALPHA 1" >>confdefs.h

    # Finally, warn user if system lib was requested but not found
    if test x$enable_localpng = xno
then :

        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: Cannot find system png lib or header: choosing the local lib mode." >&5
printf "%s\n" "$as_me: WARNING: Cannot find system png lib or header: choosing the local lib mode." >&2;}

fi

else $as_nop

    PNGINC=""
    PNG=""
    IMAGELIBS="-lpng $IMAGELIBS"
    STATICIMAGELIBS="-lpng $STATICIMAGELIBS"
    printf "%s\n" "#define HAVE_LIBPNG 1" >>confdefs.h

    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for png_get_valid in -lpng" >&5
printf %s "checking for png_get_valid in -lpng... " >&6; }
if test ${ac_cv_lib_png_png_get_valid+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lpng  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char png_get_valid ();
int
main (void)
{
return png_get_valid ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_png_png_get_valid=yes
else $as_nop
  ac_cv_lib_png_png_get_valid=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_png_png_get_valid" >&5
printf "%s\n" "$ac_cv_lib_png_png_get_valid" >&6; }
if test "x$ac_cv_lib_png_png_get_valid" = xyes
then :

        printf "%s\n" "#define HAVE_PNG_GET_VALID 1" >>confdefs.h


fi

    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for png_set_tRNS_to_alpha in -lpng" >&5
printf %s "checking for png_set_tRNS_to_alpha in -lpng... " >&6; }
if test ${ac_cv_lib_png_png_set_tRNS_to_alpha+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lpng  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char png_set_tRNS_to_alpha ();
int
main (void)
{
return png_set_tRNS_to_alpha ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_png_png_set_tRNS_to_alpha=yes
else $as_nop
  ac_cv_lib_png_png_set_tRNS_to_alpha=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_png_png_set_tRNS_to_alpha" >&5
printf "%s\n" "$ac_cv_lib_png_png_set_tRNS_to_alpha" >&6; }
if test "x$ac_cv_lib_png_png_set_tRNS_to_alpha" = xyes
then :

        printf "%s\n" "#define HAVE_PNG_SET_TRNS_TO_ALPHA 1" >>confdefs.h


fi


fi











# Control the usage of the nanosvg lib and SVG output

LIBS="$SAVELIBS"



have_pthread=no
PTHREAD_FLAGS=""

check_pthread=yes
case $host_os in #(
  mingw*) :

    check_pthread=no
 ;; #(
  cygwin*) :

    if test "x$enable_cygwin" != xyes
then :

        check_pthread=no

fi
 ;; #(
  *) :
     ;;
esac

if test "x$enable_threads" != xno -a x$check_pthread = xyes
then :

    ac_fn_c_check_header_compile "$LINENO" "pthread.h" "ac_cv_header_pthread_h" "$ac_includes_default"
if test "x$ac_cv_header_pthread_h" = xyes
then :
  printf "%s\n" "#define HAVE_PTHREAD_H 1" >>confdefs.h

fi


    if test x$ac_cv_header_pthread_h = xyes
then :

                for flag in -lpthreads -lpthread -pthread; do
            { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for pthread_create using $flag" >&5
printf %s "checking for pthread_create using $flag... " >&6; }
            SAVELIBS="$LIBS"
            LIBS="$flag $LIBS"
            cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <pthread.h>
int
main (void)
{
pthread_create(0, 0, 0, 0);
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  have_pthread=yes
else $as_nop
  LIBS="$SAVELIBS"
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
            { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $have_pthread" >&5
printf "%s\n" "$have_pthread" >&6; }

            if test $have_pthread = yes
then :

                printf "%s\n" "#define HAVE_PTHREAD 1" >>confdefs.h

                PTHREAD_FLAGS="-D_THREAD_SAFE -D_REENTRANT"

                # Solaris requires -D_POSIX_PTHREAD_SEMANTICS to
                # be POSIX-compliant... :(
                case $host_os in #(
  solaris*) :

                    PTHREAD_FLAGS="$PTHREAD_FLAGS -D_POSIX_PTHREAD_SEMANTICS"
                 ;; #(
  *) :
     ;;
esac

fi
        done

                { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether we have PTHREAD_MUTEX_RECURSIVE" >&5
printf %s "checking whether we have PTHREAD_MUTEX_RECURSIVE... " >&6; }
if test ${ac_cv_pthread_mutex_recursive+y}
then :
  printf %s "(cached) " >&6
else $as_nop

            ac_ext=c
ac_cpp='$CPP $CPPFLAGS'
ac_compile='$CC -c $CFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CC -o conftest$ac_exeext $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_c_compiler_gnu

            cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <pthread.h>

int
main (void)
{

                        return PTHREAD_MUTEX_RECURSIVE;

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  ac_cv_pthread_mutex_recursive=yes
else $as_nop
  ac_cv_pthread_mutex_recursive=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
            ac_ext=c
ac_cpp='$CPP $CPPFLAGS'
ac_compile='$CC -c $CFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CC -o conftest$ac_exeext $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_c_compiler_gnu


fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_pthread_mutex_recursive" >&5
printf "%s\n" "$ac_cv_pthread_mutex_recursive" >&6; }
        if test x$ac_cv_pthread_mutex_recursive = xyes
then :

            printf "%s\n" "#define HAVE_PTHREAD_MUTEX_RECURSIVE 1" >>confdefs.h


fi

fi

fi





HLINKS=
OSX_ONLY=:
THREADS=
LIBDECORDIR=""
LIBDECORDBUS=""
FLUIDDIR=""


# Check whether --with-links was given.
if test ${with_links+y}
then :
  withval=$with_links;
fi


INSTALL_DESKTOP=""
UNINSTALL_DESKTOP=""

if test x$enable_fluid != xno
then :
  FLUIDDIR="fluid"
fi

if test x$enable_use_std = xyes
then :

    printf "%s\n" "#define FLTK_USE_STD 1" >>confdefs.h



fi


ac_ext=c
ac_cpp='$CPP $CPPFLAGS'
ac_compile='$CC -c $CFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CC -o conftest$ac_exeext $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_c_compiler_gnu
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking how to run the C preprocessor" >&5
printf %s "checking how to run the C preprocessor... " >&6; }
# On Suns, sometimes $CPP names a directory.
if test -n "$CPP" && test -d "$CPP"; then
  CPP=
fi
if test -z "$CPP"; then
  if test ${ac_cv_prog_CPP+y}
then :
  printf %s "(cached) " >&6
else $as_nop
      # Double quotes because $CC needs to be expanded
    for CPP in "$CC -E" "$CC -E -traditional-cpp" cpp /lib/cpp
    do
      ac_preproc_ok=false
for ac_c_preproc_warn_flag in '' yes
do
  # Use a header file that comes with gcc, so configuring glibc
  # with a fresh cross-compiler works.
  # On the NeXT, cc -E runs the code through the compiler's parser,
  # not just through cpp. "Syntax error" is here to catch this case.
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <limits.h>
		     Syntax error
_ACEOF
if ac_fn_c_try_cpp "$LINENO"
then :

else $as_nop
  # Broken: fails on valid input.
continue
fi
rm -f conftest.err conftest.i conftest.$ac_ext

  # OK, works on sane cases.  Now check whether nonexistent headers
  # can be detected and how.
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <ac_nonexistent.h>
_ACEOF
if ac_fn_c_try_cpp "$LINENO"
then :
  # Broken: success on invalid input.
continue
else $as_nop
  # Passes both tests.
ac_preproc_ok=:
break
fi
rm -f conftest.err conftest.i conftest.$ac_ext

done
# Because of `break', _AC_PREPROC_IFELSE's cleaning code was skipped.
rm -f conftest.i conftest.err conftest.$ac_ext
if $ac_preproc_ok
then :
  break
fi

    done
    ac_cv_prog_CPP=$CPP

fi
  CPP=$ac_cv_prog_CPP
else
  ac_cv_prog_CPP=$CPP
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $CPP" >&5
printf "%s\n" "$CPP" >&6; }
ac_preproc_ok=false
for ac_c_preproc_warn_flag in '' yes
do
  # Use a header file that comes with gcc, so configuring glibc
  # with a fresh cross-compiler works.
  # On the NeXT, cc -E runs the code through the compiler's parser,
  # not just through cpp. "Syntax error" is here to catch this case.
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <limits.h>
		     Syntax error
_ACEOF
if ac_fn_c_try_cpp "$LINENO"
then :

else $as_nop
  # Broken: fails on valid input.
continue
fi
rm -f conftest.err conftest.i conftest.$ac_ext

  # OK, works on sane cases.  Now check whether nonexistent headers
  # can be detected and how.
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <ac_nonexistent.h>
_ACEOF
if ac_fn_c_try_cpp "$LINENO"
then :
  # Broken: success on invalid input.
continue
else $as_nop
  # Passes both tests.
ac_preproc_ok=:
break
fi
rm -f conftest.err conftest.i conftest.$ac_ext

done
# Because of `break', _AC_PREPROC_IFELSE's cleaning code was skipped.
rm -f conftest.i conftest.err conftest.$ac_ext
if $ac_preproc_ok
then :

else $as_nop
  { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
as_fn_error $? "C preprocessor \"$CPP\" fails sanity check
See \`config.log' for more details" "$LINENO" 5; }
fi

ac_ext=c
ac_cpp='$CPP $CPPFLAGS'
ac_compile='$CC -c $CFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CC -o conftest$ac_exeext $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_c_compiler_gnu


{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for X" >&5
printf %s "checking for X... " >&6; }


# Check whether --with-x was given.
if test ${with_x+y}
then :
  withval=$with_x;
fi

# $have_x is `yes', `no', `disabled', or empty when we do not yet know.
if test "x$with_x" = xno; then
  # The user explicitly disabled X.
  have_x=disabled
else
  case $x_includes,$x_libraries in #(
    *\'*) as_fn_error $? "cannot use X directory names containing '" "$LINENO" 5;; #(
    *,NONE | NONE,*) if test ${ac_cv_have_x+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  # One or both of the vars are not set, and there is no cached value.
ac_x_includes=no
ac_x_libraries=no
# Do we need to do anything special at all?
ac_save_LIBS=$LIBS
LIBS="-lX11 $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <X11/Xlib.h>
int
main (void)
{
XrmInitialize ()
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  # We can compile and link X programs with no special options.
  ac_x_includes=
  ac_x_libraries=
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS="$ac_save_LIBS"
# If that didn't work, only try xmkmf and file system searches
# for native compilation.
if test x"$ac_x_includes" = xno && test "$cross_compiling" = no
then :
  rm -f -r conftest.dir
if mkdir conftest.dir; then
  cd conftest.dir
  cat >Imakefile <<'_ACEOF'
incroot:
	@echo incroot='${INCROOT}'
usrlibdir:
	@echo usrlibdir='${USRLIBDIR}'
libdir:
	@echo libdir='${LIBDIR}'
_ACEOF
  if (export CC; ${XMKMF-xmkmf}) >/dev/null 2>/dev/null && test -f Makefile; then
    # GNU make sometimes prints "make[1]: Entering ...", which would confuse us.
    for ac_var in incroot usrlibdir libdir; do
      eval "ac_im_$ac_var=\`\${MAKE-make} $ac_var 2>/dev/null | sed -n 's/^$ac_var=//p'\`"
    done
    # Open Windows xmkmf reportedly sets LIBDIR instead of USRLIBDIR.
    for ac_extension in a so sl dylib la dll; do
      if test ! -f "$ac_im_usrlibdir/libX11.$ac_extension" &&
	 test -f "$ac_im_libdir/libX11.$ac_extension"; then
	ac_im_usrlibdir=$ac_im_libdir; break
      fi
    done
    # Screen out bogus values from the imake configuration.  They are
    # bogus both because they are the default anyway, and because
    # using them would break gcc on systems where it needs fixed includes.
    case $ac_im_incroot in
	/usr/include) ac_x_includes= ;;
	*) test -f "$ac_im_incroot/X11/Xos.h" && ac_x_includes=$ac_im_incroot;;
    esac
    case $ac_im_usrlibdir in
	/usr/lib | /usr/lib64 | /lib | /lib64) ;;
	*) test -d "$ac_im_usrlibdir" && ac_x_libraries=$ac_im_usrlibdir ;;
    esac
  fi
  cd ..
  rm -f -r conftest.dir
fi

  # Standard set of common directories for X headers.
# Check X11 before X11Rn because it is often a symlink to the current release.
ac_x_header_dirs='
/usr/X11/include
/usr/X11R7/include
/usr/X11R6/include
/usr/X11R5/include
/usr/X11R4/include

/usr/include/X11
/usr/include/X11R7
/usr/include/X11R6
/usr/include/X11R5
/usr/include/X11R4

/usr/local/X11/include
/usr/local/X11R7/include
/usr/local/X11R6/include
/usr/local/X11R5/include
/usr/local/X11R4/include

/usr/local/include/X11
/usr/local/include/X11R7
/usr/local/include/X11R6
/usr/local/include/X11R5
/usr/local/include/X11R4

/opt/X11/include

/usr/X386/include
/usr/x386/include
/usr/XFree86/include/X11

/usr/include
/usr/local/include
/usr/unsupported/include
/usr/athena/include
/usr/local/x11r5/include
/usr/lpp/Xamples/include

/usr/openwin/include
/usr/openwin/share/include'

if test "$ac_x_includes" = no; then
  # Guess where to find include files, by looking for Xlib.h.
  # First, try using that file with no special directory specified.
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <X11/Xlib.h>
_ACEOF
if ac_fn_c_try_cpp "$LINENO"
then :
  # We can compile using X headers with no special include directory.
ac_x_includes=
else $as_nop
  for ac_dir in $ac_x_header_dirs; do
  if test -r "$ac_dir/X11/Xlib.h"; then
    ac_x_includes=$ac_dir
    break
  fi
done
fi
rm -f conftest.err conftest.i conftest.$ac_ext
fi # $ac_x_includes = no

if test "$ac_x_libraries" = no; then
  # Check for the libraries.
  # See if we find them without any special options.
  # Don't add to $LIBS permanently.
  ac_save_LIBS=$LIBS
  LIBS="-lX11 $LIBS"
  cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <X11/Xlib.h>
int
main (void)
{
XrmInitialize ()
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  LIBS=$ac_save_LIBS
# We can link X programs with no special library path.
ac_x_libraries=
else $as_nop
  LIBS=$ac_save_LIBS
for ac_dir in `printf "%s\n" "$ac_x_includes $ac_x_header_dirs" | sed s/include/lib/g`
do
  # Don't even attempt the hair of trying to link an X program!
  for ac_extension in a so sl dylib la dll; do
    if test -r "$ac_dir/libX11.$ac_extension"; then
      ac_x_libraries=$ac_dir
      break 2
    fi
  done
done
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
fi # $ac_x_libraries = no

fi
# Record the results.
case $ac_x_includes,$ac_x_libraries in #(
  no,* | *,no | *\'*) :
    # Didn't find X, or a directory has "'" in its name.
    ac_cv_have_x="have_x=no" ;; #(
  *) :
    # Record where we found X for the cache.
    ac_cv_have_x="have_x=yes\
	ac_x_includes='$ac_x_includes'\
	ac_x_libraries='$ac_x_libraries'" ;;
esac
fi
;; #(
    *) have_x=yes;;
  esac
  eval "$ac_cv_have_x"
fi # $with_x != no

if test "$have_x" != yes; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $have_x" >&5
printf "%s\n" "$have_x" >&6; }
  no_x=yes
else
  # If each of the values was on the command line, it overrides each guess.
  test "x$x_includes" = xNONE && x_includes=$ac_x_includes
  test "x$x_libraries" = xNONE && x_libraries=$ac_x_libraries
  # Update the cache value to reflect the command line values.
  ac_cv_have_x="have_x=yes\
	ac_x_includes='$x_includes'\
	ac_x_libraries='$x_libraries'"
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: libraries $x_libraries, headers $x_includes" >&5
printf "%s\n" "libraries $x_libraries, headers $x_includes" >&6; }
fi

case $host_os_gui in #(
  cygwin* | mingw*) :

        # Recent versions of Cygwin are seriously broken and the size
    # checks don't work because the shell puts out \r\n instead of
    # \n.  Here we just force U32 to be defined to "unsigned"...
    printf "%s\n" "#define U32 unsigned" >>confdefs.h

    # We do no longer define WIN32 or _WIN32 (since FLTK 1.4.0)
    # and we don't need to define USE_OPENGL32 (added in svn r6657
    # but never used, see STR #2147)
    # CFLAGS="-mwindows -D_WIN32 -DUSE_OPENGL32 $CFLAGS"
    # CXXFLAGS="-mwindows -D_WIN32 -DUSE_OPENGL32 $CXXFLAGS"
    LDFLAGS="-mwindows $LDFLAGS"
    DSOFLAGS="-mwindows $DSOFLAGS"
    LIBS="$LIBS -lole32 -luuid -lcomctl32 -lws2_32 -lwinspool"

    BUILD="WIN"
    if test "x$with_optim" = x
then :

                with_optim="-O3"

fi

    if test x$enable_gl != xno
then :

        ac_fn_c_check_header_compile "$LINENO" "GL/gl.h" "ac_cv_header_GL_gl_h" "$ac_includes_default"
if test "x$ac_cv_header_GL_gl_h" = xyes
then :

            printf "%s\n" "#define HAVE_GL 1" >>confdefs.h

            GLLIBS="-lopengl32"

fi

        ac_fn_c_check_header_compile "$LINENO" "GL/glu.h" "ac_cv_header_GL_glu_h" "$ac_includes_default"
if test "x$ac_cv_header_GL_glu_h" = xyes
then :

            printf "%s\n" "#define HAVE_GL_GLU_H 1" >>confdefs.h

            GLLIBS="-lglu32 $GLLIBS"

fi


else $as_nop

        LINKFLTKGL=""
        GLLIBNAME=""
        GLDSONAME=""
        GLDEMOS=""

fi

    if test "x$enable_threads" != xno -a x$have_pthread = xyes
then :

        printf "%s\n" "#define HAVE_PTHREAD 1" >>confdefs.h


fi

    THREADS="threads$EXEEXT"

    # Don't make symlinks since Windows is not case sensitive.
    if test "x$with_links" != xyes
then :

        HLINKS="#"

fi
 ;; #(
  darwin*) :

    BUILD="OSX"

    # MacOS X uses Cocoa for graphics.
    LIBS="$LIBS -framework Cocoa"
    # Add weak-linked additional frameworks for increasingly high macOS versions
    ac_ext=c
ac_cpp='$CPP $CPPFLAGS'
ac_compile='$CC -c $CFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CC -o conftest$ac_exeext $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_c_compiler_gnu

    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <AvailabilityMacros.h>
              #if __MAC_OS_X_VERSION_MAX_ALLOWED < 110000
              #error __MAC_OS_X_VERSION_MAX_ALLOWED < 110000
              #endif

int
main (void)
{


  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  LIBS="$LIBS -weak_framework UniformTypeIdentifiers"
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */
#include <AvailabilityMacros.h>
              #if __MAC_OS_X_VERSION_MAX_ALLOWED < 150000
              #error __MAC_OS_X_VERSION_MAX_ALLOWED < 150000
              #endif

int
main (void)
{


  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  LIBS="$LIBS -weak_framework ScreenCaptureKit"
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
    ac_ext=c
ac_cpp='$CPP $CPPFLAGS'
ac_compile='$CC -c $CFLAGS $CPPFLAGS conftest.$ac_ext >&5'
ac_link='$CC -o conftest$ac_exeext $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS >&5'
ac_compiler_gnu=$ac_cv_c_compiler_gnu


    if test x$have_pthread = xyes
then :

        printf "%s\n" "#define HAVE_PTHREAD 1" >>confdefs.h

        THREADS="threads$EXEEXT"

fi

    if test x$enable_gl != xno
then :

        printf "%s\n" "#define HAVE_GL 1" >>confdefs.h

        printf "%s\n" "#define HAVE_GL_GLU_H 1" >>confdefs.h

        GLLIBS="-framework OpenGL"

else $as_nop

        LINKFLTKGL=""
        GLLIBNAME=""
        GLDSONAME=""
        GLDEMOS=""

fi

    # Don't make symlinks because HFS+ is not case sensitive...
    if test "x$with_links" != xyes
then :

        HLINKS="#"

fi

    # Some steps are only done for OS X package management
    OSX_ONLY=

    # Install/Uninstall FLUID application
    INSTALL_DESKTOP="install-osx"
    UNINSTALL_DESKTOP="uninstall-osx"
 ;; #(
  *) :

    # All others are UNIX/X11...
    # This includes Cygwin target combined with X11
    if test x$have_pthread = xyes
then :

        printf "%s\n" "#define HAVE_PTHREAD 1" >>confdefs.h

        THREADS="threads$EXEEXT"

fi

    if test x$enable_wayland != xno
then :

      if test x$PKGCONFIG = x
then :

                if test x$enable_wayland = xyes
then :

          { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: --enable-wayland: please install pkg-config." >&5
printf "%s\n" "$as_me: WARNING: --enable-wayland: please install pkg-config." >&2;}
          as_fn_error $? "Aborting." "$LINENO" 5

else $as_nop

          { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: pkg-config is not found. Continuing with --disable-wayland" >&5
printf "%s\n" "$as_me: WARNING: pkg-config is not found. Continuing with --disable-wayland" >&2;}
          enable_wayland="no"

fi

else $as_nop

        missing="no"
        if $PKGCONFIG --exists 'wayland-client >= 1.18'
then :

else $as_nop
  missing="yes"
fi
        if $PKGCONFIG --exists 'wayland-protocols >= 1.15'
then :

else $as_nop
  missing="yes"
fi
        if $PKGCONFIG --exists wayland-cursor
then :

else $as_nop
  missing="yes"
fi
        if $PKGCONFIG --exists xkbcommon
then :

else $as_nop
  missing="yes"
fi
        if $PKGCONFIG --exists pangocairo
then :

else $as_nop
  missing="yes"
fi
        if test x$missing = xyes
then :

            { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: These packages 'wayland-client>=1.18 wayland-protocols>=1.15 wayland-cursor xkbcommon  pangocairo' are required to build FLTK for wayland." >&5
printf "%s\n" "$as_me: WARNING: These packages 'wayland-client>=1.18 wayland-protocols>=1.15 wayland-cursor xkbcommon  pangocairo' are required to build FLTK for wayland." >&2;}
            { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: At least one of them is missing." >&5
printf "%s\n" "$as_me: WARNING: At least one of them is missing." >&2;}
          if test x$enable_wayland = xyes
then :

            as_fn_error $? "Building for Wayland is not possible. Aborting." "$LINENO" 5

else $as_nop

            { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: Continuing with --disable-wayland" >&5
printf "%s\n" "$as_me: WARNING: Continuing with --disable-wayland" >&2;}
            enable_wayland="no"

fi

fi

fi

fi

    if test x$enable_wayland != xno
then :

                  printf "%s\n" "#define FLTK_USE_CAIRO 1" >>confdefs.h

      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: Turning on the usecairo option" >&5
printf "%s\n" "$as_me: Turning on the usecairo option" >&6;}

      BUILD="WAYLAND"
      printf "%s\n" "#define FLTK_USE_WAYLAND 1" >>confdefs.h

      graphics="Wayland"
      if test x$enable_x11 != xno
then :

       printf "%s\n" "#define FLTK_USE_X11 1" >>confdefs.h
 # to build a hybrid Wayland/X11 library
       BUILD="WAYLANDX11"
       graphics="Wayland or X11 with cairo"

fi
      if $PKGCONFIG --exists 'libdecor-0 >= 0.2.0'
then :

        libdecor="system"
        plugin_dir="$($PKGCONFIG --variable=libdir libdecor-0)/libdecor/plugins-1"
        CFLAGS="$CFLAGS -DUSE_SYSTEM_LIBDECOR"
        CXXFLAGS="$CXXFLAGS -DUSE_SYSTEM_LIBDECOR"
        CFLAGS="$CFLAGS -DLIBDECOR_PLUGIN_DIR=\\\"$plugin_dir\\\" "
        LIBS="$LIBS $($PKGCONFIG --libs libdecor-0)"

else $as_nop

        libdecor="bundled"
        CFLAGS="$CFLAGS -DUSE_SYSTEM_LIBDECOR=0 -DLIBDECOR_PLUGIN_DIR=\\\"\\\" "
        CXXFLAGS="$CXXFLAGS -DUSE_SYSTEM_LIBDECOR=0"


fi
      LIBS="$LIBS $($PKGCONFIG --libs wayland-cursor) $($PKGCONFIG --libs wayland-client) $($PKGCONFIG --libs xkbcommon) $($PKGCONFIG --libs pangocairo) "
      if test x$enable_x11 != xno
then :
  LIBS="$LIBS $($PKGCONFIG --libs x11)"
fi
      LIBS="$LIBS -ldl"
      DSOFLAGS="$LIBS $DSOFLAGS"
      enable_pango=yes
      LIBDECORDIR="libdecor/build"
      if $PKGCONFIG --exists dbus-1
then :
  LIBDECORDBUS="-DHAS_DBUS $($PKGCONFIG --cflags dbus-1)" LIBS="$LIBS $($PKGCONFIG --libs dbus-1)"

fi
      LDFLAGS="$LDFLAGS -rdynamic"

      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for library containing dlopen" >&5
printf %s "checking for library containing dlopen... " >&6; }
if test ${ac_cv_search_dlopen+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_func_search_save_LIBS=$LIBS
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char dlopen ();
int
main (void)
{
return dlopen ();
  ;
  return 0;
}
_ACEOF
for ac_lib in '' dl
do
  if test -z "$ac_lib"; then
    ac_res="none required"
  else
    ac_res=-l$ac_lib
    LIBS="-l$ac_lib  $ac_func_search_save_LIBS"
  fi
  if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_search_dlopen=$ac_res
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext
  if test ${ac_cv_search_dlopen+y}
then :
  break
fi
done
if test ${ac_cv_search_dlopen+y}
then :

else $as_nop
  ac_cv_search_dlopen=no
fi
rm conftest.$ac_ext
LIBS=$ac_func_search_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_search_dlopen" >&5
printf "%s\n" "$ac_cv_search_dlopen" >&6; }
ac_res=$ac_cv_search_dlopen
if test "$ac_res" != no
then :
  test "$ac_res" = "none required" || LIBS="$ac_res $LIBS"

fi


      if test x$enable_gl != xno
then :

          if $PKGCONFIG --exists gl
then :

            if $PKGCONFIG --exists egl
then :

              if $PKGCONFIG --exists wayland-egl
then :

                printf "%s\n" "#define HAVE_GL 1" >>confdefs.h

                GLLIBS="$($PKGCONFIG --libs wayland-egl) $($PKGCONFIG --libs egl) $($PKGCONFIG --libs gl) $GLLIBS"

fi
fi
fi
          if $PKGCONFIG --exists glu
then :

             printf "%s\n" "#define HAVE_GL_GLU_H 1" >>confdefs.h

             GLLIBS="$($PKGCONFIG --libs glu) $GLLIBS"

fi
          { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for glXGetProcAddressARB in -lGL" >&5
printf %s "checking for glXGetProcAddressARB in -lGL... " >&6; }
if test ${ac_cv_lib_GL_glXGetProcAddressARB+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lGL -lm $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char glXGetProcAddressARB ();
int
main (void)
{
return glXGetProcAddressARB ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_GL_glXGetProcAddressARB=yes
else $as_nop
  ac_cv_lib_GL_glXGetProcAddressARB=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_GL_glXGetProcAddressARB" >&5
printf "%s\n" "$ac_cv_lib_GL_glXGetProcAddressARB" >&6; }
if test "x$ac_cv_lib_GL_glXGetProcAddressARB" = xyes
then :
  printf "%s\n" "#define HAVE_GLXGETPROCADDRESSARB 1" >>confdefs.h


fi


else $as_nop

          LINKFLTKGL=""
          GLLIBNAME=""
          GLDSONAME=""
          GLDEMOS=""

fi

      if test x$libdecor = xbundled
then :

                    gtk_found=no
          CFLAGS="$($PKGCONFIG --cflags gtk+-3.0) $CFLAGS"
                 for ac_header in gtk/gtk.h
do :
  ac_fn_c_check_header_compile "$LINENO" "gtk/gtk.h" "ac_cv_header_gtk_gtk_h" "$ac_includes_default"
if test "x$ac_cv_header_gtk_gtk_h" = xyes
then :
  printf "%s\n" "#define HAVE_GTK_GTK_H 1" >>confdefs.h

              CFLAGS="$CFLAGS -DHAVE_GTK"
              LIBS="$LIBS $($PKGCONFIG --libs gtk+-3.0)"
              gtk_found=yes

fi

done

fi

            pango_found=no
      CFLAGS="$($PKGCONFIG --cflags pangocairo) $CFLAGS"
      CXXFLAGS="$($PKGCONFIG --cflags pangocairo) $CXXFLAGS"
      LIBS="$LIBS $($PKGCONFIG --libs pangocairo)"

             for ac_header in pango/pangocairo.h
do :
  ac_fn_c_check_header_compile "$LINENO" "pango/pangocairo.h" "ac_cv_header_pango_pangocairo_h" "$ac_includes_default"
if test "x$ac_cv_header_pango_pangocairo_h" = xyes
then :
  printf "%s\n" "#define HAVE_PANGO_PANGOCAIRO_H 1" >>confdefs.h

          printf "%s\n" "#define USE_PANGO 1" >>confdefs.h

          printf "%s\n" "#define USE_XFT 1" >>confdefs.h

          pango_found=yes

fi

done

            if test x$pango_found != xyes
then :

          { printf "%s\n" "$as_me:${as_lineno-$LINENO}: --enable-wayland: Pango libs and/or headers could not be found." >&5
printf "%s\n" "$as_me: --enable-wayland: Pango libs and/or headers could not be found." >&6;}
          as_fn_error $? "Aborting." "$LINENO" 5

fi

            if test x$enable_x11 != xno
then :

        xinerama_found=no
        if $PKGCONFIG --exists xinerama
then :

          printf "%s\n" "#define HAVE_XINERAMA 1" >>confdefs.h

          LIBS="$LIBS $($PKGCONFIG --libs xinerama)"
          xinerama_found=yes

fi

        xcursor_found=no
        if $PKGCONFIG --exists xcursor
then :

          printf "%s\n" "#define HAVE_XCURSOR 1" >>confdefs.h

          LIBS="$LIBS $($PKGCONFIG --libs xcursor)"
          xcursor_found=yes

fi

        xfixes_found=no
        if $PKGCONFIG --exists xfixes
then :

          printf "%s\n" "#define HAVE_XFIXES 1" >>confdefs.h

          LIBS="$LIBS $($PKGCONFIG --libs xfixes)"
          xfixes_found=yes

fi

        xrender_found=no
        if $PKGCONFIG --exists xrender
then :

          printf "%s\n" "#define HAVE_XRENDER 1" >>confdefs.h

          LIBS="$LIBS $($PKGCONFIG --libs xrender)"
          xrender_found=yes

fi

        ac_fn_c_check_header_compile "$LINENO" "X11/Xregion.h" "ac_cv_header_X11_Xregion_h" "#include <X11/Xlib.h>
"
if test "x$ac_cv_header_X11_Xregion_h" = xyes
then :

          printf "%s\n" "#define HAVE_X11_XREGION_H 1" >>confdefs.h


fi


fi


else $as_nop


        if test "$no_x" = yes; then
  # Not all programs may use this symbol, but it does not hurt to define it.

printf "%s\n" "#define X_DISPLAY_MISSING 1" >>confdefs.h

  X_CFLAGS= X_PRE_LIBS= X_LIBS= X_EXTRA_LIBS=
else
  if test -n "$x_includes"; then
    X_CFLAGS="$X_CFLAGS -I$x_includes"
  fi

  # It would also be nice to do this for all -L options, not just this one.
  if test -n "$x_libraries"; then
    X_LIBS="$X_LIBS -L$x_libraries"
    # For Solaris; some versions of Sun CC require a space after -R and
    # others require no space.  Words are not sufficient . . . .
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking whether -R must be followed by a space" >&5
printf %s "checking whether -R must be followed by a space... " >&6; }
    ac_xsave_LIBS=$LIBS; LIBS="$LIBS -R$x_libraries"
    ac_xsave_c_werror_flag=$ac_c_werror_flag
    ac_c_werror_flag=yes
    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
       X_LIBS="$X_LIBS -R$x_libraries"
else $as_nop
  LIBS="$ac_xsave_LIBS -R $x_libraries"
       cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
	  X_LIBS="$X_LIBS -R $x_libraries"
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: neither works" >&5
printf "%s\n" "neither works" >&6; }
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
    ac_c_werror_flag=$ac_xsave_c_werror_flag
    LIBS=$ac_xsave_LIBS
  fi

  # Check for system-dependent libraries X programs must link with.
  # Do this before checking for the system-independent R6 libraries
  # (-lICE), since we may need -lsocket or whatever for X linking.

  if test "$ISC" = yes; then
    X_EXTRA_LIBS="$X_EXTRA_LIBS -lnsl_s -linet"
  else
    # Martyn Johnson says this is needed for Ultrix, if the X
    # libraries were built with DECnet support.  And Karl Berry says
    # the Alpha needs dnet_stub (dnet does not exist).
    ac_xsave_LIBS="$LIBS"; LIBS="$LIBS $X_LIBS -lX11"
    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char XOpenDisplay ();
int
main (void)
{
return XOpenDisplay ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :

else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for dnet_ntoa in -ldnet" >&5
printf %s "checking for dnet_ntoa in -ldnet... " >&6; }
if test ${ac_cv_lib_dnet_dnet_ntoa+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-ldnet  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char dnet_ntoa ();
int
main (void)
{
return dnet_ntoa ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_dnet_dnet_ntoa=yes
else $as_nop
  ac_cv_lib_dnet_dnet_ntoa=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_dnet_dnet_ntoa" >&5
printf "%s\n" "$ac_cv_lib_dnet_dnet_ntoa" >&6; }
if test "x$ac_cv_lib_dnet_dnet_ntoa" = xyes
then :
  X_EXTRA_LIBS="$X_EXTRA_LIBS -ldnet"
fi

    if test $ac_cv_lib_dnet_dnet_ntoa = no; then
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for dnet_ntoa in -ldnet_stub" >&5
printf %s "checking for dnet_ntoa in -ldnet_stub... " >&6; }
if test ${ac_cv_lib_dnet_stub_dnet_ntoa+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-ldnet_stub  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char dnet_ntoa ();
int
main (void)
{
return dnet_ntoa ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_dnet_stub_dnet_ntoa=yes
else $as_nop
  ac_cv_lib_dnet_stub_dnet_ntoa=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_dnet_stub_dnet_ntoa" >&5
printf "%s\n" "$ac_cv_lib_dnet_stub_dnet_ntoa" >&6; }
if test "x$ac_cv_lib_dnet_stub_dnet_ntoa" = xyes
then :
  X_EXTRA_LIBS="$X_EXTRA_LIBS -ldnet_stub"
fi

    fi
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
    LIBS="$ac_xsave_LIBS"

    # <EMAIL> says -lnsl (and -lsocket) are needed for his 386/AT,
    # to get the SysV transport functions.
    # Chad R. Larson says the Pyramis MIS-ES running DC/OSx (SVR4)
    # needs -lnsl.
    # The nsl library prevents programs from opening the X display
    # on Irix 5.2, according to T.E. Dickey.
    # The functions gethostbyname, getservbyname, and inet_addr are
    # in -lbsd on LynxOS 3.0.1/i386, according to Lars Hecking.
    ac_fn_c_check_func "$LINENO" "gethostbyname" "ac_cv_func_gethostbyname"
if test "x$ac_cv_func_gethostbyname" = xyes
then :

fi

    if test $ac_cv_func_gethostbyname = no; then
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for gethostbyname in -lnsl" >&5
printf %s "checking for gethostbyname in -lnsl... " >&6; }
if test ${ac_cv_lib_nsl_gethostbyname+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lnsl  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char gethostbyname ();
int
main (void)
{
return gethostbyname ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_nsl_gethostbyname=yes
else $as_nop
  ac_cv_lib_nsl_gethostbyname=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_nsl_gethostbyname" >&5
printf "%s\n" "$ac_cv_lib_nsl_gethostbyname" >&6; }
if test "x$ac_cv_lib_nsl_gethostbyname" = xyes
then :
  X_EXTRA_LIBS="$X_EXTRA_LIBS -lnsl"
fi

      if test $ac_cv_lib_nsl_gethostbyname = no; then
	{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for gethostbyname in -lbsd" >&5
printf %s "checking for gethostbyname in -lbsd... " >&6; }
if test ${ac_cv_lib_bsd_gethostbyname+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lbsd  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char gethostbyname ();
int
main (void)
{
return gethostbyname ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_bsd_gethostbyname=yes
else $as_nop
  ac_cv_lib_bsd_gethostbyname=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_bsd_gethostbyname" >&5
printf "%s\n" "$ac_cv_lib_bsd_gethostbyname" >&6; }
if test "x$ac_cv_lib_bsd_gethostbyname" = xyes
then :
  X_EXTRA_LIBS="$X_EXTRA_LIBS -lbsd"
fi

      fi
    fi

    # <EMAIL> says without -lsocket,
    # socket/setsockopt and other routines are undefined under SCO ODT
    # 2.0.  But -lsocket is broken on IRIX 5.2 (and is not necessary
    # on later versions), says Simon Leinen: it contains gethostby*
    # variants that don't use the name server (or something).  -lsocket
    # must be given before -lnsl if both are needed.  We assume that
    # if connect needs -lnsl, so does gethostbyname.
    ac_fn_c_check_func "$LINENO" "connect" "ac_cv_func_connect"
if test "x$ac_cv_func_connect" = xyes
then :

fi

    if test $ac_cv_func_connect = no; then
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for connect in -lsocket" >&5
printf %s "checking for connect in -lsocket... " >&6; }
if test ${ac_cv_lib_socket_connect+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lsocket $X_EXTRA_LIBS $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char connect ();
int
main (void)
{
return connect ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_socket_connect=yes
else $as_nop
  ac_cv_lib_socket_connect=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_socket_connect" >&5
printf "%s\n" "$ac_cv_lib_socket_connect" >&6; }
if test "x$ac_cv_lib_socket_connect" = xyes
then :
  X_EXTRA_LIBS="-lsocket $X_EXTRA_LIBS"
fi

    fi

    # Guillermo Gomez says -lposix is necessary on A/UX.
    ac_fn_c_check_func "$LINENO" "remove" "ac_cv_func_remove"
if test "x$ac_cv_func_remove" = xyes
then :

fi

    if test $ac_cv_func_remove = no; then
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for remove in -lposix" >&5
printf %s "checking for remove in -lposix... " >&6; }
if test ${ac_cv_lib_posix_remove+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lposix  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char remove ();
int
main (void)
{
return remove ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_posix_remove=yes
else $as_nop
  ac_cv_lib_posix_remove=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_posix_remove" >&5
printf "%s\n" "$ac_cv_lib_posix_remove" >&6; }
if test "x$ac_cv_lib_posix_remove" = xyes
then :
  X_EXTRA_LIBS="$X_EXTRA_LIBS -lposix"
fi

    fi

    # BSDI BSD/OS 2.1 needs -lipc for XOpenDisplay.
    ac_fn_c_check_func "$LINENO" "shmat" "ac_cv_func_shmat"
if test "x$ac_cv_func_shmat" = xyes
then :

fi

    if test $ac_cv_func_shmat = no; then
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for shmat in -lipc" >&5
printf %s "checking for shmat in -lipc... " >&6; }
if test ${ac_cv_lib_ipc_shmat+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lipc  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char shmat ();
int
main (void)
{
return shmat ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_ipc_shmat=yes
else $as_nop
  ac_cv_lib_ipc_shmat=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_ipc_shmat" >&5
printf "%s\n" "$ac_cv_lib_ipc_shmat" >&6; }
if test "x$ac_cv_lib_ipc_shmat" = xyes
then :
  X_EXTRA_LIBS="$X_EXTRA_LIBS -lipc"
fi

    fi
  fi

  # Check for libraries that X11R6 Xt/Xaw programs need.
  ac_save_LDFLAGS=$LDFLAGS
  test -n "$x_libraries" && LDFLAGS="$LDFLAGS -L$x_libraries"
  # SM needs ICE to (dynamically) link under SunOS 4.x (so we have to
  # check for ICE first), but we must link in the order -lSM -lICE or
  # we get undefined symbols.  So assume we have SM if we have ICE.
  # These have to be linked with before -lX11, unlike the other
  # libraries we check for below, so use a different variable.
  # John Interrante, Karl Berry
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for IceConnectionNumber in -lICE" >&5
printf %s "checking for IceConnectionNumber in -lICE... " >&6; }
if test ${ac_cv_lib_ICE_IceConnectionNumber+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lICE $X_EXTRA_LIBS $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char IceConnectionNumber ();
int
main (void)
{
return IceConnectionNumber ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_ICE_IceConnectionNumber=yes
else $as_nop
  ac_cv_lib_ICE_IceConnectionNumber=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_ICE_IceConnectionNumber" >&5
printf "%s\n" "$ac_cv_lib_ICE_IceConnectionNumber" >&6; }
if test "x$ac_cv_lib_ICE_IceConnectionNumber" = xyes
then :
  X_PRE_LIBS="$X_PRE_LIBS -lSM -lICE"
fi

  LDFLAGS=$ac_save_LDFLAGS

fi


    if test x$no_x = xyes
then :

        as_fn_error $? "Configure could not find required X11 libraries, aborting." "$LINENO" 5

fi

    if test "x$X_PRE_LIBS" != x
then :

        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: Ignoring libraries \"$X_PRE_LIBS\" requested by configure." >&5
printf "%s\n" "$as_me: WARNING: Ignoring libraries \"$X_PRE_LIBS\" requested by configure." >&2;}

fi

    LIBS="$LIBS -lX11 $X_EXTRA_LIBS"
    CFLAGS="$CFLAGS $X_CFLAGS"
    CXXFLAGS="$CXXFLAGS $X_CFLAGS"
    case $host_os in #(
  darwin*) :

      DARWIN_LDFLAGS="$LDFLAGS"
     ;; #(
  *) :
     ;;
esac
    LDFLAGS="$X_LIBS $LDFLAGS"
    DSOFLAGS="$X_LIBS $DSOFLAGS"
    printf "%s\n" "#define FLTK_USE_X11 1" >>confdefs.h

    BUILD="X11"
    if test "x$x_includes" != x
then :

        ac_cpp="$ac_cpp -I$x_includes"

fi

        GLLIBS=

    if test x$enable_gl != xno
then :

        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for library containing dlopen" >&5
printf %s "checking for library containing dlopen... " >&6; }
if test ${ac_cv_search_dlopen+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_func_search_save_LIBS=$LIBS
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char dlopen ();
int
main (void)
{
return dlopen ();
  ;
  return 0;
}
_ACEOF
for ac_lib in '' dl
do
  if test -z "$ac_lib"; then
    ac_res="none required"
  else
    ac_res=-l$ac_lib
    LIBS="-l$ac_lib  $ac_func_search_save_LIBS"
  fi
  if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_search_dlopen=$ac_res
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext
  if test ${ac_cv_search_dlopen+y}
then :
  break
fi
done
if test ${ac_cv_search_dlopen+y}
then :

else $as_nop
  ac_cv_search_dlopen=no
fi
rm conftest.$ac_ext
LIBS=$ac_func_search_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_search_dlopen" >&5
printf "%s\n" "$ac_cv_search_dlopen" >&6; }
ac_res=$ac_cv_search_dlopen
if test "$ac_res" != no
then :
  test "$ac_res" = "none required" || LIBS="$ac_res $LIBS"

fi

        ac_fn_c_check_header_compile "$LINENO" "GL/gl.h" "ac_cv_header_GL_gl_h" "$ac_includes_default"
if test "x$ac_cv_header_GL_gl_h" = xyes
then :

            { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for glXMakeCurrent in -lGL" >&5
printf %s "checking for glXMakeCurrent in -lGL... " >&6; }
if test ${ac_cv_lib_GL_glXMakeCurrent+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lGL -lm $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char glXMakeCurrent ();
int
main (void)
{
return glXMakeCurrent ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_GL_glXMakeCurrent=yes
else $as_nop
  ac_cv_lib_GL_glXMakeCurrent=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_GL_glXMakeCurrent" >&5
printf "%s\n" "$ac_cv_lib_GL_glXMakeCurrent" >&6; }
if test "x$ac_cv_lib_GL_glXMakeCurrent" = xyes
then :

                printf "%s\n" "#define HAVE_GL 1" >>confdefs.h

                GLLIBS="-lGL"

else $as_nop

                { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for glXMakeCurrent in -lMesaGL" >&5
printf %s "checking for glXMakeCurrent in -lMesaGL... " >&6; }
if test ${ac_cv_lib_MesaGL_glXMakeCurrent+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lMesaGL -lm $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char glXMakeCurrent ();
int
main (void)
{
return glXMakeCurrent ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_MesaGL_glXMakeCurrent=yes
else $as_nop
  ac_cv_lib_MesaGL_glXMakeCurrent=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_MesaGL_glXMakeCurrent" >&5
printf "%s\n" "$ac_cv_lib_MesaGL_glXMakeCurrent" >&6; }
if test "x$ac_cv_lib_MesaGL_glXMakeCurrent" = xyes
then :

                    printf "%s\n" "#define HAVE_GL 1" >>confdefs.h

                    GLLIBS="-lMesaGL"

fi
,

fi

            { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for glXGetProcAddressARB in -lGL" >&5
printf %s "checking for glXGetProcAddressARB in -lGL... " >&6; }
if test ${ac_cv_lib_GL_glXGetProcAddressARB+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lGL -lm $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char glXGetProcAddressARB ();
int
main (void)
{
return glXGetProcAddressARB ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_GL_glXGetProcAddressARB=yes
else $as_nop
  ac_cv_lib_GL_glXGetProcAddressARB=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_GL_glXGetProcAddressARB" >&5
printf "%s\n" "$ac_cv_lib_GL_glXGetProcAddressARB" >&6; }
if test "x$ac_cv_lib_GL_glXGetProcAddressARB" = xyes
then :

                printf "%s\n" "#define HAVE_GLXGETPROCADDRESSARB 1" >>confdefs.h


fi


fi

        ac_fn_c_check_header_compile "$LINENO" "GL/glu.h" "ac_cv_header_GL_glu_h" "$ac_includes_default"
if test "x$ac_cv_header_GL_glu_h" = xyes
then :

            printf "%s\n" "#define HAVE_GL_GLU_H 1" >>confdefs.h

            if test x$ac_cv_lib_GL_glXMakeCurrent = xyes
then :

                GLLIBS="-lGLU $GLLIBS"

fi
            if test x$ac_cv_lib_MesaGL_glXMakeCurrent = xyes
then :

                GLLIBS="-lMesaGLU $GLLIBS"

fi

fi


        if test x$ac_cv_lib_GL_glXMakeCurrent != xyes -a x$ac_cv_lib_MesaGL_glXMakeCurrent != xyes
then :

            LINKFLTKGL=""
            GLLIBNAME=""
            GLDSONAME=""
            GLDEMOS=""

fi

else $as_nop

        LINKFLTKGL=""
        GLLIBNAME=""
        GLDSONAME=""
        GLDEMOS=""

fi

    xinerama_found=no
    if test x$enable_xinerama != xno
then :

        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for XineramaIsActive in -lXinerama" >&5
printf %s "checking for XineramaIsActive in -lXinerama... " >&6; }
if test ${ac_cv_lib_Xinerama_XineramaIsActive+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lXinerama  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char XineramaIsActive ();
int
main (void)
{
return XineramaIsActive ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_Xinerama_XineramaIsActive=yes
else $as_nop
  ac_cv_lib_Xinerama_XineramaIsActive=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_Xinerama_XineramaIsActive" >&5
printf "%s\n" "$ac_cv_lib_Xinerama_XineramaIsActive" >&6; }
if test "x$ac_cv_lib_Xinerama_XineramaIsActive" = xyes
then :

            printf "%s\n" "#define HAVE_XINERAMA 1" >>confdefs.h

            LIBS="-lXinerama $LIBS"
            xinerama_found=yes

fi


fi

        xft_found=no
    if test x$enable_xft != xno -a x$enable_wayland = xno
then :

        # Try pkg-config first (freetype2 deprecated freetype-config from some version on)
        FT_FLAGS=""
        if test "x$PKGCONFIG" != x
then :

            FT_FLAGS="$($PKGCONFIG --cflags xft)"
            if test "x$FT_FLAGS" = x
then :

                FT_FLAGS="$($PKGCONFIG --cflags freetype2)"

fi

fi

        # if that failed, try freetype-config
        if test "x$FT_FLAGS" = x
then :

            # Extract the first word of "freetype-config", so it can be a program name with args.
set dummy freetype-config; ac_word=$2
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for $ac_word" >&5
printf %s "checking for $ac_word... " >&6; }
if test ${ac_cv_path_FTCONFIG+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  case $FTCONFIG in
  [\\/]* | ?:[\\/]*)
  ac_cv_path_FTCONFIG="$FTCONFIG" # Let the user override the test with a path.
  ;;
  *)
  as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    for ac_exec_ext in '' $ac_executable_extensions; do
  if as_fn_executable_p "$as_dir$ac_word$ac_exec_ext"; then
    ac_cv_path_FTCONFIG="$as_dir$ac_word$ac_exec_ext"
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: found $as_dir$ac_word$ac_exec_ext" >&5
    break 2
  fi
done
  done
IFS=$as_save_IFS

  ;;
esac
fi
FTCONFIG=$ac_cv_path_FTCONFIG
if test -n "$FTCONFIG"; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $FTCONFIG" >&5
printf "%s\n" "$FTCONFIG" >&6; }
else
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi


            if test "x$FTCONFIG" != x
then :

                FT_FLAGS="$($FTCONFIG --cflags)"

else $as_nop

                # abort if both methods failed
                { printf "%s\n" "$as_me:${as_lineno-$LINENO}: please install pkg-config or use 'configure --disable-xft'." >&5
printf "%s\n" "$as_me: please install pkg-config or use 'configure --disable-xft'." >&6;}
                as_fn_error $? "Aborting." "$LINENO" 5

fi

fi
        CPPFLAGS="$FT_FLAGS $CPPFLAGS"
        CXXFLAGS="$FT_FLAGS $CXXFLAGS"
        CFLAGS="$FT_FLAGS $CFLAGS"

        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for FcPatternCreate in -lfontconfig" >&5
printf %s "checking for FcPatternCreate in -lfontconfig... " >&6; }
if test ${ac_cv_lib_fontconfig_FcPatternCreate+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lfontconfig  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char FcPatternCreate ();
int
main (void)
{
return FcPatternCreate ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_fontconfig_FcPatternCreate=yes
else $as_nop
  ac_cv_lib_fontconfig_FcPatternCreate=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_fontconfig_FcPatternCreate" >&5
printf "%s\n" "$ac_cv_lib_fontconfig_FcPatternCreate" >&6; }
if test "x$ac_cv_lib_fontconfig_FcPatternCreate" = xyes
then :
  printf "%s\n" "#define HAVE_LIBFONTCONFIG 1" >>confdefs.h

  LIBS="-lfontconfig $LIBS"

fi

        ac_fn_c_check_header_compile "$LINENO" "X11/Xft/Xft.h" "ac_cv_header_X11_Xft_Xft_h" "$ac_includes_default"
if test "x$ac_cv_header_X11_Xft_Xft_h" = xyes
then :

            { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for XftDrawCreate in -lXft" >&5
printf %s "checking for XftDrawCreate in -lXft... " >&6; }
if test ${ac_cv_lib_Xft_XftDrawCreate+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lXft  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char XftDrawCreate ();
int
main (void)
{
return XftDrawCreate ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_Xft_XftDrawCreate=yes
else $as_nop
  ac_cv_lib_Xft_XftDrawCreate=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_Xft_XftDrawCreate" >&5
printf "%s\n" "$ac_cv_lib_Xft_XftDrawCreate" >&6; }
if test "x$ac_cv_lib_Xft_XftDrawCreate" = xyes
then :

                printf "%s\n" "#define USE_XFT 1" >>confdefs.h

                LIBS="-lXft $LIBS"
                BUILD="XFT"
                xft_found=yes

fi


fi


fi

            if test x$enable_xft != xno -a x$xft_found != xyes
then :

        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: could not find the required Xft headers and/or libraries." >&5
printf "%s\n" "$as_me: WARNING: could not find the required Xft headers and/or libraries." >&2;}
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: please install Xft headers and libraries or use 'configure --disable-xft'." >&5
printf "%s\n" "$as_me: please install Xft headers and libraries or use 'configure --disable-xft'." >&6;}
        if test x$enable_xft = xyes
then :

            as_fn_error $? "Aborting." "$LINENO" 5

fi

fi

        if test x$enable_usecairo = xyes
then :

      enable_pango=yes
      BUILD="CAIRO"
      printf "%s\n" "#define FLTK_USE_CAIRO 1" >>confdefs.h

      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: Processing usecairo option" >&5
printf "%s\n" "$as_me: Processing usecairo option" >&6;}


fi

        if test x$enable_pango = xyes -a x$xft_found = xno
then :

        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: could not find the Xft headers and/or libraries required for Pango." >&5
printf "%s\n" "$as_me: WARNING: could not find the Xft headers and/or libraries required for Pango." >&2;}
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: please install Xft headers and libraries or don't use configure with '--enable-pango'." >&5
printf "%s\n" "$as_me: please install Xft headers and libraries or don't use configure with '--enable-pango'." >&6;}
        as_fn_error $? "Aborting." "$LINENO" 5

fi

        pango_found=no
    if test x$enable_pango = xyes
then :

        if test x$PKGCONFIG != x
then :

                                    CXXFLAGS="$($PKGCONFIG --cflags pangocairo) $CXXFLAGS"
            case $host_os in #(
  darwin*) :
     ;; #(
  *) :

              CXXFLAGS="$($PKGCONFIG --cflags pangoxft) $CXXFLAGS"
             ;; #(
  *) :
     ;;
esac
            case $host_os in #(
  darwin*) :

              DARWIN_SAVE_LIBS=$LIBS
              DARWIN_PANGO_LIBS="$($PKGCONFIG --libs pangocairo)"
              LIBS="$($PKGCONFIG --libs pangocairo) $LIBS"
             ;; #(
  *) :

              LIBS="$($PKGCONFIG --libs pangocairo --libs pangoxft) $LIBS"
             ;; #(
  *) :
     ;;
esac

else $as_nop

                        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: --enable-pango: please install pkg-config. Continuing anyway." >&5
printf "%s\n" "$as_me: WARNING: --enable-pango: please install pkg-config. Continuing anyway." >&2;}

fi

        CPPFLAGS="$CXXFLAGS"
        case $host_os in #(
  darwin*) :
           for ac_header in pango/pango.h
do :
  ac_fn_c_check_header_compile "$LINENO" "pango/pango.h" "ac_cv_header_pango_pango_h" "$ac_includes_default"
if test "x$ac_cv_header_pango_pango_h" = xyes
then :
  printf "%s\n" "#define HAVE_PANGO_PANGO_H 1" >>confdefs.h

            { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for pango_layout_new in -lpango-1.0" >&5
printf %s "checking for pango_layout_new in -lpango-1.0... " >&6; }
if test ${ac_cv_lib_pango_1_0_pango_layout_new+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lpango-1.0  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char pango_layout_new ();
int
main (void)
{
return pango_layout_new ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_pango_1_0_pango_layout_new=yes
else $as_nop
  ac_cv_lib_pango_1_0_pango_layout_new=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_pango_1_0_pango_layout_new" >&5
printf "%s\n" "$ac_cv_lib_pango_1_0_pango_layout_new" >&6; }
if test "x$ac_cv_lib_pango_1_0_pango_layout_new" = xyes
then :

              printf "%s\n" "#define USE_PANGO 1" >>confdefs.h

              pango_found=yes
              LIBS=$DARWIN_SAVE_LIBS

fi


fi

done
         ;; #(
  *) :

                 for ac_header in pango/pango.h pango/pangoxft.h
do :
  as_ac_Header=`printf "%s\n" "ac_cv_header_$ac_header" | $as_tr_sh`
ac_fn_c_check_header_compile "$LINENO" "$ac_header" "$as_ac_Header" "$ac_includes_default"
if eval test \"x\$"$as_ac_Header"\" = x"yes"
then :
  cat >>confdefs.h <<_ACEOF
#define `printf "%s\n" "HAVE_$ac_header" | $as_tr_cpp` 1
_ACEOF

            { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for pango_layout_new in -lpango-1.0" >&5
printf %s "checking for pango_layout_new in -lpango-1.0... " >&6; }
if test ${ac_cv_lib_pango_1_0_pango_layout_new+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lpango-1.0  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char pango_layout_new ();
int
main (void)
{
return pango_layout_new ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_pango_1_0_pango_layout_new=yes
else $as_nop
  ac_cv_lib_pango_1_0_pango_layout_new=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_pango_1_0_pango_layout_new" >&5
printf "%s\n" "$ac_cv_lib_pango_1_0_pango_layout_new" >&6; }
if test "x$ac_cv_lib_pango_1_0_pango_layout_new" = xyes
then :

                { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for pango_xft_render_layout in -lpangoxft-1.0" >&5
printf %s "checking for pango_xft_render_layout in -lpangoxft-1.0... " >&6; }
if test ${ac_cv_lib_pangoxft_1_0_pango_xft_render_layout+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lpangoxft-1.0  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char pango_xft_render_layout ();
int
main (void)
{
return pango_xft_render_layout ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_pangoxft_1_0_pango_xft_render_layout=yes
else $as_nop
  ac_cv_lib_pangoxft_1_0_pango_xft_render_layout=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_pangoxft_1_0_pango_xft_render_layout" >&5
printf "%s\n" "$ac_cv_lib_pangoxft_1_0_pango_xft_render_layout" >&6; }
if test "x$ac_cv_lib_pangoxft_1_0_pango_xft_render_layout" = xyes
then :

                    printf "%s\n" "#define USE_PANGO 1" >>confdefs.h

                    pango_found=yes

fi


fi


fi

done
         ;; #(
  *) :
     ;;
esac

                if test x$pango_found != xyes
then :

            { printf "%s\n" "$as_me:${as_lineno-$LINENO}: --enable-pango: Pango libs and/or headers could not be found." >&5
printf "%s\n" "$as_me: --enable-pango: Pango libs and/or headers could not be found." >&6;}
            as_fn_error $? "Aborting." "$LINENO" 5

fi

fi

        xfixes_found=no
    if test x$enable_xfixes != xno
then :

        ac_fn_c_check_header_compile "$LINENO" "X11/extensions/Xfixes.h" "ac_cv_header_X11_extensions_Xfixes_h" "#include <X11/Xlib.h>
"
if test "x$ac_cv_header_X11_extensions_Xfixes_h" = xyes
then :

            { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for XFixesQueryExtension in -lXfixes" >&5
printf %s "checking for XFixesQueryExtension in -lXfixes... " >&6; }
if test ${ac_cv_lib_Xfixes_XFixesQueryExtension+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lXfixes  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char XFixesQueryExtension ();
int
main (void)
{
return XFixesQueryExtension ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_Xfixes_XFixesQueryExtension=yes
else $as_nop
  ac_cv_lib_Xfixes_XFixesQueryExtension=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_Xfixes_XFixesQueryExtension" >&5
printf "%s\n" "$ac_cv_lib_Xfixes_XFixesQueryExtension" >&6; }
if test "x$ac_cv_lib_Xfixes_XFixesQueryExtension" = xyes
then :

                printf "%s\n" "#define HAVE_XFIXES 1" >>confdefs.h

                LIBS="-lXfixes $LIBS"
                xfixes_found=yes

fi


fi


fi

        xcursor_found=no
    if test x$enable_xcursor != xno
then :

        ac_fn_c_check_header_compile "$LINENO" "X11/Xcursor/Xcursor.h" "ac_cv_header_X11_Xcursor_Xcursor_h" "#include <X11/Xlib.h>
"
if test "x$ac_cv_header_X11_Xcursor_Xcursor_h" = xyes
then :

            { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for XcursorImageCreate in -lXcursor" >&5
printf %s "checking for XcursorImageCreate in -lXcursor... " >&6; }
if test ${ac_cv_lib_Xcursor_XcursorImageCreate+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lXcursor  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char XcursorImageCreate ();
int
main (void)
{
return XcursorImageCreate ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_Xcursor_XcursorImageCreate=yes
else $as_nop
  ac_cv_lib_Xcursor_XcursorImageCreate=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_Xcursor_XcursorImageCreate" >&5
printf "%s\n" "$ac_cv_lib_Xcursor_XcursorImageCreate" >&6; }
if test "x$ac_cv_lib_Xcursor_XcursorImageCreate" = xyes
then :

                printf "%s\n" "#define HAVE_XCURSOR 1" >>confdefs.h

                LIBS="-lXcursor $LIBS"
                xcursor_found=yes

fi


fi


fi

        xrender_found=no
    if test x$enable_xrender != xno
then :

        ac_fn_c_check_header_compile "$LINENO" "X11/extensions/Xrender.h" "ac_cv_header_X11_extensions_Xrender_h" "#include <X11/Xlib.h>
"
if test "x$ac_cv_header_X11_extensions_Xrender_h" = xyes
then :

            { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for XRenderQueryVersion in -lXrender" >&5
printf %s "checking for XRenderQueryVersion in -lXrender... " >&6; }
if test ${ac_cv_lib_Xrender_XRenderQueryVersion+y}
then :
  printf %s "(cached) " >&6
else $as_nop
  ac_check_lib_save_LIBS=$LIBS
LIBS="-lXrender  $LIBS"
cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

/* Override any GCC internal prototype to avoid an error.
   Use char because int might match the return type of a GCC
   builtin and then its argument prototype would still apply.  */
char XRenderQueryVersion ();
int
main (void)
{
return XRenderQueryVersion ();
  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  ac_cv_lib_Xrender_XRenderQueryVersion=yes
else $as_nop
  ac_cv_lib_Xrender_XRenderQueryVersion=no
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
LIBS=$ac_check_lib_save_LIBS
fi
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: $ac_cv_lib_Xrender_XRenderQueryVersion" >&5
printf "%s\n" "$ac_cv_lib_Xrender_XRenderQueryVersion" >&6; }
if test "x$ac_cv_lib_Xrender_XRenderQueryVersion" = xyes
then :

                printf "%s\n" "#define HAVE_XRENDER 1" >>confdefs.h

                LIBS="-lXrender $LIBS"
                xrender_found=yes

fi


fi


fi

    case $host_os in #(
  darwin*) :

      if test x$pango_found = xyes
then :

        #place X_LIBS after homebrew's pango libs
        LIBS="$DARWIN_PANGO_LIBS $X_LIBS $LIBS"
        #remove X_LIBS from LDFLAGS while keeping its initial content
        LDFLAGS="$DARWIN_LDFLAGS"

fi
     ;; #(
  *) :
     ;;
esac

        ac_fn_c_check_header_compile "$LINENO" "X11/Xregion.h" "ac_cv_header_X11_Xregion_h" "#include <X11/Xlib.h>
"
if test "x$ac_cv_header_X11_Xregion_h" = xyes
then :

        printf "%s\n" "#define HAVE_X11_XREGION_H 1" >>confdefs.h


fi



fi

    # Make symlinks since UNIX/Linux is case sensitive,
    # but Cygwin in general not.
    case $host_os in #(
  cygwin*) :

        HLINKS="#"
     ;; #(
  *) :
     ;;
esac

    # Make symlinks since UNIX/Linux is case sensitive,
    # but only if explicitly configured (default=no)
    if test "x$with_links" != xyes
then :

        HLINKS="#"

fi

    # Install/Uninstall FLUID application support files
    INSTALL_DESKTOP="install-linux"
    UNINSTALL_DESKTOP="uninstall-linux"
 ;; #(
  *) :
     ;;
esac


















case "$host_os" in #(
  *bsd* | darwin*) :

    # *BSD
    CAT1EXT=0
    CAT3EXT=0
    CAT6EXT=0
 ;; #(
  irix*) :

    # SGI IRIX
    CAT1EXT=z
    CAT3EXT=z
    CAT6EXT=z
 ;; #(
  *) :

    # All others
    CAT1EXT=1
    CAT3EXT=3
    CAT6EXT=6
 ;; #(
  *) :
     ;;
esac





if test "$mandir" = "\${prefix}/man" -a "$prefix" = "/usr"
then :

    case "$host_os" in #(
  *bsd* | darwin* | linux*) :

        # *BSD, Darwin, and Linux
        mandir="\${prefix}/share/man"
     ;; #(
  irix*) :

        # SGI IRIX
        mandir="\${prefix}/share/catman"
     ;; #(
  *) :
     ;;
esac

fi

if test "$prefix" = NONE
then :

    prefix=/usr/local

fi

if test "$exec_prefix" = NONE
then :

    exec_prefix="\${prefix}"

fi

case $host_os in #(
  irix1-5*) :

 ;; #(
  irix*) :

    if test "$libdir" = "\${exec_prefix}/lib" -a "$exec_prefix" = "\${prefix}" -a "$prefix" = "/usr"
then :

        libdir="/usr/lib32"

fi
 ;; #(
  *) :
     ;;
esac

MAKEDEPEND="\$(CXX) -M"



if test -n "$GCC"
then :

    # Show all standard warnings + unused variables, conversion errors,
    # and inlining problems when compiling...
    OPTIM="-Wall -Wunused -Wno-format-y2k $OPTIM"

    # The following additional warnings are useful for tracking down problems...
    #OPTIM="-Wshadow -Wconversion $OPTIM"

    # We know that Carbon is deprecated on OS X 10.4. To avoid hundreds of warnings
    # we will temporarily disable 'deprecated' warnings on OS X.
    case $host_os in #(
  darwin1-7) :

     ;; #(
  darwin*) :

        OPTIM="-Wno-deprecated-declarations $OPTIM"
     ;; #(
  *) :
     ;;
esac

    # Set the default compiler optimizations...
    if test -z "$DEBUGFLAG"
then :

        #
        # Note: Can't use -fomit-frame-pointer - prevents tools like
        #       libsafe from working!
        #
        #       Don't use -fforce-mem, -fforce-addr, or -fcaller-saves.
        #       They all seem to make either no difference or enlarge
        #       the code by a few hundred bytes.
        #
        #       "-Os" seems to be the best compromise between speed and
        #       code size.  "-O3" and higher seem to make no effective
        #       difference in the speed of the code, but does bloat the
        #       library 10+%.
        #

        if test "x$with_optim" != x
then :

            OPTIM="$with_optim $OPTIM"

else $as_nop

            OPTIM="-Os $OPTIM"

fi

fi

    # Generate position-independent code when needed...
    if test $PICFLAG = 1
then :

        OPTIM="$OPTIM -fPIC"

fi

    # See if GCC supports -fno-exceptions...
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking if GCC supports -fno-exceptions" >&5
printf %s "checking if GCC supports -fno-exceptions... " >&6; }
    OLDCFLAGS="$CFLAGS"
    CFLAGS="$CFLAGS -fno-exceptions"
    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  OPTIM="$OPTIM -fno-exceptions"
         { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
    CFLAGS="$OLDCFLAGS"

    # See if GCC supports -fno-strict-aliasing...
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking if GCC supports -fno-strict-aliasing" >&5
printf %s "checking if GCC supports -fno-strict-aliasing... " >&6; }
    OLDCFLAGS="$CFLAGS"
    CFLAGS="$CFLAGS -fno-strict-aliasing"
    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  OPTIM="$OPTIM -fno-strict-aliasing"
         { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
    CFLAGS="$OLDCFLAGS"

        # See if ld supports -no-undefined...
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking if ld supports -no-undefined" >&5
printf %s "checking if ld supports -no-undefined... " >&6; }
    OLDLDFLAGS="$LDFLAGS"
    LDFLAGS="$LDFLAGS -Wl,-no-undefined"
    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  DSOFLAGS="$DSOFLAGS -Wl,-no-undefined"
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
    LDFLAGS="$OLDLDFLAGS"

    # See if ld supports -Bsymbolic-functions...
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking if ld supports -Bsymbolic-functions" >&5
printf %s "checking if ld supports -Bsymbolic-functions... " >&6; }
    OLDLDFLAGS="$LDFLAGS"
    LDFLAGS="$LDFLAGS -Wl,-Bsymbolic-functions"
    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  DSOFLAGS="$DSOFLAGS -Wl,-Bsymbolic-functions"
         { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
    LDFLAGS="$OLDLDFLAGS"

    # See if toolchain supports a sectioned build...
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking if toolchain supports sections" >&5
printf %s "checking if toolchain supports sections... " >&6; }
    OLDLDFLAGS="$LDFLAGS"
    OLDCFLAGS="$CFLAGS"
    LDFLAGS="$LDFLAGS -Wl,-gc-sections"
    CFLAGS="$CFLAGS -ffunction-sections -fdata-sections"
    cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_link "$LINENO"
then :
  DSOFLAGS="$DSOFLAGS -Wl,-gc-sections"
         OPTIM="$OPTIM -ffunction-sections -fdata-sections"
         { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
else $as_nop
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam \
    conftest$ac_exeext conftest.$ac_ext
    LDFLAGS="$OLDLDFLAGS"
    CFLAGS="$OLDCFLAGS"

    # See if we are running Solaris; if so, try the -fpermissive option...
    # This option is required on some versions of Solaris to work around
    # bugs in the X headers up through Solaris 7.
    #
    # Unlike the other compiler/optimization settings, this one is placed
    # in CFLAGS and CXXFLAGS so that fltk-config will provide the option
    # to clients - otherwise client apps will not compile properly...
    case $host_os in #(
  solaris*) :

        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking if GCC supports -fpermissive" >&5
printf %s "checking if GCC supports -fpermissive... " >&6; }

        OLDCFLAGS="$CFLAGS"
        CFLAGS="$CFLAGS -fpermissive"
        cat confdefs.h - <<_ACEOF >conftest.$ac_ext
/* end confdefs.h.  */

int
main (void)
{

  ;
  return 0;
}
_ACEOF
if ac_fn_c_try_compile "$LINENO"
then :
  CXXFLAGS="$CXXFLAGS -fpermissive"
             { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: yes" >&5
printf "%s\n" "yes" >&6; }
else $as_nop
  CFLAGS="$OLDCFLAGS"
             { printf "%s\n" "$as_me:${as_lineno-$LINENO}: result: no" >&5
printf "%s\n" "no" >&6; }
fi
rm -f core conftest.err conftest.$ac_objext conftest.beam conftest.$ac_ext
     ;; #(
  *) :
     ;;
esac

else $as_nop

    case "$host_os" in #(
  irix*) :

        # Running some flavor of IRIX; see which version and
        # set things up according...
        if test "$uversion" -ge 62
then :

            # We are running IRIX 6.2 or higher; uncomment the following
            # lines if you don't have IDO 7.2 or higher:
            #
            #     CXX="CC -n32 -mips3"
            #     CC="cc -n32 -mips3"
            #     LD="ld -n32 -mips3"
            #     MAKEDEPEND="CC -M"

            if test "x`grep abi=n32 /etc/compiler.defaults`" = x
then :

                { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: FOR BEST RESULTS BEFORE COMPILING: setenv SGI_ABI \"-n32 -mips3\"" >&5
printf "%s\n" "$as_me: WARNING: FOR BEST RESULTS BEFORE COMPILING: setenv SGI_ABI \"-n32 -mips3\"" >&2;}

fi

            OPTIM="-fullwarn $OPTIM"

fi

        if test -z "$DEBUGFLAG"
then :

            if test "x$with_optim" != x
then :

                OPTIM="$with_optim $OPTIM"

else $as_nop

                OPTIM="-O2 $OPTIM"
                if test $uversion -gt 62
then :

                    OPTIM="-OPT:Olimit=4000 $OPTIM"

fi

fi

fi
     ;; #(
  hpux*) :

        # Running HP-UX; these options should work for the HP compilers.
        if test -z "$DEBUGFLAG"
then :

            if test "x$with_optim" != x
then :

                OPTIM="$with_optim $OPTIM"

else $as_nop

                OPTIM="+O2 $OPTIM"

fi

fi

        if test $PICFLAG = 1
then :

            OPTIM="+z $OPTIM"

fi

        CXXFLAGS="$CXXFLAGS +W336,501,736,740,749,829"
     ;; #(
  OSF1*) :

        # Running Digital/Tru64 UNIX; these options should work for the
        # Digital/Compaq/NewHP compilers.
        if test -z "$DEBUGFLAG"
then :

            if test "x$with_optim" != x
then :

                OPTIM="$with_optim $OPTIM"

else $as_nop

                OPTIM="-O2 $OPTIM"

fi

fi
     ;; #(
  solaris*) :

        # Solaris
        if test -z "$DEBUGFLAG"
then :

            if test "x$with_optim" != x
then :

                OPTIM="$with_optim $OPTIM"

else $as_nop

                OPTIM="-xO3 $OPTIM"

fi

fi

        if test $PICFLAG = 1
then :

            OPTIM="-KPIC $OPTIM"

fi
     ;; #(
  aix*) :

        if test -z "$DEBUGFLAG"
then :

            if test "x$with_optim" != x
then :

                OPTIM="$with_optim $OPTIM"

else $as_nop

                OPTIM="-O2 $OPTIM"

fi

fi

        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: The AIX C and C++ compilers are known not to correctly compile the FLTK library." >&5
printf "%s\n" "$as_me: WARNING: The AIX C and C++ compilers are known not to correctly compile the FLTK library." >&2;}
     ;; #(
  *) :

        # Running some other operating system; inform the user they
        # should contribute the necessary options via the STR form..
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: Building FLTK with default compiler optimizations" >&5
printf "%s\n" "$as_me: WARNING: Building FLTK with default compiler optimizations" >&2;}
        { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: Send the FLTK developers your uname and compiler options via https://www.fltk.org/bugs.php" >&5
printf "%s\n" "$as_me: WARNING: Send the FLTK developers your uname and compiler options via https://www.fltk.org/bugs.php" >&2;}
     ;; #(
  *) :
     ;;
esac

fi

OPTIM="$DEBUGFLAG $OPTIM"

case $host_os in #(
  darwin*) :

    if test "x$with_archflags" != x
then :

        CFLAGS="$(echo $CFLAGS | sed -e 's/$with_archflags//g')"

fi
 ;; #(
  *) :
     ;;
esac

case $host_os in #(
  mingw*) :

    # Determine the path where MSys has /usr installed
    msyspath="$(mount | grep '/usr ' | cut -d ' ' -f -1 | sed -e's,\\,/, g')"

    # Then substitute that in the Windows path instead of /usr
    printf "%s\n" "#define FLTK_DOCDIR \"$msyspath/local/share/doc/fltk\"" >>confdefs.h

 ;; #(
  *) :

    if test x$prefix = xNONE
then :

        printf "%s\n" "#define FLTK_DOCDIR \"/usr/local/share/doc/fltk\"" >>confdefs.h


else $as_nop

        printf "%s\n" "#define FLTK_DOCDIR \"$prefix/share/doc/fltk\"" >>confdefs.h


fi
 ;; #(
  *) :
     ;;
esac

if test x$prefix = xNONE
then :

    printf "%s\n" "#define FLTK_DATADIR \"/usr/local/share/fltk\"" >>confdefs.h


else $as_nop

    printf "%s\n" "#define FLTK_DATADIR \"$prefix/share/fltk\"" >>confdefs.h


fi


echo ""
echo "Configuration Summary"
echo "-------------------------------------------------------------------------"

case $host_os_gui in #(
  cygwin* | mingw*) :

    if test x$gdiplus_found = xyes
then :
  graphics="GDI+"
else $as_nop
  graphics="GDI"
fi
 ;; #(
  darwin*) :

    graphics="Quartz"
 ;; #(
  *) :

    if test x$enable_wayland = xno
then :

        graphics="X11"

fi
    if test x$xft_found = xyes
then :

        graphics="$graphics + Xft"

fi
    if test x$xfixes_found = xyes
then :

        graphics="$graphics + Xfixes"

fi
    if test x$xinerama_found = xyes
then :

        graphics="$graphics + Xinerama"

fi
    if test x$xcursor_found = xyes
then :

        graphics="$graphics + Xcursor"

fi
    if test x$xrender_found = xyes
then :

        graphics="$graphics + Xrender"

fi
    if test x$enable_usecairo = xyes
then :

        graphics="$graphics + Cairo"

fi
    if test x$pango_found = xyes
then :

        graphics="$graphics + Pango"

fi
 ;; #(
  *) :
     ;;
esac

echo "    Directories: prefix=$prefix"
echo "                 bindir=$bindir"
echo "                 datadir=$datadir"
echo "                 datarootdir=$datarootdir"
echo "                 exec_prefix=$exec_prefix"
echo "                 includedir=$includedir"
echo "                 libdir=$libdir"
echo "                 mandir=$mandir"
case $host_os in #(
  mingw*) :

    echo "                 MSys docpath=$msyspath/local/share/doc/fltk"
 ;; #(
  *) :
     ;;
esac

if test x$enable_fluid != xno
then :

    echo "    Build fluid: YES"

else $as_nop

    echo "    Build fluid: NO"

fi

if test x$enable_test != xno
then :

    echo "    Build tests: YES"

else $as_nop

    echo "    Build tests: NO"

fi

if test "$fltk_cross_compiling" = "yes"
then :

    echo "Cross-compiling: YES"
    echo "          Build: $build -> Host: $host"

else $as_nop

    echo "Cross-compiling: NO"

fi

echo "       Graphics: $graphics"
if test x$JPEG = x
then :

    echo "Image Libraries: JPEG=System"

else $as_nop

    echo "Image Libraries: JPEG=Builtin"

fi
if test x$PNG = x
then :

    echo "                 PNG=System"

else $as_nop

    echo "                 PNG=Builtin"

fi
if test x$ZLIB = x
then :

    echo "                 ZLIB=System"

else $as_nop

    echo "                 ZLIB=Builtin"

fi

if test x$build_forms = xyes
then :

    echo "Forms library:   YES"

else $as_nop

    echo "Forms library:   NO"

fi

if test x$enable_cairo = xyes
then :

    echo "                 CAIRO=lib"

fi
if test x$enable_cairoext = xyes
then :

    echo "                 CAIRO=internal_use"

fi

if test x$enable_largefile != xno
then :

    echo "    Large Files: YES"

else $as_nop

    echo "    Large Files: NO"

fi

if test x$GLDEMOS = x
then :

    echo "         OpenGL: NO"

else $as_nop

    echo "         OpenGL: YES"

fi

if test x$THREADS = x
then :

    echo "        Threads: NO"

else $as_nop

    echo "        Threads: YES"

fi

if test x$enable_use_std != xyes
then :

    echo "   Allow std:: : NO"

else $as_nop

    echo "   Allow std:: : YES"

fi

BINARY_DIR=


ac_config_headers="$ac_config_headers config.h:configh.in"

ac_config_headers="$ac_config_headers FL/fl_config.h:fl_config.in"

ac_config_files="$ac_config_files makeinclude fltk.list fltk-config fltk.spec FL/Makefile"

cat >confcache <<\_ACEOF
# This file is a shell script that caches the results of configure
# tests run on this system so they can be shared between configure
# scripts and configure runs, see configure's option --config-cache.
# It is not useful on other systems.  If it contains results you don't
# want to keep, you may remove or edit it.
#
# config.status only pays attention to the cache file if you give it
# the --recheck option to rerun configure.
#
# `ac_cv_env_foo' variables (set or unset) will be overridden when
# loading this file, other *unset* `ac_cv_foo' will be assigned the
# following values.

_ACEOF

# The following way of writing the cache mishandles newlines in values,
# but we know of no workaround that is simple, portable, and efficient.
# So, we kill variables containing newlines.
# Ultrix sh set writes to stderr and can't be redirected directly,
# and sets the high bit in the cache file unless we assign to the vars.
(
  for ac_var in `(set) 2>&1 | sed -n 's/^\([a-zA-Z_][a-zA-Z0-9_]*\)=.*/\1/p'`; do
    eval ac_val=\$$ac_var
    case $ac_val in #(
    *${as_nl}*)
      case $ac_var in #(
      *_cv_*) { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: cache variable $ac_var contains a newline" >&5
printf "%s\n" "$as_me: WARNING: cache variable $ac_var contains a newline" >&2;} ;;
      esac
      case $ac_var in #(
      _ | IFS | as_nl) ;; #(
      BASH_ARGV | BASH_SOURCE) eval $ac_var= ;; #(
      *) { eval $ac_var=; unset $ac_var;} ;;
      esac ;;
    esac
  done

  (set) 2>&1 |
    case $as_nl`(ac_space=' '; set) 2>&1` in #(
    *${as_nl}ac_space=\ *)
      # `set' does not quote correctly, so add quotes: double-quote
      # substitution turns \\\\ into \\, and sed turns \\ into \.
      sed -n \
	"s/'/'\\\\''/g;
	  s/^\\([_$as_cr_alnum]*_cv_[_$as_cr_alnum]*\\)=\\(.*\\)/\\1='\\2'/p"
      ;; #(
    *)
      # `set' quotes correctly as required by POSIX, so do not add quotes.
      sed -n "/^[_$as_cr_alnum]*_cv_[_$as_cr_alnum]*=/p"
      ;;
    esac |
    sort
) |
  sed '
     /^ac_cv_env_/b end
     t clear
     :clear
     s/^\([^=]*\)=\(.*[{}].*\)$/test ${\1+y} || &/
     t end
     s/^\([^=]*\)=\(.*\)$/\1=${\1=\2}/
     :end' >>confcache
if diff "$cache_file" confcache >/dev/null 2>&1; then :; else
  if test -w "$cache_file"; then
    if test "x$cache_file" != "x/dev/null"; then
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: updating cache $cache_file" >&5
printf "%s\n" "$as_me: updating cache $cache_file" >&6;}
      if test ! -f "$cache_file" || test -h "$cache_file"; then
	cat confcache >"$cache_file"
      else
        case $cache_file in #(
        */* | ?:*)
	  mv -f confcache "$cache_file"$$ &&
	  mv -f "$cache_file"$$ "$cache_file" ;; #(
        *)
	  mv -f confcache "$cache_file" ;;
	esac
      fi
    fi
  else
    { printf "%s\n" "$as_me:${as_lineno-$LINENO}: not updating unwritable cache $cache_file" >&5
printf "%s\n" "$as_me: not updating unwritable cache $cache_file" >&6;}
  fi
fi
rm -f confcache

test "x$prefix" = xNONE && prefix=$ac_default_prefix
# Let make expand exec_prefix.
test "x$exec_prefix" = xNONE && exec_prefix='${prefix}'

DEFS=-DHAVE_CONFIG_H

ac_libobjs=
ac_ltlibobjs=
U=
for ac_i in : $LIBOBJS; do test "x$ac_i" = x: && continue
  # 1. Remove the extension, and $U if already installed.
  ac_script='s/\$U\././;s/\.o$//;s/\.obj$//'
  ac_i=`printf "%s\n" "$ac_i" | sed "$ac_script"`
  # 2. Prepend LIBOBJDIR.  When used with automake>=1.10 LIBOBJDIR
  #    will be set to the directory where LIBOBJS objects are built.
  as_fn_append ac_libobjs " \${LIBOBJDIR}$ac_i\$U.$ac_objext"
  as_fn_append ac_ltlibobjs " \${LIBOBJDIR}$ac_i"'$U.lo'
done
LIBOBJS=$ac_libobjs

LTLIBOBJS=$ac_ltlibobjs




: "${CONFIG_STATUS=./config.status}"
ac_write_fail=0
ac_clean_files_save=$ac_clean_files
ac_clean_files="$ac_clean_files $CONFIG_STATUS"
{ printf "%s\n" "$as_me:${as_lineno-$LINENO}: creating $CONFIG_STATUS" >&5
printf "%s\n" "$as_me: creating $CONFIG_STATUS" >&6;}
as_write_fail=0
cat >$CONFIG_STATUS <<_ASEOF || as_write_fail=1
#! $SHELL
# Generated by $as_me.
# Run this file to recreate the current configuration.
# Compiler output produced by configure, useful for debugging
# configure, is in config.log if it exists.

debug=false
ac_cs_recheck=false
ac_cs_silent=false

SHELL=\${CONFIG_SHELL-$SHELL}
export SHELL
_ASEOF
cat >>$CONFIG_STATUS <<\_ASEOF || as_write_fail=1
## -------------------- ##
## M4sh Initialization. ##
## -------------------- ##

# Be more Bourne compatible
DUALCASE=1; export DUALCASE # for MKS sh
as_nop=:
if test ${ZSH_VERSION+y} && (emulate sh) >/dev/null 2>&1
then :
  emulate sh
  NULLCMD=:
  # Pre-4.2 versions of Zsh do word splitting on ${1+"$@"}, which
  # is contrary to our usage.  Disable this feature.
  alias -g '${1+"$@"}'='"$@"'
  setopt NO_GLOB_SUBST
else $as_nop
  case `(set -o) 2>/dev/null` in #(
  *posix*) :
    set -o posix ;; #(
  *) :
     ;;
esac
fi



# Reset variables that may have inherited troublesome values from
# the environment.

# IFS needs to be set, to space, tab, and newline, in precisely that order.
# (If _AS_PATH_WALK were called with IFS unset, it would have the
# side effect of setting IFS to empty, thus disabling word splitting.)
# Quoting is to prevent editors from complaining about space-tab.
as_nl='
'
export as_nl
IFS=" ""	$as_nl"

PS1='$ '
PS2='> '
PS4='+ '

# Ensure predictable behavior from utilities with locale-dependent output.
LC_ALL=C
export LC_ALL
LANGUAGE=C
export LANGUAGE

# We cannot yet rely on "unset" to work, but we need these variables
# to be unset--not just set to an empty or harmless value--now, to
# avoid bugs in old shells (e.g. pre-3.0 UWIN ksh).  This construct
# also avoids known problems related to "unset" and subshell syntax
# in other old shells (e.g. bash 2.01 and pdksh 5.2.14).
for as_var in BASH_ENV ENV MAIL MAILPATH CDPATH
do eval test \${$as_var+y} \
  && ( (unset $as_var) || exit 1) >/dev/null 2>&1 && unset $as_var || :
done

# Ensure that fds 0, 1, and 2 are open.
if (exec 3>&0) 2>/dev/null; then :; else exec 0</dev/null; fi
if (exec 3>&1) 2>/dev/null; then :; else exec 1>/dev/null; fi
if (exec 3>&2)            ; then :; else exec 2>/dev/null; fi

# The user is always right.
if ${PATH_SEPARATOR+false} :; then
  PATH_SEPARATOR=:
  (PATH='/bin;/bin'; FPATH=$PATH; sh -c :) >/dev/null 2>&1 && {
    (PATH='/bin:/bin'; FPATH=$PATH; sh -c :) >/dev/null 2>&1 ||
      PATH_SEPARATOR=';'
  }
fi


# Find who we are.  Look in the path if we contain no directory separator.
as_myself=
case $0 in #((
  *[\\/]* ) as_myself=$0 ;;
  *) as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  case $as_dir in #(((
    '') as_dir=./ ;;
    */) ;;
    *) as_dir=$as_dir/ ;;
  esac
    test -r "$as_dir$0" && as_myself=$as_dir$0 && break
  done
IFS=$as_save_IFS

     ;;
esac
# We did not find ourselves, most probably we were run as `sh COMMAND'
# in which case we are not to be found in the path.
if test "x$as_myself" = x; then
  as_myself=$0
fi
if test ! -f "$as_myself"; then
  printf "%s\n" "$as_myself: error: cannot find myself; rerun with an absolute file name" >&2
  exit 1
fi



# as_fn_error STATUS ERROR [LINENO LOG_FD]
# ----------------------------------------
# Output "`basename $0`: error: ERROR" to stderr. If LINENO and LOG_FD are
# provided, also output the error to LOG_FD, referencing LINENO. Then exit the
# script with STATUS, using 1 if that was 0.
as_fn_error ()
{
  as_status=$1; test $as_status -eq 0 && as_status=1
  if test "$4"; then
    as_lineno=${as_lineno-"$3"} as_lineno_stack=as_lineno_stack=$as_lineno_stack
    printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: $2" >&$4
  fi
  printf "%s\n" "$as_me: error: $2" >&2
  as_fn_exit $as_status
} # as_fn_error



# as_fn_set_status STATUS
# -----------------------
# Set $? to STATUS, without forking.
as_fn_set_status ()
{
  return $1
} # as_fn_set_status

# as_fn_exit STATUS
# -----------------
# Exit the shell with STATUS, even in a "trap 0" or "set -e" context.
as_fn_exit ()
{
  set +e
  as_fn_set_status $1
  exit $1
} # as_fn_exit

# as_fn_unset VAR
# ---------------
# Portably unset VAR.
as_fn_unset ()
{
  { eval $1=; unset $1;}
}
as_unset=as_fn_unset

# as_fn_append VAR VALUE
# ----------------------
# Append the text in VALUE to the end of the definition contained in VAR. Take
# advantage of any shell optimizations that allow amortized linear growth over
# repeated appends, instead of the typical quadratic growth present in naive
# implementations.
if (eval "as_var=1; as_var+=2; test x\$as_var = x12") 2>/dev/null
then :
  eval 'as_fn_append ()
  {
    eval $1+=\$2
  }'
else $as_nop
  as_fn_append ()
  {
    eval $1=\$$1\$2
  }
fi # as_fn_append

# as_fn_arith ARG...
# ------------------
# Perform arithmetic evaluation on the ARGs, and store the result in the
# global $as_val. Take advantage of shells that can avoid forks. The arguments
# must be portable across $(()) and expr.
if (eval "test \$(( 1 + 1 )) = 2") 2>/dev/null
then :
  eval 'as_fn_arith ()
  {
    as_val=$(( $* ))
  }'
else $as_nop
  as_fn_arith ()
  {
    as_val=`expr "$@" || test $? -eq 1`
  }
fi # as_fn_arith


if expr a : '\(a\)' >/dev/null 2>&1 &&
   test "X`expr 00001 : '.*\(...\)'`" = X001; then
  as_expr=expr
else
  as_expr=false
fi

if (basename -- /) >/dev/null 2>&1 && test "X`basename -- / 2>&1`" = "X/"; then
  as_basename=basename
else
  as_basename=false
fi

if (as_dir=`dirname -- /` && test "X$as_dir" = X/) >/dev/null 2>&1; then
  as_dirname=dirname
else
  as_dirname=false
fi

as_me=`$as_basename -- "$0" ||
$as_expr X/"$0" : '.*/\([^/][^/]*\)/*$' \| \
	 X"$0" : 'X\(//\)$' \| \
	 X"$0" : 'X\(/\)' \| . 2>/dev/null ||
printf "%s\n" X/"$0" |
    sed '/^.*\/\([^/][^/]*\)\/*$/{
	    s//\1/
	    q
	  }
	  /^X\/\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\/\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`

# Avoid depending upon Character Ranges.
as_cr_letters='abcdefghijklmnopqrstuvwxyz'
as_cr_LETTERS='ABCDEFGHIJKLMNOPQRSTUVWXYZ'
as_cr_Letters=$as_cr_letters$as_cr_LETTERS
as_cr_digits='0123456789'
as_cr_alnum=$as_cr_Letters$as_cr_digits


# Determine whether it's possible to make 'echo' print without a newline.
# These variables are no longer used directly by Autoconf, but are AC_SUBSTed
# for compatibility with existing Makefiles.
ECHO_C= ECHO_N= ECHO_T=
case `echo -n x` in #(((((
-n*)
  case `echo 'xy\c'` in
  *c*) ECHO_T='	';;	# ECHO_T is single tab character.
  xy)  ECHO_C='\c';;
  *)   echo `echo ksh88 bug on AIX 6.1` > /dev/null
       ECHO_T='	';;
  esac;;
*)
  ECHO_N='-n';;
esac

# For backward compatibility with old third-party macros, we provide
# the shell variables $as_echo and $as_echo_n.  New code should use
# AS_ECHO(["message"]) and AS_ECHO_N(["message"]), respectively.
as_echo='printf %s\n'
as_echo_n='printf %s'

rm -f conf$$ conf$$.exe conf$$.file
if test -d conf$$.dir; then
  rm -f conf$$.dir/conf$$.file
else
  rm -f conf$$.dir
  mkdir conf$$.dir 2>/dev/null
fi
if (echo >conf$$.file) 2>/dev/null; then
  if ln -s conf$$.file conf$$ 2>/dev/null; then
    as_ln_s='ln -s'
    # ... but there are two gotchas:
    # 1) On MSYS, both `ln -s file dir' and `ln file dir' fail.
    # 2) DJGPP < 2.04 has no symlinks; `ln -s' creates a wrapper executable.
    # In both cases, we have to default to `cp -pR'.
    ln -s conf$$.file conf$$.dir 2>/dev/null && test ! -f conf$$.exe ||
      as_ln_s='cp -pR'
  elif ln conf$$.file conf$$ 2>/dev/null; then
    as_ln_s=ln
  else
    as_ln_s='cp -pR'
  fi
else
  as_ln_s='cp -pR'
fi
rm -f conf$$ conf$$.exe conf$$.dir/conf$$.file conf$$.file
rmdir conf$$.dir 2>/dev/null


# as_fn_mkdir_p
# -------------
# Create "$as_dir" as a directory, including parents if necessary.
as_fn_mkdir_p ()
{

  case $as_dir in #(
  -*) as_dir=./$as_dir;;
  esac
  test -d "$as_dir" || eval $as_mkdir_p || {
    as_dirs=
    while :; do
      case $as_dir in #(
      *\'*) as_qdir=`printf "%s\n" "$as_dir" | sed "s/'/'\\\\\\\\''/g"`;; #'(
      *) as_qdir=$as_dir;;
      esac
      as_dirs="'$as_qdir' $as_dirs"
      as_dir=`$as_dirname -- "$as_dir" ||
$as_expr X"$as_dir" : 'X\(.*[^/]\)//*[^/][^/]*/*$' \| \
	 X"$as_dir" : 'X\(//\)[^/]' \| \
	 X"$as_dir" : 'X\(//\)$' \| \
	 X"$as_dir" : 'X\(/\)' \| . 2>/dev/null ||
printf "%s\n" X"$as_dir" |
    sed '/^X\(.*[^/]\)\/\/*[^/][^/]*\/*$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)[^/].*/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`
      test -d "$as_dir" && break
    done
    test -z "$as_dirs" || eval "mkdir $as_dirs"
  } || test -d "$as_dir" || as_fn_error $? "cannot create directory $as_dir"


} # as_fn_mkdir_p
if mkdir -p . 2>/dev/null; then
  as_mkdir_p='mkdir -p "$as_dir"'
else
  test -d ./-p && rmdir ./-p
  as_mkdir_p=false
fi


# as_fn_executable_p FILE
# -----------------------
# Test if FILE is an executable regular file.
as_fn_executable_p ()
{
  test -f "$1" && test -x "$1"
} # as_fn_executable_p
as_test_x='test -x'
as_executable_p=as_fn_executable_p

# Sed expression to map a string onto a valid CPP name.
as_tr_cpp="eval sed 'y%*$as_cr_letters%P$as_cr_LETTERS%;s%[^_$as_cr_alnum]%_%g'"

# Sed expression to map a string onto a valid variable name.
as_tr_sh="eval sed 'y%*+%pp%;s%[^_$as_cr_alnum]%_%g'"


exec 6>&1
## ----------------------------------- ##
## Main body of $CONFIG_STATUS script. ##
## ----------------------------------- ##
_ASEOF
test $as_write_fail = 0 && chmod +x $CONFIG_STATUS || ac_write_fail=1

cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
# Save the log message, to keep $0 and so on meaningful, and to
# report actual input values of CONFIG_FILES etc. instead of their
# values after options handling.
ac_log="
This file was extended by fltk $as_me 1.4.4, which was
generated by GNU Autoconf 2.71.  Invocation command line was

  CONFIG_FILES    = $CONFIG_FILES
  CONFIG_HEADERS  = $CONFIG_HEADERS
  CONFIG_LINKS    = $CONFIG_LINKS
  CONFIG_COMMANDS = $CONFIG_COMMANDS
  $ $0 $@

on `(hostname || uname -n) 2>/dev/null | sed 1q`
"

_ACEOF

case $ac_config_files in *"
"*) set x $ac_config_files; shift; ac_config_files=$*;;
esac

case $ac_config_headers in *"
"*) set x $ac_config_headers; shift; ac_config_headers=$*;;
esac


cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
# Files that config.status was made for.
config_files="$ac_config_files"
config_headers="$ac_config_headers"

_ACEOF

cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
ac_cs_usage="\
\`$as_me' instantiates files and other configuration actions
from templates according to the current configuration.  Unless the files
and actions are specified as TAGs, all are instantiated by default.

Usage: $0 [OPTION]... [TAG]...

  -h, --help       print this help, then exit
  -V, --version    print version number and configuration settings, then exit
      --config     print configuration, then exit
  -q, --quiet, --silent
                   do not print progress messages
  -d, --debug      don't remove temporary files
      --recheck    update $as_me by reconfiguring in the same conditions
      --file=FILE[:TEMPLATE]
                   instantiate the configuration file FILE
      --header=FILE[:TEMPLATE]
                   instantiate the configuration header FILE

Configuration files:
$config_files

Configuration headers:
$config_headers

Report bugs to <https://github.com/fltk/fltk/issues>.
fltk home page: <https://www.fltk.org/>."

_ACEOF
ac_cs_config=`printf "%s\n" "$ac_configure_args" | sed "$ac_safe_unquote"`
ac_cs_config_escaped=`printf "%s\n" "$ac_cs_config" | sed "s/^ //; s/'/'\\\\\\\\''/g"`
cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
ac_cs_config='$ac_cs_config_escaped'
ac_cs_version="\\
fltk config.status 1.4.4
configured by $0, generated by GNU Autoconf 2.71,
  with options \\"\$ac_cs_config\\"

Copyright (C) 2021 Free Software Foundation, Inc.
This config.status script is free software; the Free Software Foundation
gives unlimited permission to copy, distribute and modify it."

ac_pwd='$ac_pwd'
srcdir='$srcdir'
INSTALL='$INSTALL'
test -n "\$AWK" || AWK=awk
_ACEOF

cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
# The default lists apply if the user does not specify any file.
ac_need_defaults=:
while test $# != 0
do
  case $1 in
  --*=?*)
    ac_option=`expr "X$1" : 'X\([^=]*\)='`
    ac_optarg=`expr "X$1" : 'X[^=]*=\(.*\)'`
    ac_shift=:
    ;;
  --*=)
    ac_option=`expr "X$1" : 'X\([^=]*\)='`
    ac_optarg=
    ac_shift=:
    ;;
  *)
    ac_option=$1
    ac_optarg=$2
    ac_shift=shift
    ;;
  esac

  case $ac_option in
  # Handling of the options.
  -recheck | --recheck | --rechec | --reche | --rech | --rec | --re | --r)
    ac_cs_recheck=: ;;
  --version | --versio | --versi | --vers | --ver | --ve | --v | -V )
    printf "%s\n" "$ac_cs_version"; exit ;;
  --config | --confi | --conf | --con | --co | --c )
    printf "%s\n" "$ac_cs_config"; exit ;;
  --debug | --debu | --deb | --de | --d | -d )
    debug=: ;;
  --file | --fil | --fi | --f )
    $ac_shift
    case $ac_optarg in
    *\'*) ac_optarg=`printf "%s\n" "$ac_optarg" | sed "s/'/'\\\\\\\\''/g"` ;;
    '') as_fn_error $? "missing file argument" ;;
    esac
    as_fn_append CONFIG_FILES " '$ac_optarg'"
    ac_need_defaults=false;;
  --header | --heade | --head | --hea )
    $ac_shift
    case $ac_optarg in
    *\'*) ac_optarg=`printf "%s\n" "$ac_optarg" | sed "s/'/'\\\\\\\\''/g"` ;;
    esac
    as_fn_append CONFIG_HEADERS " '$ac_optarg'"
    ac_need_defaults=false;;
  --he | --h)
    # Conflict between --help and --header
    as_fn_error $? "ambiguous option: \`$1'
Try \`$0 --help' for more information.";;
  --help | --hel | -h )
    printf "%s\n" "$ac_cs_usage"; exit ;;
  -q | -quiet | --quiet | --quie | --qui | --qu | --q \
  | -silent | --silent | --silen | --sile | --sil | --si | --s)
    ac_cs_silent=: ;;

  # This is an error.
  -*) as_fn_error $? "unrecognized option: \`$1'
Try \`$0 --help' for more information." ;;

  *) as_fn_append ac_config_targets " $1"
     ac_need_defaults=false ;;

  esac
  shift
done

ac_configure_extra_args=

if $ac_cs_silent; then
  exec 6>/dev/null
  ac_configure_extra_args="$ac_configure_extra_args --silent"
fi

_ACEOF
cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
if \$ac_cs_recheck; then
  set X $SHELL '$0' $ac_configure_args \$ac_configure_extra_args --no-create --no-recursion
  shift
  \printf "%s\n" "running CONFIG_SHELL=$SHELL \$*" >&6
  CONFIG_SHELL='$SHELL'
  export CONFIG_SHELL
  exec "\$@"
fi

_ACEOF
cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
exec 5>>config.log
{
  echo
  sed 'h;s/./-/g;s/^.../## /;s/...$/ ##/;p;x;p;x' <<_ASBOX
## Running $as_me. ##
_ASBOX
  printf "%s\n" "$ac_log"
} >&5

_ACEOF
cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
_ACEOF

cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1

# Handling of arguments.
for ac_config_target in $ac_config_targets
do
  case $ac_config_target in
    "config.h") CONFIG_HEADERS="$CONFIG_HEADERS config.h:configh.in" ;;
    "FL/fl_config.h") CONFIG_HEADERS="$CONFIG_HEADERS FL/fl_config.h:fl_config.in" ;;
    "makeinclude") CONFIG_FILES="$CONFIG_FILES makeinclude" ;;
    "fltk.list") CONFIG_FILES="$CONFIG_FILES fltk.list" ;;
    "fltk-config") CONFIG_FILES="$CONFIG_FILES fltk-config" ;;
    "fltk.spec") CONFIG_FILES="$CONFIG_FILES fltk.spec" ;;
    "FL/Makefile") CONFIG_FILES="$CONFIG_FILES FL/Makefile" ;;

  *) as_fn_error $? "invalid argument: \`$ac_config_target'" "$LINENO" 5;;
  esac
done


# If the user did not use the arguments to specify the items to instantiate,
# then the envvar interface is used.  Set only those that are not.
# We use the long form for the default assignment because of an extremely
# bizarre bug on SunOS 4.1.3.
if $ac_need_defaults; then
  test ${CONFIG_FILES+y} || CONFIG_FILES=$config_files
  test ${CONFIG_HEADERS+y} || CONFIG_HEADERS=$config_headers
fi

# Have a temporary directory for convenience.  Make it in the build tree
# simply because there is no reason against having it here, and in addition,
# creating and moving files from /tmp can sometimes cause problems.
# Hook for its removal unless debugging.
# Note that there is a small window in which the directory will not be cleaned:
# after its creation but before its name has been assigned to `$tmp'.
$debug ||
{
  tmp= ac_tmp=
  trap 'exit_status=$?
  : "${ac_tmp:=$tmp}"
  { test ! -d "$ac_tmp" || rm -fr "$ac_tmp"; } && exit $exit_status
' 0
  trap 'as_fn_exit 1' 1 2 13 15
}
# Create a (secure) tmp directory for tmp files.

{
  tmp=`(umask 077 && mktemp -d "./confXXXXXX") 2>/dev/null` &&
  test -d "$tmp"
}  ||
{
  tmp=./conf$$-$RANDOM
  (umask 077 && mkdir "$tmp")
} || as_fn_error $? "cannot create a temporary directory in ." "$LINENO" 5
ac_tmp=$tmp

# Set up the scripts for CONFIG_FILES section.
# No need to generate them if there are no CONFIG_FILES.
# This happens for instance with `./config.status config.h'.
if test -n "$CONFIG_FILES"; then


ac_cr=`echo X | tr X '\015'`
# On cygwin, bash can eat \r inside `` if the user requested igncr.
# But we know of no other shell where ac_cr would be empty at this
# point, so we can use a bashism as a fallback.
if test "x$ac_cr" = x; then
  eval ac_cr=\$\'\\r\'
fi
ac_cs_awk_cr=`$AWK 'BEGIN { print "a\rb" }' </dev/null 2>/dev/null`
if test "$ac_cs_awk_cr" = "a${ac_cr}b"; then
  ac_cs_awk_cr='\\r'
else
  ac_cs_awk_cr=$ac_cr
fi

echo 'BEGIN {' >"$ac_tmp/subs1.awk" &&
_ACEOF


{
  echo "cat >conf$$subs.awk <<_ACEOF" &&
  echo "$ac_subst_vars" | sed 's/.*/&!$&$ac_delim/' &&
  echo "_ACEOF"
} >conf$$subs.sh ||
  as_fn_error $? "could not make $CONFIG_STATUS" "$LINENO" 5
ac_delim_num=`echo "$ac_subst_vars" | grep -c '^'`
ac_delim='%!_!# '
for ac_last_try in false false false false false :; do
  . ./conf$$subs.sh ||
    as_fn_error $? "could not make $CONFIG_STATUS" "$LINENO" 5

  ac_delim_n=`sed -n "s/.*$ac_delim\$/X/p" conf$$subs.awk | grep -c X`
  if test $ac_delim_n = $ac_delim_num; then
    break
  elif $ac_last_try; then
    as_fn_error $? "could not make $CONFIG_STATUS" "$LINENO" 5
  else
    ac_delim="$ac_delim!$ac_delim _$ac_delim!! "
  fi
done
rm -f conf$$subs.sh

cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
cat >>"\$ac_tmp/subs1.awk" <<\\_ACAWK &&
_ACEOF
sed -n '
h
s/^/S["/; s/!.*/"]=/
p
g
s/^[^!]*!//
:repl
t repl
s/'"$ac_delim"'$//
t delim
:nl
h
s/\(.\{148\}\)..*/\1/
t more1
s/["\\]/\\&/g; s/^/"/; s/$/\\n"\\/
p
n
b repl
:more1
s/["\\]/\\&/g; s/^/"/; s/$/"\\/
p
g
s/.\{148\}//
t nl
:delim
h
s/\(.\{148\}\)..*/\1/
t more2
s/["\\]/\\&/g; s/^/"/; s/$/"/
p
b
:more2
s/["\\]/\\&/g; s/^/"/; s/$/"\\/
p
g
s/.\{148\}//
t delim
' <conf$$subs.awk | sed '
/^[^""]/{
  N
  s/\n//
}
' >>$CONFIG_STATUS || ac_write_fail=1
rm -f conf$$subs.awk
cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
_ACAWK
cat >>"\$ac_tmp/subs1.awk" <<_ACAWK &&
  for (key in S) S_is_set[key] = 1
  FS = ""

}
{
  line = $ 0
  nfields = split(line, field, "@")
  substed = 0
  len = length(field[1])
  for (i = 2; i < nfields; i++) {
    key = field[i]
    keylen = length(key)
    if (S_is_set[key]) {
      value = S[key]
      line = substr(line, 1, len) "" value "" substr(line, len + keylen + 3)
      len += length(value) + length(field[++i])
      substed = 1
    } else
      len += 1 + keylen
  }

  print line
}

_ACAWK
_ACEOF
cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
if sed "s/$ac_cr//" < /dev/null > /dev/null 2>&1; then
  sed "s/$ac_cr\$//; s/$ac_cr/$ac_cs_awk_cr/g"
else
  cat
fi < "$ac_tmp/subs1.awk" > "$ac_tmp/subs.awk" \
  || as_fn_error $? "could not setup config files machinery" "$LINENO" 5
_ACEOF

# VPATH may cause trouble with some makes, so we remove sole $(srcdir),
# ${srcdir} and @srcdir@ entries from VPATH if srcdir is ".", strip leading and
# trailing colons and then remove the whole line if VPATH becomes empty
# (actually we leave an empty line to preserve line numbers).
if test "x$srcdir" = x.; then
  ac_vpsub='/^[	 ]*VPATH[	 ]*=[	 ]*/{
h
s///
s/^/:/
s/[	 ]*$/:/
s/:\$(srcdir):/:/g
s/:\${srcdir}:/:/g
s/:@srcdir@:/:/g
s/^:*//
s/:*$//
x
s/\(=[	 ]*\).*/\1/
G
s/\n//
s/^[^=]*=[	 ]*$//
}'
fi

cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
fi # test -n "$CONFIG_FILES"

# Set up the scripts for CONFIG_HEADERS section.
# No need to generate them if there are no CONFIG_HEADERS.
# This happens for instance with `./config.status Makefile'.
if test -n "$CONFIG_HEADERS"; then
cat >"$ac_tmp/defines.awk" <<\_ACAWK ||
BEGIN {
_ACEOF

# Transform confdefs.h into an awk script `defines.awk', embedded as
# here-document in config.status, that substitutes the proper values into
# config.h.in to produce config.h.

# Create a delimiter string that does not exist in confdefs.h, to ease
# handling of long lines.
ac_delim='%!_!# '
for ac_last_try in false false :; do
  ac_tt=`sed -n "/$ac_delim/p" confdefs.h`
  if test -z "$ac_tt"; then
    break
  elif $ac_last_try; then
    as_fn_error $? "could not make $CONFIG_HEADERS" "$LINENO" 5
  else
    ac_delim="$ac_delim!$ac_delim _$ac_delim!! "
  fi
done

# For the awk script, D is an array of macro values keyed by name,
# likewise P contains macro parameters if any.  Preserve backslash
# newline sequences.

ac_word_re=[_$as_cr_Letters][_$as_cr_alnum]*
sed -n '
s/.\{148\}/&'"$ac_delim"'/g
t rset
:rset
s/^[	 ]*#[	 ]*define[	 ][	 ]*/ /
t def
d
:def
s/\\$//
t bsnl
s/["\\]/\\&/g
s/^ \('"$ac_word_re"'\)\(([^()]*)\)[	 ]*\(.*\)/P["\1"]="\2"\
D["\1"]=" \3"/p
s/^ \('"$ac_word_re"'\)[	 ]*\(.*\)/D["\1"]=" \2"/p
d
:bsnl
s/["\\]/\\&/g
s/^ \('"$ac_word_re"'\)\(([^()]*)\)[	 ]*\(.*\)/P["\1"]="\2"\
D["\1"]=" \3\\\\\\n"\\/p
t cont
s/^ \('"$ac_word_re"'\)[	 ]*\(.*\)/D["\1"]=" \2\\\\\\n"\\/p
t cont
d
:cont
n
s/.\{148\}/&'"$ac_delim"'/g
t clear
:clear
s/\\$//
t bsnlc
s/["\\]/\\&/g; s/^/"/; s/$/"/p
d
:bsnlc
s/["\\]/\\&/g; s/^/"/; s/$/\\\\\\n"\\/p
b cont
' <confdefs.h | sed '
s/'"$ac_delim"'/"\\\
"/g' >>$CONFIG_STATUS || ac_write_fail=1

cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
  for (key in D) D_is_set[key] = 1
  FS = ""
}
/^[\t ]*#[\t ]*(define|undef)[\t ]+$ac_word_re([\t (]|\$)/ {
  line = \$ 0
  split(line, arg, " ")
  if (arg[1] == "#") {
    defundef = arg[2]
    mac1 = arg[3]
  } else {
    defundef = substr(arg[1], 2)
    mac1 = arg[2]
  }
  split(mac1, mac2, "(") #)
  macro = mac2[1]
  prefix = substr(line, 1, index(line, defundef) - 1)
  if (D_is_set[macro]) {
    # Preserve the white space surrounding the "#".
    print prefix "define", macro P[macro] D[macro]
    next
  } else {
    # Replace #undef with comments.  This is necessary, for example,
    # in the case of _POSIX_SOURCE, which is predefined and required
    # on some systems where configure will not decide to define it.
    if (defundef == "undef") {
      print "/*", prefix defundef, macro, "*/"
      next
    }
  }
}
{ print }
_ACAWK
_ACEOF
cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
  as_fn_error $? "could not setup config headers machinery" "$LINENO" 5
fi # test -n "$CONFIG_HEADERS"


eval set X "  :F $CONFIG_FILES  :H $CONFIG_HEADERS    "
shift
for ac_tag
do
  case $ac_tag in
  :[FHLC]) ac_mode=$ac_tag; continue;;
  esac
  case $ac_mode$ac_tag in
  :[FHL]*:*);;
  :L* | :C*:*) as_fn_error $? "invalid tag \`$ac_tag'" "$LINENO" 5;;
  :[FH]-) ac_tag=-:-;;
  :[FH]*) ac_tag=$ac_tag:$ac_tag.in;;
  esac
  ac_save_IFS=$IFS
  IFS=:
  set x $ac_tag
  IFS=$ac_save_IFS
  shift
  ac_file=$1
  shift

  case $ac_mode in
  :L) ac_source=$1;;
  :[FH])
    ac_file_inputs=
    for ac_f
    do
      case $ac_f in
      -) ac_f="$ac_tmp/stdin";;
      *) # Look for the file first in the build tree, then in the source tree
	 # (if the path is not absolute).  The absolute path cannot be DOS-style,
	 # because $ac_f cannot contain `:'.
	 test -f "$ac_f" ||
	   case $ac_f in
	   [\\/$]*) false;;
	   *) test -f "$srcdir/$ac_f" && ac_f="$srcdir/$ac_f";;
	   esac ||
	   as_fn_error 1 "cannot find input file: \`$ac_f'" "$LINENO" 5;;
      esac
      case $ac_f in *\'*) ac_f=`printf "%s\n" "$ac_f" | sed "s/'/'\\\\\\\\''/g"`;; esac
      as_fn_append ac_file_inputs " '$ac_f'"
    done

    # Let's still pretend it is `configure' which instantiates (i.e., don't
    # use $as_me), people would be surprised to read:
    #    /* config.h.  Generated by config.status.  */
    configure_input='Generated from '`
	  printf "%s\n" "$*" | sed 's|^[^:]*/||;s|:[^:]*/|, |g'
	`' by configure.'
    if test x"$ac_file" != x-; then
      configure_input="$ac_file.  $configure_input"
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: creating $ac_file" >&5
printf "%s\n" "$as_me: creating $ac_file" >&6;}
    fi
    # Neutralize special characters interpreted by sed in replacement strings.
    case $configure_input in #(
    *\&* | *\|* | *\\* )
       ac_sed_conf_input=`printf "%s\n" "$configure_input" |
       sed 's/[\\\\&|]/\\\\&/g'`;; #(
    *) ac_sed_conf_input=$configure_input;;
    esac

    case $ac_tag in
    *:-:* | *:-) cat >"$ac_tmp/stdin" \
      || as_fn_error $? "could not create $ac_file" "$LINENO" 5 ;;
    esac
    ;;
  esac

  ac_dir=`$as_dirname -- "$ac_file" ||
$as_expr X"$ac_file" : 'X\(.*[^/]\)//*[^/][^/]*/*$' \| \
	 X"$ac_file" : 'X\(//\)[^/]' \| \
	 X"$ac_file" : 'X\(//\)$' \| \
	 X"$ac_file" : 'X\(/\)' \| . 2>/dev/null ||
printf "%s\n" X"$ac_file" |
    sed '/^X\(.*[^/]\)\/\/*[^/][^/]*\/*$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)[^/].*/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`
  as_dir="$ac_dir"; as_fn_mkdir_p
  ac_builddir=.

case "$ac_dir" in
.) ac_dir_suffix= ac_top_builddir_sub=. ac_top_build_prefix= ;;
*)
  ac_dir_suffix=/`printf "%s\n" "$ac_dir" | sed 's|^\.[\\/]||'`
  # A ".." for each directory in $ac_dir_suffix.
  ac_top_builddir_sub=`printf "%s\n" "$ac_dir_suffix" | sed 's|/[^\\/]*|/..|g;s|/||'`
  case $ac_top_builddir_sub in
  "") ac_top_builddir_sub=. ac_top_build_prefix= ;;
  *)  ac_top_build_prefix=$ac_top_builddir_sub/ ;;
  esac ;;
esac
ac_abs_top_builddir=$ac_pwd
ac_abs_builddir=$ac_pwd$ac_dir_suffix
# for backward compatibility:
ac_top_builddir=$ac_top_build_prefix

case $srcdir in
  .)  # We are building in place.
    ac_srcdir=.
    ac_top_srcdir=$ac_top_builddir_sub
    ac_abs_top_srcdir=$ac_pwd ;;
  [\\/]* | ?:[\\/]* )  # Absolute name.
    ac_srcdir=$srcdir$ac_dir_suffix;
    ac_top_srcdir=$srcdir
    ac_abs_top_srcdir=$srcdir ;;
  *) # Relative name.
    ac_srcdir=$ac_top_build_prefix$srcdir$ac_dir_suffix
    ac_top_srcdir=$ac_top_build_prefix$srcdir
    ac_abs_top_srcdir=$ac_pwd/$srcdir ;;
esac
ac_abs_srcdir=$ac_abs_top_srcdir$ac_dir_suffix


  case $ac_mode in
  :F)
  #
  # CONFIG_FILE
  #

  case $INSTALL in
  [\\/$]* | ?:[\\/]* ) ac_INSTALL=$INSTALL ;;
  *) ac_INSTALL=$ac_top_build_prefix$INSTALL ;;
  esac
_ACEOF

cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
# If the template does not know about datarootdir, expand it.
# FIXME: This hack should be removed a few years after 2.60.
ac_datarootdir_hack=; ac_datarootdir_seen=
ac_sed_dataroot='
/datarootdir/ {
  p
  q
}
/@datadir@/p
/@docdir@/p
/@infodir@/p
/@localedir@/p
/@mandir@/p'
case `eval "sed -n \"\$ac_sed_dataroot\" $ac_file_inputs"` in
*datarootdir*) ac_datarootdir_seen=yes;;
*@datadir@*|*@docdir@*|*@infodir@*|*@localedir@*|*@mandir@*)
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: $ac_file_inputs seems to ignore the --datarootdir setting" >&5
printf "%s\n" "$as_me: WARNING: $ac_file_inputs seems to ignore the --datarootdir setting" >&2;}
_ACEOF
cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
  ac_datarootdir_hack='
  s&@datadir@&$datadir&g
  s&@docdir@&$docdir&g
  s&@infodir@&$infodir&g
  s&@localedir@&$localedir&g
  s&@mandir@&$mandir&g
  s&\\\${datarootdir}&$datarootdir&g' ;;
esac
_ACEOF

# Neutralize VPATH when `$srcdir' = `.'.
# Shell code in configure.ac might set extrasub.
# FIXME: do we really want to maintain this feature?
cat >>$CONFIG_STATUS <<_ACEOF || ac_write_fail=1
ac_sed_extra="$ac_vpsub
$extrasub
_ACEOF
cat >>$CONFIG_STATUS <<\_ACEOF || ac_write_fail=1
:t
/@[a-zA-Z_][a-zA-Z_0-9]*@/!b
s|@configure_input@|$ac_sed_conf_input|;t t
s&@top_builddir@&$ac_top_builddir_sub&;t t
s&@top_build_prefix@&$ac_top_build_prefix&;t t
s&@srcdir@&$ac_srcdir&;t t
s&@abs_srcdir@&$ac_abs_srcdir&;t t
s&@top_srcdir@&$ac_top_srcdir&;t t
s&@abs_top_srcdir@&$ac_abs_top_srcdir&;t t
s&@builddir@&$ac_builddir&;t t
s&@abs_builddir@&$ac_abs_builddir&;t t
s&@abs_top_builddir@&$ac_abs_top_builddir&;t t
s&@INSTALL@&$ac_INSTALL&;t t
$ac_datarootdir_hack
"
eval sed \"\$ac_sed_extra\" "$ac_file_inputs" | $AWK -f "$ac_tmp/subs.awk" \
  >$ac_tmp/out || as_fn_error $? "could not create $ac_file" "$LINENO" 5

test -z "$ac_datarootdir_hack$ac_datarootdir_seen" &&
  { ac_out=`sed -n '/\${datarootdir}/p' "$ac_tmp/out"`; test -n "$ac_out"; } &&
  { ac_out=`sed -n '/^[	 ]*datarootdir[	 ]*:*=/p' \
      "$ac_tmp/out"`; test -z "$ac_out"; } &&
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: $ac_file contains a reference to the variable \`datarootdir'
which seems to be undefined.  Please make sure it is defined" >&5
printf "%s\n" "$as_me: WARNING: $ac_file contains a reference to the variable \`datarootdir'
which seems to be undefined.  Please make sure it is defined" >&2;}

  rm -f "$ac_tmp/stdin"
  case $ac_file in
  -) cat "$ac_tmp/out" && rm -f "$ac_tmp/out";;
  *) rm -f "$ac_file" && mv "$ac_tmp/out" "$ac_file";;
  esac \
  || as_fn_error $? "could not create $ac_file" "$LINENO" 5
 ;;
  :H)
  #
  # CONFIG_HEADER
  #
  if test x"$ac_file" != x-; then
    {
      printf "%s\n" "/* $configure_input  */" >&1 \
      && eval '$AWK -f "$ac_tmp/defines.awk"' "$ac_file_inputs"
    } >"$ac_tmp/config.h" \
      || as_fn_error $? "could not create $ac_file" "$LINENO" 5
    if diff "$ac_file" "$ac_tmp/config.h" >/dev/null 2>&1; then
      { printf "%s\n" "$as_me:${as_lineno-$LINENO}: $ac_file is unchanged" >&5
printf "%s\n" "$as_me: $ac_file is unchanged" >&6;}
    else
      rm -f "$ac_file"
      mv "$ac_tmp/config.h" "$ac_file" \
	|| as_fn_error $? "could not create $ac_file" "$LINENO" 5
    fi
  else
    printf "%s\n" "/* $configure_input  */" >&1 \
      && eval '$AWK -f "$ac_tmp/defines.awk"' "$ac_file_inputs" \
      || as_fn_error $? "could not create -" "$LINENO" 5
  fi
 ;;


  esac

done # for ac_tag


as_fn_exit 0
_ACEOF
ac_clean_files=$ac_clean_files_save

test $ac_write_fail = 0 ||
  as_fn_error $? "write failure creating $CONFIG_STATUS" "$LINENO" 5


# configure is writing to config.log, and then calls config.status.
# config.status does its own redirection, appending to config.log.
# Unfortunately, on DOS this fails, as config.log is still kept open
# by configure, so config.status won't be able to write to it; its
# output is simply discarded.  So we exec the FD to /dev/null,
# effectively closing config.log, so it can be properly (re)opened and
# appended to by config.status.  When coming back to configure, we
# need to make the FD available again.
if test "$no_create" != yes; then
  ac_cs_success=:
  ac_config_status_args=
  test "$silent" = yes &&
    ac_config_status_args="$ac_config_status_args --quiet"
  exec 5>/dev/null
  $SHELL $CONFIG_STATUS $ac_config_status_args || ac_cs_success=false
  exec 5>>config.log
  # Use ||, not &&, to avoid exiting from the if with $? = 1, which
  # would make configure fail if this is the last instruction.
  $ac_cs_success || as_fn_exit 1
fi
if test -n "$ac_unrecognized_opts" && test "$enable_option_checking" != no; then
  { printf "%s\n" "$as_me:${as_lineno-$LINENO}: WARNING: unrecognized options: $ac_unrecognized_opts" >&5
printf "%s\n" "$as_me: WARNING: unrecognized options: $ac_unrecognized_opts" >&2;}
fi


chmod +x fltk-config

echo ""
echo "-------------------------------------------------------------------------"
echo "  IMPORTANT NOTE:"
echo "-------------------------------------------------------------------------"
echo ""
echo "  FLTK 1.4.x will be the last version(s) of FLTK supporting"
echo "  autotools (configure + provided Makefiles) to build FLTK."
echo "  FLTK 1.5.0 and higher will only support FLTK builds using CMake."
echo ""
echo "  We suggest to explore and use the CMake build system generators"
echo "  for your own FLTK builds as soon as possible. Some new FLTK build"
echo "  options will only be supported by CMake based builds."
echo "  Please see README.CMake.txt for details and instructions."
echo ""
echo "  User projects that use CMake for their own build can benefit"
echo "  substantially if the FLTK library has been built using CMake."
echo ""
echo "-------------------------------------------------------------------------"
echo ""

