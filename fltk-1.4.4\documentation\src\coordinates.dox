/**

 \page coordinates Coordinates and Layout Widgets

This chapter describes the coordinate systems that apply when
positioning widgets manually, and some of the basics of FLTK
layout widgets that are used to position widgets automatically.


\section coordinates_coordinates The Widget Coordinate System

All widgets have constructors with \p x and \p y parameters to
let the programmer specify the desired initial position of the
top left corner during explicit manual layout within Fl_Window
and Fl_Group container widgets.

This position is always relative to the enclosing Fl_Window,
which is usually, but not always, the top-level application
window, or a free-floating pop-up dialog window.
In some cases it could also be a subwindow embedded in a
higher-level window, as shown in the figure below.

\image html  coordinates.png "FLTK coordinate system"
\image latex coordinates.png "FLTK coordinate system" width=10cm

The positions of the TL and BR sub-windows and the TR and BL
groups are all relative to the top-left corner of the main window.
The positions of the boxes inside the TR and BL groups are also
relative to the main window, but the boxes inside the TL and BR
sub-windows are positioned relative to the enclosing sub-window.

In other words, the widget hierarchy and positions can be summarized as:
<pre>
    Fl_Window main window
      Fl_Window TL subwindow     # x, y relative to main window
        Fl_Box  tl box           # x, y relative to TL subwindow
      Fl_Window BR subwindow     # x, y relative to main window
        Fl_Box  br box           # x, y relative to BR subwindow
      Fl_Group  TR group         # x, y relative to main window
        Fl_Box  tr box           # x, y relative to main window
      Fl_Group  BL group         # x, y relative to main window
        Fl_Box  bl box           # x, y relative to main window
</pre>


\section coordinate_layout Layout and Container Widgets

There are four main groups of widgets derived from Fl_Group for
a range of different purposes.

The first group are composite widgets that each contain a fixed
set of components that work together for a specific purpose,
rather than layout widgets as such, and are not discussed here.

The second group are basically containers offering the same manual
layout features as Fl_Group, as described above, but which add one
new capability. These widgets are Fl_Scroll, Fl_Tabs and Fl_Wizard.

The third group are layout managers that relocate and resize the
child widgets added to them in order to satisfy a particular layout
algorithm. These widgets are Fl_Flex, Fl_Grid, Fl_Pack, and Fl_Tile.

The final group consists of Fl_Window and its derivatives.
Their special capability is that they can be top-level application
windows and dialogs that interface with the operating system window
manager, but can also be embedded within other windows and groups
as shown in the example above.
Note that the window manager may impose its own constraints on
the position of top-level windows, and the \p x and \p y
position parameters may be treated as hints, or even ignored.
The Fl_Window class has an extra constructor that omits them.

Descriptions of layout and container widgets follow in alphabetical order.


\subsection coordinates_flex The Fl_Flex Layout Widget

The Fl_Flex widget allows the layout of its direct children as a single row
or column. If its type() is set to give the row or horizontal layout, the
children are all resized to have the same height as the Fl_Flex and are
moved next to each other. If set to give the column or vertical layout,
the children are all resized to have the same width as the Fl_Flex and
are then stacked below each other.

Widget positions (x, y) need not be given by the user because widgets are
positioned inside the Fl_Flex container in the order of its children.
Widget sizes can be set to (0, 0) as in Fl_Pack since they are calculated
by Fl_Flex.

This is similar to Fl_Pack described below and Fl_Flex is designed to
act as a drop-in replacement of Fl_Pack with some minor differences.

Other than Fl_Pack the Fl_Flex widget does \b not resize itself but resizes
its children to fill the entire space of the Fl_Flex container. Single
children of Fl_Flex can be set to fixed sizes to inhibit this resizing
behavior. In this case the remaining space is distributed to all
non-fixed widgets.

Fl_Flex widgets can be nested inside each other and with Fl_Grid in any
combination.

The name Fl_Flex was inspired by the CSS 'flex' container.

\image html  Fl_Flex_simple.png "Simple Fl_Flex Layout"
\image latex Fl_Flex_simple.png "Simple Fl_Flex Layout" width=8cm

Fl_Flex was added in FLTK 1.4.0.


\subsection coordinates_grid The Fl_Grid Layout Widget

Fl_Grid is the most flexible layout container in FLTK 1.4. It is based
on a flexible grid of \b cells that can be assigned one widget per cell
which is the \e anchor of the widget. Widgets can span multiple rows
and columns and the cells can constitute a sparse matrix. Widgets can be
aligned inside their cells in several ways (left, right, top, bottom) and
can stretch horizontally, vertically, or both, i.e. fill the entire cell.

Widget positions (x, y) need not be given by the user because widgets are
assigned to a particular grid cell by row and column number. Widget sizes
can be given as their \b minimal sizes and will be resized appropriately
depending on the free space.

Optional margins around all cells inside the widget border and gaps between
rows and cells make the layout even more flexible.

The Fl_Grid widget should be designed with a grid (matrix) and its minimal
size in mind. It is designed to \b enlarge cells and widgets in a flexible
way when the Fl_Grid widget itself is created or resized.

Additional free space inside the Fl_Grid container is distributed to widgets
by considering minimal row heights, column widths, sizes of widgets, and
row and column \e weights. These weights are used to distribute the free
space proportionally according to the row and column weights.

Fl_Grid widgets can be nested inside each other and with Fl_Flex and other
subclasses of Fl_Group in any combination.

\note We don't recommend to use Fl_Pack as child widgets although this
  \b may work as well.

The name Fl_Grid was inspired by the CSS 'grid' container but it has
some properties in common with HTML \<table\> containers as well, for
instance row and column spanning.

\image html  Fl_Grid.png "Simple Fl_Grid Layout"
\image latex Fl_Grid.png "Simple Fl_Grid Layout" width=8cm

Fl_Grid was added in FLTK 1.4.0.


\subsection coordinates_pack The Fl_Pack Layout Widget

The Fl_Pack widget allows the layout of its direct children as a
single row, or column.
If its type() is set to give the row or horizontal layout,
the children are all resized to have the same height as the Fl_Pack
and are moved next to each other.
If set to give the column or vertical layout, the children are all
resized to have the same width as the Fl_Pack and are then stacked
below each other. The Fl_Pack then resizes itself to shrink-wrap itself
around all of the children.

Fl_Pack widgets are often used inside an Fl_Scroll, as shown in the
diagram below, to avoid having to deal with tricky resize behavior
when used with nested widgets.

\image html  pack.png "Fl_Pack test program screenshot"
\image latex pack.png "Fl_Pack test program screenshot" width=8cm

Since FLTK 1.4.0 Fl_Flex (described above) can in many cases be used as
a drop-in replacement for Fl_Pack if this "shrink-wrap" behavior is not
required. Note that the Fl_Pack layout algorithm can cause some issues
because its widget size can change depending on its children and
particularly because this is done late, i.e. during draw() and not
as usual during resize of the window.

\note We recommend that developers evaluate whether using Fl_Flex or Fl_Grid
  instead of Fl_Pack can be a better solution with more predictable and
  reliable resizing behavior of the overall program layout.


\subsection coordinates_scroll The Fl_Scroll Container Widget

The Fl_Scroll container widget can hold an assortment of widgets
that may extend beyond its own width and height, in which case
horizontal and/or vertical scrollbars may appear automatically
so that you can scroll and view the entire contents.

\image html  Fl_Scroll.png "Fl_Scroll container widget"
\image latex Fl_Scroll.png "Fl_Scroll container widget" width=4cm


\subsection coordinates_tabs The Fl_Tabs Container Widget

The Fl_Tabs widget provides a front-to-back stack of individual
panels which usually contain Fl_Group widgets and their children.
The user can switch between panels by clicking on the small
tabs that protrude from the panels. The appearance of each tab
is determined by the child widget's label and related attributes.

\image html  tabs.png "Fl_Tabs container widget"
\image latex tabs.png "Fl_Tabs container widget" width=8cm


\subsection coordinates_tile The Fl_Tile Layout Widget

The Fl_Tile widget allows the user to resize one or more of its children
by dragging on the border between adjacent child widgets.
However, the programmer must first explicitly layout the child widgets
so that their borders exactly fill the width and height of the Fl_Tile
without having any gaps between them, or at the edges.
Some care is needed when initially positioning the children and setting
the resizable() widget within the Fl_Tile to prevent squeezing a child
to have a zero width or height.
For more information see the Fl_Tile widget manual page, and \ref resize.


\image html  Fl_Tile.png "The Fl_Tile layout widget"
\image latex Fl_Tile.png "The Fl_Tile layout widget" width=4cm


\subsection coordinates_wizard The Fl_Wizard Container Widget

The Fl_Wizard widget derives from the Fl_Tabs class, but instead
of having tabs that the user can click to select the corresponding
panel, the programmer uses the prev(), next() or value() methods
to show the appropriate panel.
For example, the user might be able to click on "Next" and "Prev"
navigation buttons or keys, as shown below.

\image html  wizard.png "Fl_Wizard container widget"
\image latex wizard.png "Fl_Wizard container widget" width=4cm


\htmlonly
<hr>
<table summary="navigation bar" width="100%" border="0">
<tr>
  <td width="45%" align="LEFT">
    <a class="el" href="common.html">
    [Prev]
    Common Widgets and Attributes
    </a>
  </td>
  <td width="10%" align="CENTER">
    <a class="el" href="index.html">[Index]</a>
  </td>
  <td width="45%" align="RIGHT">
    <a class="el" href="resize.html">
    How Does Resizing Work?
    [Next]
    </a>
  </td>
</tr>
</table>
\endhtmlonly

*/
