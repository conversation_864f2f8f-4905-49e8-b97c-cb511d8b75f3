#
# clang-format control file for the FLTK project.
#
# Copyright 2017-2022 by <PERSON> and others.
#
# This library is free software. Distribution and use rights are outlined in
# the file "COPYING" which should have been included with this file.  If this
# file is missing or damaged, see the license at:
#
#     https://www.fltk.org/COPYING.php
#
# Please see the following page on how to report bugs and issues:
#
#     https://www.fltk.org/bugs.php
#
#
# Important notes:
#
# This is a preliminary, experimental version of a clang-format control file.
# To use all options including embedded comments to switch formatting
# temporarily off and on in source files (see below) clang-format 3.6
# or later is required.
#
# DO NOT USE WITHOUT CHECKING THE RESULT OF FORMATTING FOR CORRECTNESS
# AND COMPATIBILITY WITH THE FLTK CMP!
# https://www.fltk.org/cmp.php#CODING_STANDARDS
#
# For more information about clang-format please refer to the online docs at:
# https://clang.llvm.org/docs/ClangFormat.html
# https://clang.llvm.org/docs/ClangFormatStyleOptions.html
#
# Embedded comments ("clang-format pragma's") in the source code:
#   // clang-format off
#   // clang-format on
#   /* clang-format off */
#   /* clang-format on */
# can be used to switch clang-format(ting) temporarily off in a source file.
# This is particularly useful for embedded pixmaps and other tables
# like menu arrays that are pre-formatted for better readability.

# The options used for FLTK are based on predefined style options of LLVM,
# which are also the default settings of clang-format.
# For a full list of LLVM settings please use
#   clang-format -style=llvm -dump-config

# FLTK settings (currently experimental).

BasedOnStyle:                                   LLVM

# The Language tag marks C++ options

# Language:                                     Cpp

# The following options override the LLVM style definitions, if set.

# Uncomment one of the following option lines if indenting with tabs
# shall be used. Note: tab spacing is still 8 columns, tabs are only
# used for indents of 8 columns or more.
#
# Option "Always" seems to fail counting columns: comments may not be
# adjusted as expected (as of clang-format 3.6 and 3.8).
# This applies only if "AlignTrailingComments: true" is also set (default).
#
# UseTab can be set to 'Never' (default) or 'ForIndentation' to avoid
# this annoying bug of clang-format.
#
# UseTab:                                       Always
UseTab:                                         Never

# Should we extend code lines beyond 80 columns ?
# Default: 80
ColumnLimit:                                    100

# The FLTK CMP requires that case labels are indented (LLVM: false)
IndentCaseLabels:                               true

# Should we keep two or more consecutive empty lines ?
# LLVM default is 1.

MaxEmptyLinesToKeep:                            2

# Present FLTK source code contains some short blocks and if statements
# in one line, but we should better make it consistent and NOT use the
# following "Allow..." statements (leave them commented out):
#
# LLVM default values:
#
# AllowShortBlocksOnASingleLine:                false
# AllowShortFunctionsOnASingleLine:             All
# AllowShortIfStatementsOnASingleLine:          false
# AllowShortLoopsOnASingleLine:                 false
#
# FLTK values:
# AllowShortBlocksOnASingleLine:                true
# AllowShortIfStatementsOnASingleLine:          true

# Short inline functions in header files are an exception to the above "rule":

AllowShortFunctionsOnASingleLine:               Inline

# The following is particularly useful for macros with continuation lines.
# LLVM default: AlignEscapedNewlinesLeft:       false

AlignEscapedNewlinesLeft:                       true

# Include files should be left as-is until we *know* we can
# sort them without bad side effects.
# LLVM default: true

SortIncludes:                                   false

# Multiple constructor initializers must be on consecutive lines.
# Note: this is NOT (always) true in current FLTK code!

BreakConstructorInitializersBeforeComma:        true

# Constructor initializers will be indented by 2 spaces.
# LLVM default: 4

ConstructorInitializerIndentWidth:              2

# Continuation lines (if automatically wrapped) may be indented differently.
# This does not apply to function call arguments which are aligned to the
# opening bracket.
# LLVM default: 4
# ContinuationIndentWidth:                      2

# Most of FLTK's code uses 'void *p' as opposed to 'void* p'.
# This is particularly useful in combined declarations like:
# int var, var2, *pv, **pp;
# Note: clang-format uses "Right" in such combined declarations anyway,
# so to be consistent the best setting appears to be "Right".
# clang-format can try to derive the setting from code in the file, but this
# is error-prone and can lead to inconsistent settings in different files.
# Note: this also applies to references like 'int &w, int &h', for instance
# in function parameter lists.

DerivePointerAlignment:                         false
PointerAlignment:                               Right
