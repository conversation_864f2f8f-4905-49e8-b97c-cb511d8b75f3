Changes in FLTK 1.0, up to FLTK 1.0.11 (May 7, 2001)

CHANGES IN FLTK 1.0.11

  - CHANGED THE DEFAULT RUN-TIME LINKING TO "MULTITHREADED DLL".
    You'll need to change your project settings to
    use this as well or you'll get errors.

  - Added new --disable-gl option to configure script.

  - Added new const const pointer versions of pixmap
    functions to eliminate an annoying pointer warning
    message that was generated by the Sun and other C++
    compilers.

  - Eliminated all "var hides class::var" warnings.

  - Eliminated all "string literal converted to char *"
    warnings.

  - OS/2 updates from <PERSON>.

  - Tidied up the HTML documentation to be more standards
    compliant.

  - Compiling with -DBOXX_BUGS will work around some
    problems with the newest X drivers that BoXX delivers,
    the problems all affect use of Overlays for normal X
    drawing and OpenGL drawing. Normal compilation is
    unchanged.

  - The file chooser buttons use user_data() rather than
    the label to decide what to do, allowing the label to
    be somewhat cleaner.

  - Selection color on X changed to blue, to match what
    happens on Windows now.

  - Added support for AIX (static library only).

  - Added support for SunOS 4.x

  - Now process WIN32 WM_ACTIVATEAPP message to reset the
    key and button states in Fl::e_state.

  - Fl_has_idle only tested N-1 callbacks and missed one.

  - Restored WM_SYNCPAINT handling under WIN32; this fixed
    a refresh bug under some versions of Windows.

  - Check for OpenGL headers before checking to see if
    OpenGL is supported.  This should eliminate compile
    errors due to missing non-FLTK header files...

  - Add -D_INCLUDE_POSIX_SOURCE option when compiling with
    the HP compilers.

  - Replaced remaining _WIN32 symbols with WIN32

  - Removed reference to unused GL/glu.h header file, which is missing on
    some Linux systems.

  - Fl_Gl_Window has a new method to allow you to get and set the context:

        void Fl_Gl_Window::context(void*, int destroy = 0)
        void* Fl_Gl_Window::context() const;

    Return or set a pointer to the GLContext that this window is
    using. This is a system-dependent structure, but it is portable to
    copy the context from one window to another. You can also set it to
    NULL, which will force FLTK to recreate the context the next time
    make_current() is called, this is useful for getting around bugs in
    OpenGL implementations.

    If destroy_flag is true the context will be destroyed by fltk when
    the window is destroyed, or when the mode() is changed, or the next
    time context(x) is called.

  - Some cleanup of Fl_Gl_Choice to move most of the system dependent
    #ifdefs into Fl_Gl_Choice.cxx.

  - Fl_Gl_Window does not set drawbuffer(BACKBUFFER) for
    single-buffered windows.

  - Fl_Input::replace(...) correctly updates the display
    if the replaced region does not include the mark,
    point, or selected region.

  - Added Fl::add_check(...), Fl::remove_check, and
    Fl::has_check. These are similar to idle callbacks but
    are only called just before it waits for new events.
    They can be used to watch for changes in global state
    and respond to them.

  - "accu-timer": some changes to repeat_timeout that seem
    to make it accurate on Unix and WIN32 at speeds up to
    500000 timeouts/second (and 700000 on Linux), and
    within about .001% as accurate as the system clock.

  - Fix to Fl_Valuator::step() by Guillermo Andrade.

  - Fixed the FLUID write-menu bug introduced in 1.0.10

  - Fl::flush() now calls GdiFlush() under WIN32 to
    ensure that all graphics are drawn.

  - fl_curve() now uses a much better algorithim to figure
    out how many pieces to cut the curve into.

  - FLUID now uses GetTempPath() under WIN32 to determine
    where to store the clipboard.

  - Right-ctrl does not delete selected text in Fl_Input,
    until you type a composed character.

  - Added simple FLTK and FLUID manual pages.

  - Fl_Gl_Window leaked memory under WIN32.

  - The colbrowser demo was missing an include file when
    compiled under OS/2.


CHANGES IN FLTK 1.0.10

  - Added a strcasecmp() function to FLUID; AIX doesn't
    have it.

  - Bug #115509: Fl_Scroll not repainting background.

  - Updated the configure script and makeinclude.in file
    to work with the Sun PRO compilers.

  - Disabled the WIN32 async socket select code by default:
    it doesn't seem to work anymore...

  - Fl::below_mouse() was incorrectly clearing e_is_click;
    this prevented any double-clicks from getting
    through...

  - No longer clear Fl::keysym on every event, this makes
    better back compatability and fixes Win2000

  - FLUID now restores which tab in an Fl_Tabs was
    selected when loads .fl files.

  - Hack to fix the annoying "raise another application
    when a modal window is closed" problem on WIN32.

  - Fl_Tabs now draws the background behind the tabs.

  - Fl::set_fonts() on WIN32 fixed to work before the
    first window is shown.

  - CUA function keys, code submitted by George Yohng
    <<EMAIL>>

  - Another attempt to get glut.h to work on WIN32.

  - Fl_Menu_::add() ignores '&' signs when comparing menu
    items, so you don't have to make the shortcuts the
    same all the time.

  - Fixed bit-flipping patterns in WIN32 bitmap code.

  - Fixed size of data written by gif images to .C files

  - Menu titles and buttons in the menubar can be images
    (allows it to be used as a toolbar)

  - Reads selectBackground from the xrdb database to set
    the selection color. Adding this to your .Xdefaults
    will make fltk and Motif programs look much more
    Windoze-like:

        *selectForeground: white
        *selectBackground: #000080

  - FL_WHEN_RELEASE on Fl_Input will now do the callback
    when the input field is hidden, for instance when it
    is on a tab and the user switches to another tab.

  - Fl_Gl_Window with an overlay on X always resized any
    child windows even if you turned resizable() off
    because it turned it back on to resize the overlay
    window. This patch avoids changing resizable().

  - Fix so multiple Fl::add_idle() calls works

  - The input focus got messed up if you called
    Fl_Tabs::value(x) and there was something that took
    focus on an earlier tab.

  - Removed some (not all) of the warnings when compiled
    with -Wwrite-strings, this should also get similar
    warnings Solaris produces.

  - Made Fl_Browser not hide the Fl_Widget::show() method

  - Changes & additions for OS/2 from Alexander Mai

  - Patch from Mike Lindner to make the turning on/off of
    scrollbars on Fl_Scroll smarter.

  - Added missing FL_EXPORT for Fl_Valuator::format()

  - Shortcuts for "buttons" in a Fl_Menu_Bar work again.

  - Fix for cut/paste support and Xdnd.

  - Shortcuts for submenu titles in a menubar pop up the
    submenu (rather than calling the callback)

  - Added documentation for GL_SWAP_TYPE

  - Buttons with box(FL_NO_BOX) did not draw.  Apparently
    they did in older versions of fltk, I restored this.
    (bug 108771)

  - Removed 8-bit colormap drawing code that was not doing
    anything in fl_draw_image due to the colormap
    allocation changes.  I also made fl_color(r,g,b)
    actually allocate the requested color rather than the
    nearest fltk color-cube color (this is only done for
    the first color that maps to a given entry in the fltk
    color cube), the result is that pixmaps with a small
    number of colors are drawn much more accurately. The
    resulting code seems to produce better images and is a
    good deal smaller!

  - Fixed makeinclude.in so CFLAGS are used for c source
    code instead of CXXFLAGS. (bug 108694)

  - Better fix for gif files suggested by pauly (bug 108770)

  - Performance of Fl_Gl_Window may be improved on some
    types of OpenGL implementations, in particular MESA
    or other software emulators, by setting the
    GL_SWAP_TYPE environment variable.  This variable
    declares what is in the back buffer after you do a
    swapbuffers:

        setenv GL_SWAP_TYPE COPY

        This indicates that the back buffer is copied to
        the front buffer, and still contains it's old
        data. This is true of many hardware
        implementations.  Setting this will speed up
        emulation of overlays, and widgets that can do
        partial update can take advantage of this as
        damage() will not be cleared to -1.

        setenv GL_SWAP_TYPE NODAMAGE

        This indicates that nothing changes the back
        buffer except drawing into it.  This is true of
        MESA and Win32 software emulation and perhaps some
        hardware emulation on systems with lots of memory.

    All other values for GL_SWAP_TYPE, and not setting
    the variable, cause fltk to assume that the back
    buffer must be completely redrawn after a swap.

    This is easily tested by running the gl_overlay demo
    program and seeing if the display is correct when
    you drag another window over it or if you drag the
    window off the screen and back on. You have to exit
    and run the program again for it to see any changes
    to the environment variable.

  - Optimized colormap usage on 8-bit displays with
    images. New code only allocates colors as they are
    needed (still converts indexed images to full RGB and
    dithers, tho...)

  - Fixed .gif files in FLUID, they were broken by the fix
    for large .xpm files in version 1.0.9.

  - Fix for OpenGL hardware overlays with the transparent
    index != 0. Tested on the brand new HP Linux
    Workstations, this is the only bug encountered.  Both
    X and OpenGL hardware overlay works perfectly on
    these, though configue may not enable it by
    default...)

  - Fl_Choice and all other Fl_Menu_ subclasses draw the
    items using textcolor() as the default color of the
    text.

  - Fix suggested by Stuart Levy to fix scrolling when
    deleting items from the browser.

  - Replaced the -$(MAKEFLAGS) with $(MFLAGS) as per the
    gmake documenation.  Apperntly this works with other
    make programs and MAKEFLAGS is passed invisibly by
    gmake, though the documenation is not too clear...


CHANGES IN FLTK 1.0.9

  - More documentation fixes.
  - GLUT_STROKE_*_ROMAN in glut.h are defined as 0,1 on
    WIN32 to match the glut header files there.
  - Added Fl::has_timeout() and Fl::has_idle() functions.
  - Added new Fl::repeat_timeout() method that
    measures time from when the last timeout was called.
    This has slightly less overhead and allows accurate
    spacing of timeouts.
  - More Cygwin changes
  - FLUID could crash with identifiers with trailing
    whitespace.
  - Fixed the XPM loading code in FLUID to handle files
    longer than 2048 lines.
  - Added a bunch of missing FL_EXTERN's to glut.h to
    eliminate GLUT linking errors under WIN32.
  - Fix for sliders so that clicking on one with a small
    (or zero) slider_size will not move the slider.
  - fl_shortcut.cxx didn't export fl_old_shortcut() in the
    WIN32 DLL.
  - Fixed xpaint link in the documentation.
  - Included Fl_Input word-wrap fixes from Alexander Rabi
    Beels. This will not affect things much because
    word-wrap is normally disabled.
  - Patch from Stuart Levy so the *last* widget in an
    Fl_Pack may be resizable.  This should be compatable
    because resizable didn't do anything before so there
    was no reason to set it.
  - Cleaned up the timeout and Fl::wait() code.  The new
    code calls the clock function less than half as much,
    which results in a noticable performance improvement
    in some apps.
  - Fl::wait(time) with a time greater than the system can
    handle (24.855 days on NT, the same on some Unix
    systems) will now act as though the time is infinity.
    Before it would do unpredictable things.
  - "USE_POLL" now compiles and works, although it is
    disabled by default. poll() is an alternative to the
    UNIX select() call which is available on some version
    of UNIX and may be faster depending on the platform;
    try it by editing config.h.
  - The WIN32 USE_ASYNC_SELECT code now does translation
    and dispatching of the select events; this makes
    Windows a lot happier.
  - Added a check for an open display in Fl::wait() so
    that you don't need an open window under X to call it.

    [changes in snapshot 2]

  - fl_old_shortcut() wasn't being exported in the WIN32 DLL project.
  - Updated Cygwin and Mingw makefiles.
  - Updated the BC++ project file.
  - You can no longer insert control chars into Fl_Int/Float_Input.
  - Fl_Multiline_Input now resets the horizontal position when
    focus is changed; this caused problems when multiple multiline
    widgets were used in an application.
  - All handle() methods are now public, and all draw() methods are
    now protected in FLTK widgets.
  - More fixes to the OpenGL overlay code on win32.  This now
    seems to work quite reliably on several different pieces of
    hardware. Apparently doing SetLayerPaletteEntries with a
    palette larger than the overlay size caused the drivers to
    screw up in unpredictable ways. Also SwapBuffers swapped both
    the overlay and main window, which is not what fltk's
    interface wanted, this was easy to fix however.
  - Patch for full scrollbars so that clicking on them does not
    move anything.
  - Documentation fixes.
  - Better horizontal scrolling of Fl_Input when cursor is near
    the end of the line.
  - Fl_Input::value(x) selects all text.
  - Fl_Output and Fl_Multiline_Output would scroll to the end
    of the text.
  - filename_isdir() now drops any trailing slash from the
    filename (needed for Windows)
  - Added return type for main() function in line_style demo.
  - Running FLUID with the "-cs" option writes the I18N message
    file.
  - The WIN32 version of XParseGeometry() didn't initialize some
    variables.  This caused a compiler warning but did not affect
    the actual code.

    [changes in snapshot 1]

  - EMail changes - <EMAIL> now officially
    <EMAIL>.
  - The FLTK DLL project file didn't include fl_compose.cxx
  - Dropped the GCC -fno-rtti option since it caused problems
    with existing programs.
  - Moved the .fl rules back to the test directory.
  - Fixed some makefile and spec file problems.
  - Fixed hardware overlays.  The problem was the new
    fl_clipped() code, which tests against the current window
    size.  The hardware overlay code did not set the current
    window when drawing the overlay.  I needed hardware overlay
    for DD's code, I'm not sure if these fixes are good enough to
    enable this in our general release.  Hardware overlay still
    only works on SGI Irix.
  - Some patches to turn off the MSVC++ -Oa (assume no aliasing)
    optimization flag.  Suprisingly this only broke a few parts
    of fltk, or at least these are the only ones I found.
  - Does not unmap child windows when the main window is
    iconized.  This reduces flashing when the window is deiconized.
  - Fl::key() is set to zero by all events except key down/up.
    This will allow you to reliably test if an event or callback
    was produced by a keystroke.  Fixes the bug posted about
    stopping Escape from closing the window.
  - User defined cursors on OpenGL windows slowed down NT a
    *LOT*.  Some attempts to fix this by turning off the cursor
    while drawing the window.
  - Filename completion in the file chooser works better on NT.
    Typing TAB fixes the case of everything you typed to match
    the shortest name that can be completed.


CHANGES IN FLTK 1.0.8

  - Many documentation changes/fixes/improvements.
  - FLUID didn't save Fl_Double_Window's as
    double-buffered windows.
  - Fl_Menu_ text color is used if Fl_Menu_Item text color
    is not set.
  - Added Fl::first_window(window) method to change the
    "top" window that is used when showing modal windows.
    By default it is the window the user last
    clicked/typed in.
  - The Fl_Menu::global() handler now uses the current top
    window instead of the menu bar for modal stuff.
  - Added fl_line_style() function to set the line style.
    Note that user-defined line styles ONLY WORK UNDER X11
    and Windows NT/2000. Windows 95/98 do, however,
    support the "standard" line styles.
  - Fl::wait() does not return immediately when no windows
  - XForms keyboard shortcuts using hex keycode constants
    now work.
  - Updated the configure script for *BSD and to turn off
    exceptions and RTTI in the FLTK library itself (does
    not affect applications which use these things)
  - FLUID now supports I18N using the POSIX or GNU mechanisms.
  - Fixed definition of glutBitmapWidth to match header file.
  - Does not turn visible() on when a window is iconized()
    or if a modal window is shown and it's parent is
    iconized.  This allows the code "while (w->visible()
    && w->damage()) Fl::check();" to reliably wait for the
    window to be mapped and drawn the first time.
  - Setting box(FL_NO_BOX) on a button makes it an
    invisible overlay
  - FL_NORMAL_SIZE is now a global variable so you can
    change the default text size prior to creating your
    widgets.
  - Menus now draw properly with a box type of
    FL_FLAT_BOX.
  - Cygwin fixes to compile in POSIX mode.
  - Fl_Value_Input callback can call value() or destructor.
  - OpenGL overlays now work under Windows NT!
  - Fl_Slider and Fl_Scrollbar could cause a divide by zero.
  - Clicking in an Fl_Input field no longer selects the
    whole field, it just moves the text cursor.
  - Tru64 UNIX fixes for filename_list()
  - Fl_Browser now draws itself properly when deactivated.
  - FLUID GUIs now use Courier font for all code input.
  - The FLUID OK and Cancel buttons are now all shown in
    the same order in all windows.
  - Fixes to compile under GCC 2.95.2
  - Fixed the BC5 project files.
  - FL_LEFT_MOUSE and friends are now in
    <FL/Enumerations.H>
  - Fixes for fake OpenGL overlay code under WIN32.
  - Message windows are now resizeable.
  - On WIN32 non_modal (but not modal) windows have the
    close and size boxes.
  - Fl_Button and friends didn't honor the
    FL_WHEN_NOT_CHANGED condition.
  - Disabled XDBE on all platforms.
  - XGetDefault patch from James Roth
  - New fl_open_display(Display *) function to allow FLTK
    to share a display connection with another toolkit
    (like Xt, GTK, etc.)
  - Shortcut labels for special keys should now display
    properly under WIN32.
  - fl_set_fonts() did not reuse fonts.
  - Fixed shortcut problem under WIN32 when the focus
    window changes.
  - "dead" keys should now work under X11.
  - Fixes to make FLTK compile with GCC 2.95.2
  - FL_SHORTCUT fix for I18N.
  - Fixed cut/paste problems under WIN32
  - FLUID now produces correct code for nested class
    destructors.
  - Nested windows should now redraw properly under WIN32.
  - "table" is now static in fl_cursor.cxx
  - Fl_Chart used the textcolor() and not the color() for
    horizontal bar charts.
  - Now set the input hint for TWM and TWM-derived window
    managers.
  - Now look for TrueColor visual if FLTK is compiled with
    USE_COLORMAP == 0.
  - Fl_Scrollbar could generate a divide-by-0 error if the
    min and max values were the same.
  - Fl_Menu_::remove() now removes whole submenus if
    needed.
  - Scrollbar buttons now draw themselves pushed in as
    needed.
  - Fixed the gl_overlay demo (and gl overlays in general)
    when they are faked with no hardware and the window is
    resized.
  - Selections weren't shown in Fl_Browser widgets when an
    item used the @B (background) format.
  - Windows can now be resized by the program under X11
    for more window managers.
  - OS/2 makeinclude updates.
  - Added Fl.H required by an inline function in
    Fl_Repeat_Button.H
  - Fl_add_idle adds new functions to the end of the queue
    ring, rather than the start, so they are executed in
    the order added, and a callback that adds itself does
    not prevent others from being called.
  - FLUID lets you type in code that starts with '#' for
    cpp directives.
  - XBell() could be called before the X11 display was
    opened, causing a segfault.
  - Fixed Fl_Gl_Window::ortho() - Borland C++ doesn't
    define GLint to "int", but instead to "long"...
  - Fixed Fl_Browser scrollbars within an Fl_Scroll
    widget.
  - Fl_Output (and non-focused Fl_Input) now scroll in
    response to position()
  - Fl_Input now does not scroll horizontally if the
    entire string will fit in the widget.
  - Fl_Scrollbar didn't push the right arrow buttons when
    you clicked outside the scroller.
  - Now use WSAAsyncSelect() for better socket performance
    with Fl::add_fd()


CHANGES IN FLTK 1.0.7

  - Fixed Fl_Input_ bug under WIN32 - no longer stop accepting input
    when one of the "Windows" keys is pressed.
  - Now call TranslateEvent for all events under WIN32.
  - Fixes for OpenBSD and NetBSD
  - The FL_CURSOR_HAND cursor now uses the IDC_HAND cursor instead of
    IDC_UPARROW under Windows 98 and 2000.
  - Fl_Scrollbar now does a page-up/down when you click outside the
    scroller.
  - Fl_Window::show(0, NULL) causes core dump
  - Fixed a compile-time error in fl_call_main.c for Borland C++.
  - "fluid -c filename.fl" would try to open an X display if the
    FLUID file contained an Fl_Browser widget.
  - Fl_Browser now correctly measures items with @C or @B color
    formatting commands.
  - Fixed a bitmap drawing bug for WIN32 (bit reversal table was wrong)
  - fl_xyz() dialogs now set a title in the title bar.
  - fl_alert() sounds the bell under X11.
  - fl_xyz() dialogs now call MessageBeep() under WIN32.
  - Fl_Browser_ didn't draw the selection box with the inactive color
    when the browser wasn't activated.
  - Fl_Browser now responds to FL_KEYBOARD as well as FL_SHORTCUT.  If
    you subclass it to accept focus then keyboard navigation will work.
  - Fl_Tile and Fl_Tabs do their callback when the user changes their
    display.
  - Made some of the private methods of Fl_Browser protected.
  - Now set win_gravity correctly, this helps some X
    window managers that use it position the window where
    FLTK wants it to be.
  - 0-width browsers crashed.
  - Minor change: if the X window manager does not do
    anything else with windows that don't have their
    position specified, the windows appear centered in the
    screen, rather than in the top-left corner.  This
    happened with modal windows under Irix 4Dwm.  This
    also causes windows to be centered when no window
    manager is running, which might be useful for
    installation gui programs?
  - Clicking in an Fl_Input field the first time selects the entire
    field.
  - Clicking the middle mouse button in an Fl_Input field now inserts
    the text at the indicated position instead of the cursor position.
  - Drag-selecting text in an Fl_Input field now copies the text
    automatically.
  - Fl::flush() no longer calls the draw() method for invisible windows.
  - Calling deactivate() on an invisible widget could cause an
    infinite loop in some obscure cases.
  - Added #pragma's for SGI C++ compilers - the 6.{23} X headers had
    errors in them.
  - Fl_Gl_Window::ortho() changed so that text and images
    are not erased if the origin is off the left/bottom of the
    window.
  - Small change to Fl_Input so that a click that gives it
    the focus also selects all the text.
  - Fixed a slider drawing problem.
  - You can now add/delete children of Fl_Tabs widgets whether or
    not they are visible.
  - Now embed woff options for SGI C++ compilers (gets rid of X11
    header warnings)
  - draw_pixmap used a cast that the Digital UNIX C++ compiler didn't
    like.
  - The GLUT function key constants were off by one.
  - The XPM reading code didn't handle RGB colors other than #rrggbb.


CHANGES IN FLTK 1.0.6

  - Fl_win32.cxx defined WM_MOUSE_LEAVE instead of WM_MOUSELEAVE.
  - Fl_get_key_win32.cxx needed to include <ctype.h>
  - gl_draw_pixmap.cxx needed a pointer cast for ANSI C++.
  - Fl_Repeat_Button didn't always delete its timeout.
  - Now keep track of the current OpenGL context; this provides
    significant performance improvements for OpenGL applications
    with a single context.


CHANGES IN FLTK 1.0.5

  - Fl_Roller didn't handle a width and height of 0.
  - filename_list() fix for FreeBSD.
  - Fixed RPM install docos - needed "--install" option...
  - Fl_Browser_ wouldn't draw the vertical scrollbar right away if it
    added a horizontal one which covered the last line.
  - Fl_Tabs problems - single-character labels don't show up (problem in
    measure_tabs() or measure_label() methods?), and doesn't clear top
    tab area before drawing tabs.
  - Fl_Browser needs a destructor.
  - fl_draw_label() quoted characters between 0x80 and 0xa0, which
    caused problems for some programs using the WinANSI character set.
  - FLUID didn't handle declared class destructors.
  - Fixed another WIN32 cut/paste bug.
  - Fl_Tabs didn't work properly when there was only 1 tab.
  - Fl_Menu::add() didn't delete the old array.
  - Fl_Repeat_Button didn't delete its timeout when disabled.
  - fl_draw() would crash if no font was set (now defaults to
    a 14-pixel Helvetica font)
  - Can't forward declare classes; need to check for "class ", "struct ",
    "union ", etc.  See Bill's message
  - Added #pragma around xlib.h for IRIX
  - FL_KEYBOARD events have the correct x/y when sent to child X
    windows. Note that if you worked around this bug by adjusting the
    x/y yourself you will have to change your code. In addition all
    events have the correct x/y when sent to the grab() widget.  And
    the code to do all this was simplified a lot.
  - The XPM code didn't handle named colors with spaces in the names.
  - Pressing ESCape closed the window with pointer focus, even if there
    was a modal window open (now closes the modal window).
  - FLUID no longer produces trigraphs accidentally in the image data.
  - FLUID uses string constant concatenation to produce shorter image
    data.
  - The Fl_Group deletion code crashed if there was exactly one child
    widget.
  - Simulated overlays in single-buffered Fl_Gl_Windows now draw
    correctly (though very slowly as it requires the entire window to
    be redrawn to erase the overlay).  This fix ported our Digital
    Domain programs better to systems with no overlay hardware.
  - Added support for extern "C" declarations in FLUID.
  - Added Fl_Pack support to FLUID.
  - Fixed the order of #include's in FLUID generated header files.
  - Fixed detection of vsnprintf and snprintf under HP-UX 10.20 once
    and for all.
  - The checkers demo did not compile with GCC 2.95
  - FLUID didn't output virtual destructors properly.
  - Added inline "make_visible()" method to Fl_Browser.
  - Fl::wait() now returns immediately if any timeouts are
    called.
  - 16-bit XPM files are now properly handled.
  - Fl_Window::resize() was missing FL_EXPORT (caused problems
    with Windows DLLs)
  - FLUID was writing extern declarations twice.
  - New FLUID arrow key functionality: arrows move by one pixel, shift+arrow
    resizes, ctrl+arrow steps by grid


CHANGES IN FLTK 1.0.4

  - Documentation updates
  - Fl_Browser::bottomline(size) didn't scroll to the bottom
    if the second-to-last line was visible.
  - fl_wait() didn't poll FDs properly for WIN32.
  - Fixed DLL definitions for BC++.
  - FLUID now handles nested classes properly.
  - The "connect" demo now does a wait() for the PPP process
    so that you aren't left with a lot of zombie processes.
  - Fixed the FLTK colormap to use FF instead of F4 for full
    intensity values.
  - Minor change to scrollbar drawing code to match other
    toolkits.
  - New selections would cancel themselves out in WIN32.
  - The header file links were broken in the IRIX
    distributions.
  - fl_elapsed() now always uses GetClockTick() for WIN32.
  - fl_display is now initialized to GetModuleHandle(NULL) -
    this fixes problems people had with Cygwin and MingW32.
  - WinMain() is no longer compiled in with Cygwin and
    MingW32; it wasn't being used for those compilers anyways.
  - Added Solaris compiler options to configure script.
  - Fl_Value_Input wouldn't update properly if you set the
    value from a callback.
  - Fl_Tile wouldn't resize if the resizeable widget was the
    last child.
  - Was missing #include <ctype.h> and #include <stdlib.h> in
    several files, which caused problems on some platforms.
  - Fixed another case where Fl_Browser_ could get in an
    infinite resizing loop.
  - Fl_win32.cxx now includes <FL/filename.H> to export missing
    DLL symbols.
  - FLUID didn't handle member functions that include the
    scope operator.
  - Fl_Chart was dividing by 0 if there were no data samples
    or if they were all the same (min == max).


CHANGES IN FLTK 1.0.3

  - XDBE is now enabled for IRIX 6.[234] as well as 6.5.
  - FLUID didn't write the when() condition properly.
  - Tab/space/backtab/backspace can be used to navigate
    through menus.
  - Changed $(DSONAME) in the src/Makefile to "libfltk.so.1
    libfltk.sl.1".
  - Fl_Browser could read past the end of the string when
    computing the item height.
  - Fl_Browser could get in an infinite loop when checking to
    see if scrollbars needed to be displayed.
  - FLUID now honors the return type of the outermost widget.
    This was a problem when substituting Fl_Group in an
    Fl_Window widget.
  - Fl_Menu_::copy() wasn't allocating a power of 2 for the
    array size.
  - FLWM would crash if fl_xmousewin was deleted.
  - The fast_slow demo now uses output widgets.
  - Timers under WIN32 were unreliable.


CHANGES IN FLTK 1.0.2

  - Documentation updates
  - The Visual C++ project files didn't include fl_add_idle.cxx.
  - LIBRARY/DSO name inconsistencies in src/Makefile.
  - src/Makefile didn't clean the DSO.
  - The valuator demo now has only a single callback.
  - The code looked for HAVE_SYS_SELECT_H, but the
    config file uses HAVE_SYS_SELECT.
  - Fl_Image redraw not quite right under X11 or WIN32
  - Problems with timeouts & cube demo under WIN32
  - FLUID problems with inline functions.
  - Documentation fixes...
  - Fl_Browser::item_height() didn't handle blank lines or
    non-default fonts properly.
  - FL/math.h didn't have #ifndef...#define...#endif guards
    against multiple inclusion...
  - Fl_Menu_::copy() fix - didn't allocate power of 2...
  - Fl::damage() now remains true until all windows are actually
    redrawn.
  - Fl_Widget destructor, hide(), and deactivate() methods no longer
    send FL_LEAVE, FL_RELEASE, or FL_UNFOCUS events to the widget
    (which could cause applications to crash).
  - FLUID now outputs symbolic names for align() and when().
  - Fixed select() to use maxfd + 1 instead of maxfd.
  - Added "Fl::remove_fd(fd, when)" function so you can remove the
    read and write callbacks separately.
  - The Fl::add_fd() and Fl::add_timeout() arrays are now dynamically
    allocated.
  - FLUID didn't always turn the FL_SUBMENU flag on for submenu titles.
  - The "extra code" in FLUID now is placed before the "o->end()" call
    for Fl_Group and its derived classes.
  - You can now set a FL_Window widget's class in FLUID to Fl_Group to
    generate a function or class that builds part of a GUI (i.e. no window).
  - FLUID now displays "Save file before exiting?" with the standard yes,
    no, and cancel buttons rather than "Discard changes?".
  - Fl_Menu_::add() now works with any type of menu, even one set with
    the menu() method.
  - The keypad keys were not always decoded properly under X11.
  - Some pointers were not being turned off when widgets were deleted,
    which caused some applications (like FLWM) to crash.


CHANGES IN FLTK 1.0.1

  - Documentation fixes.
  - Fl::check() didn't return the correct value, breaking a number
    of applications.
  - Fixed FLUID bug that caused styles patch to crash when you delete
    a menu item.
  - Updated valuators demo to put the values in the gui box.
  - Fl_Browser_::item_height() didn't always compute the correct
    value.
  - Fixed the alignment of Fl_Choice text.
  - Fixes for OS/2.
  - Fl_Menu_Item::clear() didn't clear value.
  - Added some changes to make FLTK work with Borland C++.
  - ANSI C++ fixes.
  - Plugged a memory leak in the fractal demo.
  - Fl::add_timeout() didn't work under WIN32 with small values.
  - The configure script and makefiles now define DSONAME and
    use the static library for all example programs.
