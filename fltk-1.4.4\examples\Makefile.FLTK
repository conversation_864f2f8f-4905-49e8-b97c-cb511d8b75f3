#
# Included Makefile used to build example apps by 'make'
#
# Copyright 2020-2022 by <PERSON> and others.
#
# This library is free software. Distribution and use rights are outlined in
# the file "COPYING" which should have been included with this file.  If this
# file is missing or damaged, see the license at:
#
#     https://www.fltk.org/COPYING.php
#
# Please see the following page on how to report bugs and issues:
#
#     https://www.fltk.org/bugs.php
#
################################################################################

#
# Stuff every FLTK application might need
#
#    If you take this for use in your own project, be sure to change
#    the 'FLTKCONFIG' setting to point to where it's installed
#    on your system. Common examples:
#
#	FLTKCONFIG = /usr/local/bin/fltk-config
#       FLTKCONFIG = /usr/local/src/fltk-1.4.x/fltk-config
#       FLTKCONFIG = ../build/fltk-config # if ../build == CMake build directory
#
#    Set .SILENT in your Makefile if you want 'quieter' builds.
#

ifeq '$(OS)' "Windows_NT"
EXEEXT = .exe
endif

FLTKCONFIG     = ../fltk-config
CXX            = $(shell $(FLTKCONFIG) --cxx)
CXXFLAGS       = $(shell $(FLTKCONFIG) --cxxflags) -Wall -I.
LINKFLTK       = $(shell $(FLTKCONFIG) --ldstaticflags)
LINKFLTK_GL    = $(shell $(FLTKCONFIG) --use-gl --ldstaticflags)
LINKFLTK_IMG   = $(shell $(FLTKCONFIG) --use-images --ldstaticflags)
LINKFLTK_ALL   = $(shell $(FLTKCONFIG) --use-images --use-gl --ldstaticflags)
CXXFLAGS_CAIRO = $(shell $(FLTKCONFIG) --use-cairo --cxxflags)
LINKFLTK_CAIRO = $(shell $(FLTKCONFIG) --use-cairo --ldstaticflags)
.SUFFIXES: .cxx .h .fl .o $(EXEEXT)

# HOW TO COMPILE
.cxx.o:
	@echo "*** Compile $<..."
	$(CXX) -I.. $(CXXFLAGS) -c $< -o $@

# HOW TO LINK
.o$(EXEEXT):
	@echo "*** Linking $@..."
	$(CXX) $< $(LINKFLTK) $(LINKFLTK_IMG) -o $@
