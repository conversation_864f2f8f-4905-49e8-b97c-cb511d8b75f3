/**

 \page basics FLTK Basics

This chapter teaches you the basics of writing and compiling programs
that use FLTK.

\section basics_writing Writing Your First FLTK Program

Up to FLTK 1.3.x all FLTK programs were required to include the file
<tt><FL/Fl.H></tt> as the first FLTK header file.

Since FLTK 1.4.0 this requirement was relaxed and <tt><FL/Fl.H></tt> needs
only be included if the class \c Fl is used or if some other stuff like
enumerations is used in the source code. Example code in this documentation
may still include it "everywhere" even if it is no longer strictly required.

In addition the program must include a header file for each FLTK class it uses.
Listing 1 shows a simple "Hello, World!" program that uses FLTK to display the window.

\par Listing 1 - "hello.cxx"
\code
#include <FL/Fl.H>
#include <FL/Fl_Window.H>
#include <FL/Fl_Box.H>

int main(int argc, char **argv) {
  Fl_Window *window = new Fl_Window(340, 180);
  Fl_Box *box = new Fl_Box(20, 40, 300, 100, "Hello, World!");
  box->box(FL_UP_BOX);
  box->labelfont(FL_BOLD + FL_ITALIC);
  box->labelsize(36);
  box->labeltype(FL_SHADOW_LABEL);
  window->end();
  window->show(argc, argv);
  return Fl::run();
}
\endcode

<!-- NEED 2in -->

After including the required header files, the program then creates a
window. All following widgets will automatically be children of this window.

\code
Fl_Window *window = new Fl_Window(340, 180);
\endcode

Then we create a box with the "Hello, World!" string in it. FLTK automatically
adds the new box to \p window, the current grouping widget.

\code
Fl_Box *box = new Fl_Box(20, 40, 300, 100, "Hello, World!");
\endcode

Next, we set the type of box and the font, size, and style of the label:

\code
box->box(FL_UP_BOX);
box->labelfont(FL_BOLD + FL_ITALIC);
box->labelsize(36);
box->labeltype(FL_SHADOW_LABEL);
\endcode

We tell FLTK that we will not add any more widgets to \p window.

\code
window->end();
\endcode

Finally, we show the window and enter the FLTK event loop:

\code
window->show(argc, argv);
return Fl::run();
\endcode

The resulting program will display the "Hello, World!" window:

\image html  hello_cxx.png "The Hello, World! Window"
\image latex hello_cxx.png "The Hello, World! Window" width=8cm

You can quit the program by closing the window or pressing the
<tt>ESC</tt>ape key.

\subsection basics_creating Creating the Widgets

The widgets are created using the C++ \p new operator.  For
most widgets the arguments to the constructor are:

\code
Fl_Widget(x, y, width, height, label)
\endcode

The \p x and \p y parameters determine where the
widget or window is placed on the screen. In FLTK the top left
corner of the window or screen is the origin
(i.e. <tt>x = 0, y = 0</tt>).

The \p width and \p height parameters determine
the size of the widget or window. The maximum widget
size is typically governed by the underlying window system or
hardware.

\ref drawing_DrawingUnit describes the unit FLTK employs for
<tt>x, y, width</tt>, and \c height, and more generally, for
all graphical quantities.

\p label is a pointer to a character string to label
the widget with or \p NULL. If not specified the label
defaults to \p NULL. The label string must be in static
storage such as a string constant because FLTK does not make a
copy of it - it just uses the pointer.

\subsection basics_hierarchies Creating Widget Hierarchies

Widgets are commonly ordered into functional groups, which
in turn may be grouped again, creating a hierarchy of widgets.
FLTK makes it easy to fill groups by automatically adding all widgets
that are created between a
<tt>myGroup->begin()</tt>
and
<tt>myGroup->end()</tt>.
In this example, \p myGroup would be the \e current group.

Newly created groups and their derived widgets implicitly call
\p begin() in the constructor, effectively adding all subsequently
created widgets to itself until \p end() is called.

Calling end() on one group widget transfers the "current group"
property to the \b parent of that widget. Calling end() on a top
level window (which has no parent) sets the current group to \p NULL.

Setting the current group to \p NULL will stop automatic
hierarchies. New widgets can now be added manually using
<tt>Fl_Group::add(...)</tt>
and
<tt>Fl_Group::insert(...)</tt>.

\subsection basics_getset Get/Set Methods

<tt>box->box(FL_UP_BOX)</tt>
sets the type of box the Fl_Box draws, changing it from the default
of \p FL_NO_BOX, which means that no box is drawn. In our
"Hello, World!" example we use \p FL_UP_BOX, which means that a
raised button border will be drawn around the widget.
More details are available in the \ref common_boxtypes section.

You could examine the boxtype by doing
<tt>box->box()</tt>. FLTK uses method name overloading to make
short names for get/set methods. A "set" method is always of
the form "void name(type)", and a "get" method is always
of the form "type name() const".

\subsection basics_redrawing Redrawing After Changing Attributes

Almost all of the get/set pairs are very fast, short inline
functions and thus very efficient. However, <i>the "set" methods
do not call \p redraw()</i> - you have to call it
yourself. This greatly reduces code size and execution time. The
only common exceptions are \p value() which calls
\p redraw() and \p label() which calls
\p redraw_label() if necessary.

\subsection basics_labels Labels

All widgets support labels. In the case of window widgets,
the label is used for the label in the title bar. Our example
program calls the \p labelfont(), \p labelsize(),
and \p labeltype() methods.

The \p labelfont() method sets the typeface and style
that is used for the label, which for this example we are using
\p FL_BOLD and \p FL_ITALIC.

The \p labelsize() method sets the height of the font in FLTK units.

The \p labeltype()
method sets the type of label. FLTK supports normal, embossed,
and shadowed labels internally, and more types can be added as
desired.

A complete list of all label options can be found in the section on
\ref common_labels.

\subsection basics_showing Showing the Window

The \p show() method shows the widget or window. For windows
you can also provide the command-line arguments to allow users to
customize the appearance, size, and position of your windows.

\subsection basics_eventloop The Main Event Loop

All FLTK applications (and most GUI applications in general)
are based on a simple event processing model. User actions such
as mouse movement, button clicks, and keyboard activity generate
events that are sent to an application. The application may then
ignore the events or respond to the user, typically by redrawing
a button in the "down" position, adding the text to an input
field, and so forth.

FLTK also supports idle, timer, and file pseudo-events that
cause a function to be called when they occur. Idle functions
are called when no user input is present and no timers or files
need to be handled - in short, when the application is not doing
anything. Idle callbacks are often used to update a 3D display
or do other background processing.

Timer functions are called after a specific amount of time
has expired. They can be used to pop up a progress dialog after
a certain amount of time or do other things that need to happen
at more-or-less regular intervals. FLTK timers are not 100%
accurate, so they should not be used to measure time intervals,
for example.

File functions are called when data is ready to read or
write, or when an error condition occurs on a file. They are
most often used to monitor network connections (sockets) for
data-driven displays.

FLTK applications must periodically check (Fl::check())
or wait (Fl::wait()) for events or use the Fl::run()
method to enter a standard event processing loop. Calling
Fl::run() is equivalent to the following code:

\code
while (Fl::wait());
\endcode

Fl::run() does not return until all of the windows
under FLTK control are closed by the user or your program.

\section basics_naming Naming Conventions

All public symbols in FLTK start with the characters 'F' and 'L':

\li Functions are either \p Fl::foo() or \p fl_foo().

\li Class and type names are capitalized: \p Fl_Foo.

\li \ref enumerations "Constants and Enumerations"
    are uppercase: \p FL_FOO.

\li All header files start with <tt><FL/...></tt>.

<!-- NEED 5in -->

\section basics_headerfiles Header Files

The proper way to include FLTK header files is:

\code
#include <FL/Fl_xyz.H>
\endcode

\note
Case \e is \e significant on many operating systems,
and the C standard uses the forward slash (/) to
separate directories. <i>Do not use any of the following
include lines:</i>

\code
#include <FL\Fl_xyz.H>
#include <fl/fl_xyz.h>
#include <Fl/fl_xyz.h>
\endcode


\section basics_compiling Compiling Programs that Use FLTK

Since FLTK 1.4 CMake is the recommended build system. The details below show
the "old" methods and reference information in case you like to write your
build configuration manually (e.g. Makefiles, Visual Studio, other IDE's ...).

CMake can simplify this task substantially. For now, refer to README.CMake.txt
for further information.

\todo This section needs a major rework. Add a chapter "Building FLTK with CMake".


\subsection basics_standard_compiler Compiling Programs with Standard Compilers

Under UNIX (and under Microsoft Windows when using the GNU development
tools) you will probably need to tell the compiler where to find the
header files. This is usually done using the \p -I option:

\code
c++ -I/usr/local/include ...
\endcode

\note You need a C++ compiler to build FLTK. The commands given in this
  chapter are \b examples using \p 'c++'. Please replace this command with
  the C++ compiler suitable for your system or use the `fltk-config` script
  as described below (this is recommended).

\subsection basics_fltk_config Compiling Programs with the 'fltk-config' Script

The \p fltk-config script included with FLTK can be used on systems with
a Posix compliant shell, for instance Unix/Linux, macOS, Windows with MinGW,
MSYS2, or Cygwin.

\note `fltk-config` is not designed to work on Windows with Visual Studio
compilers. If it works, then only by accident and this is undefined behavior.

\code
fltk-config --help
\endcode

displays all available options.

`fltk-config` can be used to get the compiler and the options that are
required by your compiler to build a program using the FLTK library:

\code
  fltk-config --cc
  fltk-config --cxx
\endcode

return the C and C++ compiler commands used to build FLTK.

\code
c++ `fltk-config --cxxflags` ...
\endcode

can be used to include the required compiler flags in the command line.

Similarly, when linking your application you will need to tell the
compiler to use the FLTK library:

\code
c++ ... -L/usr/local/lib -lfltk -lXext -lX11 ... -lm -ldl
\endcode

Aside from the "fltk" library, there are also the following libraries
  - "fltk_forms" for the XForms compatibility classes (deprecated)
  - "fltk_gl" for the OpenGL and GLUT classes
  - "fltk_images" for the image file classes, Fl_Help_Dialog widget, and system icon support.

The libraries are named `fltk.lib`, `fltk_forms.lib`, `fltk_gl.lib`, and
`fltk_images.lib` under Windows.

\note
  The separate \p fltk_cairo library is no longer necessary since FLTK 1.4.0.
  However, this release of FLTK builds a dummy `fltk_cairo` library for
  backwards compatibility. You are advised to remove the usage of
  the `fltk_cairo` library from your build systems and tools.
  <b>The `fltk_cairo` library will be removed in a future release.</b>


As before, the \p fltk-config script can be used to get the options that are
required by your linker:

\code
c++ ... `fltk-config --ldflags`
\endcode

<!-- NEED 2in -->

The forms, GL, and images libraries are included with the "--use-foo"
options, as follows:

\code
c++ ... `fltk-config --use-forms  --ldflags`
c++ ... `fltk-config --use-gl     --ldflags`
c++ ... `fltk-config --use-images --ldflags`
c++ ... `fltk-config --use-cairo  --ldflags`
c++ ... `fltk-config --use-forms --use-gl --use-images --ldflags`
\endcode

The option `--use-cairo` may be used to build your program with Cairo libs if
you use Cairo in your code. It does no longer include the `fltk_cairo` lib but
all necessary Cairo compiler flags and Cairo libs, if and only if FLTK has been
built with the optional Cairo support by configure or CMake.

Finally, you can use the \p fltk-config script to compile one or more source
files as a FLTK program.

The following examples will create an executable named \p filename (or
\p filename.exe under Windows) from a single source file:

\code
fltk-config --compile filename.cxx
fltk-config --use-forms --compile filename.cpp
fltk-config --use-gl --compile filename.C
fltk-config --use-images --compile filename.cc
fltk-config --use-cairo --compile filename.cpp
fltk-config --use-forms --use-gl --use-images --compile filename.cpp
\endcode

\note <kbd>'fltk-config \-\-compile'</kbd> accepts only a limited set of file
  extensions for C++ source files: \p '.cpp', \p '.cxx',  \p '.cc',  and \p '.C'
  (capital 'C').

\subsection basics_fltk_config2 Compiling Multiple Source Files with 'fltk-config'

Before version 1.4.0 \p fltk-config accepted only a single source file
and no additional compiler options or libraries.
Since FLTK 1.4.0 it is possible to use additional compiler flags, more than
one source file, and additional link libraries.

This is intended to be used for quick prototyping and not for production code
development. It can be used to test compiler command options (like `-Wall` or
`-Wextra`) or additional link libraries if these are required.

Building from more than one source file with flags and libraries can be
achieved as follows:

\code
fltk-config [USE-FLAGS] --compile MAIN [FLAGS] [SOURCES] [--link LFLAGS LIBS]
\endcode

where
 - arguments in `[...]` are optional
 - `USE-FLAGS` are as described above, e.g. `--use-images`
 - `MAIN` is the main C++ source file as documented above
 - `FLAGS` are additional compiler flags
 - `SOURCES` are additional source files or libraries
 - `--link` is used to separate source files and flags from linker flags and libs
 - `LFLAGS` are optional linker flags
 - `LIBS` are additional libraries to link against

The final commandline is composed like this example:
\code
$ fltk-config --compile main.cxx button.o -Wextra x1.a --link -L/usr/include/cairo/ -lcairo

g++ {fltk-flags} -o main -Wextra main.cxx button.o x1.a {fltk-libs} -L/usr/include/cairo/ -lcairo
\endcode

where `{fltk-flags}` are the compiler flags generated by `fltk-config` as
before and `{fltk-libs}` are the usual linker flags and libraries.
All optional parameters are used as-is, i.e. there is no syntax checking or
special parsing except: the order of flags and source files is preserved
(from the commandline) but all flags (`-something`) are positioned before
all sources, i.e. arguments w/o leading dash ('-').
All compiler flags and libraries generated from the library build follow
all options and source files given on the commandline, and finally
everything after `--link` is appended.


\subsection basics_makefile Compiling Programs with Makefiles

The previous sections described how to use \p fltk-config to build a program
from the command line, and this is very convenient for small test programs.
But \p fltk-config can also be used to set the compiler and linker options
as variables within a \p Makefile that can be used to build larger programs.

\code
CXX      = $(shell fltk-config --cxx)
DEBUG    = -g
CXXFLAGS = $(shell fltk-config --use-gl --use-images --cxxflags ) -I.
LDFLAGS  = $(shell fltk-config --use-gl --use-images --ldflags )
LDSTATIC = $(shell fltk-config --use-gl --use-images --ldstaticflags )
LINK     = $(CXX)

TARGET = cube
OBJS   = CubeMain.o CubeView.o CubeViewUI.o
SRCS   = CubeMain.cxx CubeView.cxx CubeViewUI.cxx

.SUFFIXES: .o .cxx

%.o: %.cxx
	$(CXX) $(CXXFLAGS) $(DEBUG) -c $<

all: $(TARGET)

$(TARGET): $(OBJS)
	$(LINK) -o $(TARGET) $(OBJS) $(LDSTATIC)

CubeMain.o:   CubeMain.cxx CubeViewUI.h
CubeView.o:   CubeView.cxx CubeView.h CubeViewUI.h
CubeViewUI.o: CubeViewUI.cxx CubeView.h

clean:
	rm -f *.o 2> /dev/null
	rm -f $(TARGET) 2> /dev/null

\endcode

\subsection basics_visual_cpp Compiling Programs with Microsoft Visual C++

In Visual C++ you will need to tell the compiler where to find the FLTK
header files. This can be done by selecting "Settings" from the "Project"
menu and then changing the "Preprocessor" settings under the "C/C++" tab.

You will also need to add the following libraries to the \p Linker settings:
 - \p fltk.lib or \p fltkd.lib, the main FLTK library (postfix 'd' = Debug)
 - all FLTK libraries your program requires (fltk_gl, fltk_images, …)
 - additional libraries like \p libpng.lib, \p libjpeg.lib, etc.
 - the Windows Common Controls (\p comctl32.lib) and
 - the GDIplus library if used to build FLTK (\p gdiplus.lib) and
 - the Windows Socket (\p ws2_32.lib) libraries.

\note There's a \p Linker setting "Additional Library Directories" or similar;
  the exact name depends on the Visual Studio version you're using. You can
  and \b should use this to simplify adding the libraries above. If you set
  this to the FLTK library path you can just use the library \b names
  and don't need to use the full paths to all libraries.

You must also define <tt>_WIN32</tt> if the compiler doesn't do this.
Currently all known Windows compilers define _WIN32 - unless you use Cygwin
(that's correct, you must not define _WIN32 if you use Cygwin).

More information can be found in <tt>README.Windows.txt</tt>.

You can build your Microsoft Windows applications as Console or
Desktop applications.  If you want to use the standard C \p main()
function as the entry point, FLTK includes a \p WinMain()
function that will call your \p main() function for you.

\htmlonly
<hr>
<table summary="navigation bar" width="100%" border="0">
<tr>
  <td width="45%" align="LEFT">
    <a class="el" href="intro.html">
    [Prev]
    Introduction to FLTK
    </a>
  </td>
  <td width="10%" align="CENTER">
    <a class="el" href="index.html">[Index]</a>
  </td>
  <td width="45%" align="RIGHT">
    <a class="el" href="common.html">
    Common Widgets and Attributes
    [Next]
    </a>
  </td>
</tr>
</table>
\endhtmlonly

*/
