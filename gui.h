// generated by Fast Light User Interface Designer (fluid) version 1.0109

#ifndef gui_h
#define gui_h
#include <FL/Fl.H>
#include <FL/Fl_Double_Window.H>
#include "Application.h"
#include <stdlib.h>
#include <FL/Fl_Menu_Bar.H>
#include "EditorWindow.h"
#include "DisplayWindow.h"

class Gui {
public:
  Gui();
  Fl_Double_Window *MainWindow;
  Fl_Menu_Bar *menuBar;
  static Fl_Menu_Item menu_menuBar[];
  static Fl_Menu_Item *fileMenu;
private:
  inline void cb_readFile_i(Fl_Menu_*, void*);
  static void cb_readFile(Fl_Menu_*, void*);
public:
  static Fl_Menu_Item *writeFile;
private:
  inline void cb_writeFile_i(Fl_Menu_*, void*);
  static void cb_writeFile(Fl_Menu_*, void*);
  inline void cb_Update_i(Fl_Menu_*, void*);
  static void cb_Update(Fl_Menu_*, void*);
  inline void cb_Average_i(Fl_Menu_*, void*);
  static void cb_Average(Fl_Menu_*, void*);
  inline void cb_Median_i(Fl_Menu_*, void*);
  static void cb_Median(Fl_Menu_*, void*);
  inline void cb_Gaussian_i(Fl_Menu_*, void*);
  static void cb_Gaussian(Fl_Menu_*, void*);
  inline void cb_Edge_i(Fl_Menu_*, void*);
  static void cb_Edge(Fl_Menu_*, void*);
  inline void cb_Undo_i(Fl_Menu_*, void*);
  static void cb_Undo(Fl_Menu_*, void*);
public:
  static Fl_Menu_Item *exitButton;
private:
  inline void cb_exitButton_i(Fl_Menu_*, void*);
  static void cb_exitButton(Fl_Menu_*, void*);
public:
  CEditorWindow *EditorWindow;
  CDisplayWindow *DisplayWindow;
  void show();
  Application *app;
};
#endif