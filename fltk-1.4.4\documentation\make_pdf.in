#! /bin/sh
#
# Makefile helper script for the Fast Light Tool Kit (FLTK) documentation.
#
# Copyright 1998-2020 by <PERSON> and others.
#
# This library is free software. Distribution and use rights are outlined in
# the file "COPYING" which should have been included with this file.  If this
# file is missing or damaged, see the license at:
#
#     https://www.fltk.org/COPYING.php
#
# Please see the following page on how to report bugs and issues:
#
#      https://www.fltk.org/bugs.php
#

# This script generates latex/refman.pdf after doxygen has been executed.
#
# Input:  run `doxygen Doxybook' (creates files in subdirectory latex)
# Output: latex/refman.pdf (if successful)
#
# Next step: cp -f latex/refman.pdf fltk.pdf (why is this extra step needed ?)
#
# Working directory: fltk/documentation
#
# Used in: Makefile and CMakeLists.txt

run_pdflatex() {
  pdflatex --interaction=nonstopmode \
    "\pdfinfo{/CreationDate(@PDF_DATE@)/ModDate(@PDF_DATE@)}\input{refman.tex}"
}

( cd latex
  run_pdflatex
  makeindex refman.idx
  run_pdflatex
  latex_count=5
  while egrep -s 'Rerun (LaTeX|to get cross-references right)' refman.log \
	  && [ $latex_count -gt 0 ]
    do
      echo "Rerunning pdflatex ..."
      run_pdflatex
      latex_count=`expr $latex_count - 1`
    done
  cd ..) > pdfall.log 2>&1
