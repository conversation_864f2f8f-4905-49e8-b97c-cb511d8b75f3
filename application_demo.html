<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FLTK Image Processing Application Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
        }
        
        .application-window {
            width: 985px;
            height: 555px;
            background-color: #e0e0e0;
            border: 2px solid #808080;
            margin: 0 auto;
            position: relative;
        }
        
        .menu-bar {
            width: 100%;
            height: 25px;
            background-color: #d0d0d0;
            border-bottom: 1px solid #808080;
            display: flex;
            align-items: center;
            padding-left: 5px;
            font-size: 12px;
        }
        
        .menu-item {
            padding: 5px 10px;
            cursor: pointer;
            border-right: 1px solid #a0a0a0;
        }
        
        .menu-item:hover {
            background-color: #b0b0b0;
        }
        
        .editor-window {
            position: absolute;
            left: 15px;
            top: 25px;
            width: 385px;
            height: 350px;
            background-color: #000;
            border: 2px inset #c0c0c0;
        }
        
        .display-window {
            position: absolute;
            left: 415px;
            top: 25px;
            width: 570px;
            height: 530px;
            background-color: #000;
            border: 2px inset #c0c0c0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .histogram {
            position: absolute;
            bottom: 10px;
            left: 10px;
            width: 256px;
            height: 100px;
            background-color: #000;
        }
        
        .histogram-bar {
            position: absolute;
            bottom: 0;
            width: 1px;
            background-color: white;
        }
        
        .transfer-function {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 256px;
            height: 200px;
            background-color: #000;
        }
        
        .sample-image {
            width: 300px;
            height: 300px;
            background: linear-gradient(45deg, #666, #999, #ccc);
            border: 1px solid #555;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .controls {
            margin-top: 20px;
            text-align: center;
        }
        
        .control-button {
            margin: 5px;
            padding: 8px 16px;
            background-color: #d0d0d0;
            border: 2px outset #c0c0c0;
            cursor: pointer;
            font-size: 12px;
        }
        
        .control-button:hover {
            background-color: #e0e0e0;
        }
        
        .control-button:active {
            border: 2px inset #c0c0c0;
        }
        
        .info-panel {
            margin-top: 20px;
            padding: 15px;
            background-color: white;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }
        
        .feature-item {
            padding: 8px;
            background-color: #f8f8f8;
            border-left: 3px solid #007acc;
        }
        
        canvas {
            border: 1px solid #555;
        }
    </style>
</head>
<body>
    <h1>FLTK Image Processing Application - Demo Interface</h1>
    
    <div class="application-window">
        <div class="menu-bar">
            <div class="menu-item">File</div>
            <div class="menu-item">Image Processing</div>
            <div class="menu-item">Exit</div>
        </div>
        
        <div class="editor-window">
            <div class="transfer-function">
                <canvas id="transferCanvas" width="256" height="200"></canvas>
            </div>
            <div class="histogram">
                <canvas id="histogramCanvas" width="256" height="100"></canvas>
            </div>
        </div>
        
        <div class="display-window">
            <div class="sample-image">
                Sample Image<br>
                (Load .ppm/.pgm file)
            </div>
        </div>
    </div>
    
    <div class="controls">
        <div class="control-button" onclick="simulateOperation('load')">Load Image</div>
        <div class="control-button" onclick="simulateOperation('update')">Update</div>
        <div class="control-button" onclick="simulateOperation('average')">Average Smooth</div>
        <div class="control-button" onclick="simulateOperation('median')">Median Smooth</div>
        <div class="control-button" onclick="simulateOperation('gaussian')">Gaussian Smooth</div>
        <div class="control-button" onclick="simulateOperation('edge')">Edge Detect</div>
        <div class="control-button" onclick="simulateOperation('undo')">Undo</div>
    </div>
    
    <div class="info-panel">
        <h3>Application Features Implemented:</h3>
        <div class="feature-list">
            <div class="feature-item">✅ FLTK GUI with menu system</div>
            <div class="feature-item">✅ Image loading (.ppm/.pgm)</div>
            <div class="feature-item">✅ Histogram computation</div>
            <div class="feature-item">✅ Transfer function editor</div>
            <div class="feature-item">✅ 3×3 Average smoothing</div>
            <div class="feature-item">✅ 3×3 Median filtering</div>
            <div class="feature-item">✅ 5×5 Gaussian smoothing</div>
            <div class="feature-item">✅ Sobel edge detection</div>
            <div class="feature-item">✅ Undo functionality</div>
            <div class="feature-item">✅ Real-time preview</div>
            <div class="feature-item">✅ Interactive editing</div>
            <div class="feature-item">✅ Memory management</div>
        </div>
        
        <h4>To run the actual application:</h4>
        <ol>
            <li>Install FLTK libraries (FL, GL, Lib folders)</li>
            <li>Set up Visual Studio project with proper include/library paths</li>
            <li>Build and run the application</li>
            <li>Use File → Read to load test images</li>
            <li>Try the Image Processing menu operations</li>
        </ol>
    </div>

    <script>
        // Draw sample histogram
        const histCanvas = document.getElementById('histogramCanvas');
        const histCtx = histCanvas.getContext('2d');
        histCtx.fillStyle = 'white';
        
        // Generate sample histogram data
        for (let i = 0; i < 256; i++) {
            const height = Math.random() * 80 + 10;
            histCtx.fillRect(i, 100 - height, 1, height);
        }
        
        // Draw sample transfer function
        const transCanvas = document.getElementById('transferCanvas');
        const transCtx = transCanvas.getContext('2d');
        transCtx.strokeStyle = 'red';
        transCtx.lineWidth = 2;
        transCtx.beginPath();
        transCtx.moveTo(0, 200);
        
        // Draw a curved transfer function
        for (let i = 0; i <= 256; i++) {
            const y = 200 - (i * 0.8 + Math.sin(i * 0.02) * 20);
            transCtx.lineTo(i, y);
        }
        transCtx.stroke();
        
        function simulateOperation(op) {
            const operations = {
                'load': 'Loading image file...',
                'update': 'Applying transfer function...',
                'average': 'Applying 3×3 average filter...',
                'median': 'Applying 3×3 median filter...',
                'gaussian': 'Applying 5×5 Gaussian filter...',
                'edge': 'Applying Sobel edge detection...',
                'undo': 'Reverting to previous image...'
            };
            
            alert(operations[op] || 'Operation completed!');
        }
    </script>
</body>
</html>
