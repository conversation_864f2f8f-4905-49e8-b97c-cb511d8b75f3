//
// Filled dial header file for the Fast Light Tool Kit (FLTK).
//
// Copyright 1998-2010 by <PERSON> and others.
//
// This library is free software. Distribution and use rights are outlined in
// the file "COPYING" which should have been included with this file.  If this
// file is missing or damaged, see the license at:
//
//     https://www.fltk.org/COPYING.php
//
// Please see the following page on how to report bugs and issues:
//
//     https://www.fltk.org/bugs.php
//

/* \file
   Fl_Fill_Dial widget . */

#ifndef Fl_Fill_Dial_H
#define Fl_Fill_Dial_H

#include "Fl_Dial.H"

/** Draws a dial with a filled arc */
class FL_EXPORT Fl_Fill_Dial : public Fl_Dial {
public:
  /** Creates a filled dial, also setting its type to FL_FILL_DIAL. */
  Fl_Fill_Dial(int X,int Y,int W,int H, const char *L = NULL);
};

#endif
