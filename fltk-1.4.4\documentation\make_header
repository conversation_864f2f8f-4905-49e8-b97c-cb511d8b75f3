#!/bin/sh
#
# Create a new LaTeX header file for doxygen PDF docs
#
# Note: this LaTeX file depends on Doxygen and LaTeX versions, resp.
# and needs therefore to be created with current Doxygen and LaTeX
# versions on the build system.
#
# Usage:
#
#   $ sh make_header doxygen_path input-file output-file
#
# where
# - 'doxygen_path' is the full path to the doxygen executable
#   or just 'doxygen'. If the full path is used an arbitrary
#   doxygen executable and thus doxygen version can be used.
# - 'input-file' is the pure (LaTeX) title page (template)
# - 'output-file' is the generated (LaTeX) title page (template)
#   that is used by `make' or `cmake` to generate the final LaTeX
#   page header (combined doxygen template + FLTK title page).
#
#=======================================================================
# This script requires a posix shell and uses the following commands:
# cat, rm and sed and (obviously) doxygen
#=======================================================================

# input and output file names
DOXY_CMD="$1"
FLTK_HEAD="$2"
DOXY_HEAD="$3"
# temp file
DOXY_TEMP="doxy-header.tex.$$"

if test x$FLTK_HEAD = x; then
  echo "usage: $0 fltk-header-file output-file"
  exit 1
fi

if test x$DOXY_HEAD = x; then
  echo "usage: $0 fltk-header-file output-file"
  exit 1
fi

# Create the doxygen LaTeX header template and replace the LaTeX
# code between (and including) the lines containing
# - 'begin{titlepage}' and
# - 'end{titlepage}'
# with our PDF document title page (LaTeX code) and write the
# result to $DOXY_HEAD.

"$DOXY_CMD" -w latex $DOXY_TEMP /dev/null /dev/null

# combine three parts of these files to the output file
# using '( ... ) > $DOXY_HEAD' to write (concatenate)
# all three parts to one file

( sed -e'/begin{titlepage}/,$d' < $DOXY_TEMP
  cat $FLTK_HEAD
  sed -e'1,/end{titlepage}/d' < $DOXY_TEMP
) > $DOXY_HEAD

# cleanup
rm -f $DOXY_TEMP
