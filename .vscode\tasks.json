{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "msvc build",
      "type": "shell",
      "command": "cl.exe",
      "args": [
        "/EHsc",
        "/Zi",
        "/Fe:",
        "scivis.exe", // Output executable name
        "*.cpp", // Compile all .cpp files
        "/I${workspaceFolder}", // Additional Include Directories [cite: 46]
        "/link",
        "/LIBPATH:${workspaceFolder}/Lib", // Additional Library Directories [cite: 48]
        // Additional Dependencies [cite: 50]
        "fltkd.lib",
        "wsock32.lib",
        "comctl32.lib",
        "fltkjpegd.lib",
        "fltkimagesd.lib",
        "odbc32.lib",
        "odbccp32.lib",
        "opengl32.lib",
        "glu32.lib",
        "fltk.lib",
        "fltkgl.lib"
      ],
      "group": {
        "kind": "build",
        "isDefault": true
      },
      "presentation": {
        "reveal": "always"
      },
      "problemMatcher": "$msCompile"
    }
  ]
}
