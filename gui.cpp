// generated by Fast Light User Interface Designer (fluid) version 1.0109

#include "gui.h"

void Gui::cb_readFile_i(Fl_Menu_*, void*) {
  app->ReadFile();
}
void Gui::cb_readFile(Fl_Menu_* o, void* v) {
  ((Gui*)(o->parent()->user_data()))->cb_readFile_i(o,v);
}

void Gui::cb_writeFile_i(Fl_Menu_*, void*) {
  app->WriteFile();
}
void Gui::cb_writeFile(Fl_Menu_* o, void* v) {
  ((Gui*)(o->parent()->user_data()))->cb_writeFile_i(o,v);
}

void Gui::cb_Update_i(Fl_Menu_*, void*) {
  app->Update();
}
void Gui::cb_Update(Fl_Menu_* o, void* v) {
  ((Gui*)(o->parent()->user_data()))->cb_Update_i(o,v);
}

void Gui::cb_Average_i(Fl_Menu_*, void*) {
  app->AverageSmooth();
}
void Gui::cb_Average(Fl_Menu_* o, void* v) {
  ((Gui*)(o->parent()->user_data()))->cb_Average_i(o,v);
}

void Gui::cb_Median_i(Fl_Menu_*, void*) {
  app->MedianSmooth();
}
void Gui::cb_Median(Fl_Menu_* o, void* v) {
  ((Gui*)(o->parent()->user_data()))->cb_Median_i(o,v);
}

void Gui::cb_Gaussian_i(Fl_Menu_*, void*) {
  app->GaussianSmooth();
}
void Gui::cb_Gaussian(Fl_Menu_* o, void* v) {
  ((Gui*)(o->parent()->user_data()))->cb_Gaussian_i(o,v);
}

void Gui::cb_Edge_i(Fl_Menu_*, void*) {
  app->EdgeDetect();
}
void Gui::cb_Edge(Fl_Menu_* o, void* v) {
  ((Gui*)(o->parent()->user_data()))->cb_Edge_i(o,v);
}

void Gui::cb_Undo_i(Fl_Menu_*, void*) {
  app->Undo();
}
void Gui::cb_Undo(Fl_Menu_* o, void* v) {
  ((Gui*)(o->parent()->user_data()))->cb_Undo_i(o,v);
}

void Gui::cb_exitButton_i(Fl_Menu_*, void*) {
  exit(1);
}
void Gui::cb_exitButton(Fl_Menu_* o, void* v) {
  ((Gui*)(o->parent()->user_data()))->cb_exitButton_i(o,v);
}

Fl_Menu_Item Gui::menu_menuBar[] = {
 {"File", 0,  0, 0, 64, FL_NORMAL_LABEL, 0, 14, 0},
 {"Read", 0,  (Fl_Callback*)Gui::cb_readFile, 0, 0, FL_NORMAL_LABEL, 0, 14, 0},
 {"Write", 0,  (Fl_Callback*)Gui::cb_writeFile, 0, 0, FL_NORMAL_LABEL, 0, 14, 0},
 {0,0,0,0,0,0,0,0,0},
 {"Image Processing", 0,  0, 0, 64, FL_NORMAL_LABEL, 0, 14, 0},
 {"Update", 0,  (Fl_Callback*)Gui::cb_Update, 0, 0, FL_NORMAL_LABEL, 0, 14, 0},
 {"Average Smooth", 0,  (Fl_Callback*)Gui::cb_Average, 0, 0, FL_NORMAL_LABEL, 0, 14, 0},
 {"Median Smooth", 0,  (Fl_Callback*)Gui::cb_Median, 0, 0, FL_NORMAL_LABEL, 0, 14, 0},
 {"Gaussian Smooth", 0,  (Fl_Callback*)Gui::cb_Gaussian, 0, 0, FL_NORMAL_LABEL, 0, 14, 0},
 {"Edge Detect", 0,  (Fl_Callback*)Gui::cb_Edge, 0, 0, FL_NORMAL_LABEL, 0, 14, 0},
 {"Undo", 0,  (Fl_Callback*)Gui::cb_Undo, 0, 0, FL_NORMAL_LABEL, 0, 14, 0},
 {0,0,0,0,0,0,0,0,0},
 {"Exit", 0,  (Fl_Callback*)Gui::cb_exitButton, 0, 0, FL_NORMAL_LABEL, 0, 14, 1},
 {0,0,0,0,0,0,0,0,0}
};
Fl_Menu_Item* Gui::fileMenu = Gui::menu_menuBar + 0;
Fl_Menu_Item* Gui::readFile = Gui::menu_menuBar + 1;
Fl_Menu_Item* Gui::writeFile = Gui::menu_menuBar + 2;
Fl_Menu_Item* Gui::exitButton = Gui::menu_menuBar + 12;

Gui::Gui() {
  { MainWindow = new Fl_Double_Window(985, 555, "Scientific Visualization Projects");
    MainWindow->user_data((void*)(this));
    { menuBar = new Fl_Menu_Bar(0, 0, 985, 25, "menuBar");
      menuBar->menu(menu_menuBar);
    } // Fl_Menu_Bar* menuBar
    { EditorWindow = new CEditorWindow(15, 25, 385, 350, "EditorWindow");
      EditorWindow->box(FL_NO_BOX);
      EditorWindow->color(FL_BACKGROUND_COLOR);
      EditorWindow->selection_color(FL_BACKGROUND_COLOR);
      EditorWindow->labeltype(FL_NORMAL_LABEL);
      EditorWindow->labelfont(0);
      EditorWindow->labelsize(14);
      EditorWindow->labelcolor(FL_FOREGROUND_COLOR);
      EditorWindow->align(FL_ALIGN_CENTER);
      EditorWindow->when(FL_WHEN_RELEASE);
    } // CEditorWindow* EditorWindow
    { DisplayWindow = new CDisplayWindow(415, 25, 570, 530, "DisplayWindow");
      DisplayWindow->box(FL_NO_BOX);
      DisplayWindow->color(FL_BACKGROUND_COLOR);
      DisplayWindow->selection_color(FL_BACKGROUND_COLOR);
      DisplayWindow->labeltype(FL_NORMAL_LABEL);
      DisplayWindow->labelfont(0);
      DisplayWindow->labelsize(14);
      DisplayWindow->labelcolor(FL_FOREGROUND_COLOR);
      DisplayWindow->align(FL_ALIGN_CENTER);
      DisplayWindow->when(FL_WHEN_RELEASE);
    } // CDisplayWindow* DisplayWindow
    MainWindow->end();
  } // Fl_Double_Window* MainWindow
  app=new Application();
}

void Gui::show() {
  MainWindow->show();
EditorWindow->show();
DisplayWindow->show();
}