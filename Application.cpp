#define _CRT_SECURE_NO_DEPRECATE
#include "Application.h"
#include <FL/fl_file_chooser.H>
#include "Gui.h"
#include <algorithm>

extern Gui *gui;
Image curImage; // make inImage a global, need it in other classes
Volume vol;

// these are declared in transFunc.cpp
extern TransferFunction transFunc[4];
extern int maxTransFunc;
extern int activeTransFunc;
extern float transFuncColor[4][3];

// Helper function to copy image data
void Application::copyImage(Image* from, Image* to)
{
    if (to->data) {
        delete[] to->data;
    }
    to->nx = from->nx;
    to->ny = from->ny;
    to->n = from->n;
    to->ncolorChannels = from->ncolorChannels;
    to->data = new unsigned char[to->n * to->ncolorChannels];
    memcpy(to->data, from->data, to->n * to->ncolorChannels);
    memcpy(to->histogram, from->histogram, sizeof(int) * 256);
}

// Helper function to compute histogram
void Application::computeHistogram(Image* img)
{
    for (int i = 0; i < 256; i++) {
        img->histogram[i] = 0;
    }

    if (img->ncolorChannels == 1) { // Grayscale
        for (int i = 0; i < img->n; i++) {
            img->histogram[img->data[i]]++;
        }
    } else if (img->ncolorChannels == 3) { // Color
        for (int i = 0; i < img->n * 3; i += 3) {
            // Convert to grayscale for histogram
            int gray = (int)(0.299 * img->data[i] + 0.587 * img->data[i+1] + 0.114 * img->data[i+2]);
            img->histogram[gray]++;
        }
    }
}

// the constructor method for the Application class
Application::Application()
{
  // initialize the image data structure
  curImage.nx=curImage.ny=curImage.n=curImage.ncolorChannels=0;
  curImage.data = NULL;
  oldImage.nx=oldImage.ny=oldImage.n=oldImage.ncolorChannels=0;
  oldImage.data = NULL;


  // add more initialization here:

}

Application::~Application()
{
    if (curImage.data) delete[] curImage.data;
    if (oldImage.data) delete[] oldImage.data;
}

// the method that gets executed when the readFile callback is called
void Application::ReadFile()
{
   FILE *fp;
   char imageType[3],str[100];
   int dummy;
   int i,j;

   char *file = fl_file_chooser("Pick a file to READ from", "*.{pgm,ppm,vol}", "");
   if(file == NULL)
		return;

    fp=fopen(file,"rb");

    // free memory from old image, if any
    if(curImage.n>0)
    {
	  delete[] curImage.data;
	}

    fgets(str,100,fp);
	sscanf(str,"%s",imageType);

    if(!strncmp(imageType,"P5",2)) // greylevel image
    {
       curImage.ncolorChannels=1;
       maxTransFunc=1;
       activeTransFunc=0;
       transFuncColor[0][0]=1.0; transFuncColor[0][1]=0.0; transFuncColor[0][2]=0.0;
	}
	else if(!strncmp(imageType,"P6",2)) // color image
    {
	   curImage.ncolorChannels=3;
       maxTransFunc=1;
       activeTransFunc=0;
       transFuncColor[0][0]=1.0; transFuncColor[0][1]=0.0; transFuncColor[0][2]=0.0;
       transFuncColor[1][0]=0.0; transFuncColor[1][1]=1.0; transFuncColor[1][2]=0.0;
       transFuncColor[2][0]=0.0; transFuncColor[2][1]=0.0; transFuncColor[2][2]=1.0;
	}

    fgets(str,100,fp);
	while(str[0]=='#')
		fgets(str,100,fp);

    sscanf(str,"%d %d",&curImage.nx,&curImage.ny);

    fgets(str,100,fp);

	curImage.n=curImage.nx*curImage.ny;

	curImage.data = new unsigned char [curImage.n*curImage.ncolorChannels];
	fread(curImage.data,sizeof(unsigned char),curImage.n*curImage.ncolorChannels,fp);

    FlipImage(&curImage);

	fclose(fp);

    computeHistogram(&curImage);
    copyImage(&curImage, &oldImage);

	gui->DisplayWindow->redraw();
	gui->EditorWindow->redraw();
}


void Application::WriteFile()
{
   FILE *fp;
   char imageType[3],str[100];
   int dummy,i;
   char *file = fl_file_chooser("Specify a filename to WRITE to", "*.{vol,ppm,pgm}", "");
   if(file == NULL)
		return;

    fp=fopen(file,"wb");

	if(curImage.ncolorChannels==1)
   	  fprintf(fp,"P5\n"); //greylevel image
    else if(curImage.ncolorChannels==3)
      fprintf(fp,"P6\n");  // color image

    fprintf(fp,"%d %d\n",curImage.nx,curImage.ny);

    fprintf(fp,"255\n");

    for(i=curImage.ny-1;i>=0;i--)
  	  fwrite(&curImage.data[i*curImage.nx*curImage.ncolorChannels],sizeof(unsigned char),curImage.nx*curImage.ncolorChannels,fp);

	fclose(fp);
}

// flips an image upside down
void Application::FlipImage(Image *img)
{
    int i,j,k,rowOffsetSrc,rowOffsetDest,columnOffset;
    unsigned char ctmp;

	for(i=0;i<img->ny/2;i++)
	{
	   rowOffsetSrc=i*img->nx*img->ncolorChannels;
	   rowOffsetDest=(img->ny-1-i)*img->nx*img->ncolorChannels;
       for(j=0;j<img->nx;j++)
	   {
		   columnOffset=j*img->ncolorChannels;
		   for(k=0;k<img->ncolorChannels;k++)
		   {
			   ctmp=img->data[rowOffsetSrc+columnOffset+k];
               img->data[rowOffsetSrc+columnOffset+k]=img->data[rowOffsetDest+columnOffset+k];
               img->data[rowOffsetDest+columnOffset+k]=ctmp;
		   }
	   }
	}
}



// put your application routines here:

void Application::Update() {
    if (curImage.n == 0) return;

    copyImage(&curImage, &oldImage);

    for (int i = 0; i < curImage.n * curImage.ncolorChannels; i++) {
        curImage.data[i] = transFunc[activeTransFunc][curImage.data[i]];
    }

    computeHistogram(&curImage);
    gui->DisplayWindow->redraw();
    gui->EditorWindow->redraw();
}

void Application::AverageSmooth() {
    if (curImage.n == 0 || curImage.ncolorChannels != 1) return; // Only for grayscale

    copyImage(&curImage, &oldImage);

    unsigned char* tempData = new unsigned char[curImage.n];

    for (int y = 1; y < curImage.ny - 1; y++) {
        for (int x = 1; x < curImage.nx - 1; x++) {
            int sum = 0;
            for (int j = -1; j <= 1; j++) {
                for (int i = -1; i <= 1; i++) {
                    sum += oldImage.data[(y + j) * curImage.nx + (x + i)];
                }
            }
            tempData[y * curImage.nx + x] = sum / 9;
        }
    }
    memcpy(curImage.data, tempData, curImage.n);
    delete[] tempData;

    computeHistogram(&curImage);
    gui->DisplayWindow->redraw();
    gui->EditorWindow->redraw();
}


int Application::compare(const void* a, const void* b) {
    return (*(int*)a - *(int*)b);
}

void Application::MedianSmooth() {
    if (curImage.n == 0 || curImage.ncolorChannels != 1) return; // Only for grayscale

    copyImage(&curImage, &oldImage);

    unsigned char* tempData = new unsigned char[curImage.n];
    int neighborhood[9];

    for (int y = 1; y < curImage.ny - 1; y++) {
        for (int x = 1; x < curImage.nx - 1; x++) {
            int k = 0;
            for (int j = -1; j <= 1; j++) {
                for (int i = -1; i <= 1; i++) {
                    neighborhood[k++] = oldImage.data[(y + j) * curImage.nx + (x + i)];
                }
            }
            std::sort(neighborhood, neighborhood + 9);
            tempData[y * curImage.nx + x] = neighborhood[4];
        }
    }
    memcpy(curImage.data, tempData, curImage.n);
    delete[] tempData;


    computeHistogram(&curImage);
    gui->DisplayWindow->redraw();
    gui->EditorWindow->redraw();
}

void Application::GaussianSmooth() {
    if (curImage.n == 0 || curImage.ncolorChannels != 1) return; // Only for grayscale

    copyImage(&curImage, &oldImage);

    unsigned char* tempData = new unsigned char[curImage.n];
    int kernel[5][5] = {
        {1, 4, 7, 4, 1},
        {4, 16, 26, 16, 4},
        {7, 26, 41, 26, 7},
        {4, 16, 26, 16, 4},
        {1, 4, 7, 4, 1}
    };
    int kernelSum = 273;

    for (int y = 2; y < curImage.ny - 2; y++) {
        for (int x = 2; x < curImage.nx - 2; x++) {
            int sum = 0;
            for (int j = -2; j <= 2; j++) {
                for (int i = -2; i <= 2; i++) {
                    sum += oldImage.data[(y + j) * curImage.nx + (x + i)] * kernel[j + 2][i + 2];
                }
            }
            tempData[y * curImage.nx + x] = sum / kernelSum;
        }
    }
    memcpy(curImage.data, tempData, curImage.n);
    delete[] tempData;

    computeHistogram(&curImage);
    gui->DisplayWindow->redraw();
    gui->EditorWindow->redraw();
}

void Application::EdgeDetect() {
    if (curImage.n == 0 || curImage.ncolorChannels != 1) return; // Only for grayscale

    copyImage(&curImage, &oldImage);
    unsigned char* tempData = new unsigned char[curImage.n];

    int Gx[3][3] = {{-1, 0, 1}, {-2, 0, 2}, {-1, 0, 1}};
    int Gy[3][3] = {{1, 2, 1}, {0, 0, 0}, {-1, -2, -1}};

    for (int y = 1; y < curImage.ny - 1; y++) {
        for (int x = 1; x < curImage.nx - 1; x++) {
            int sumX = 0;
            int sumY = 0;
            for (int j = -1; j <= 1; j++) {
                for (int i = -1; i <= 1; i++) {
                    sumX += oldImage.data[(y + j) * curImage.nx + (x + i)] * Gx[j + 1][i + 1];
                    sumY += oldImage.data[(y + j) * curImage.nx + (x + i)] * Gy[j + 1][i + 1];
                }
            }
            int magnitude = sqrt(sumX * sumX + sumY * sumY);
            if (magnitude > 255) magnitude = 255;
            tempData[y * curImage.nx + x] = magnitude;
        }
    }

    memcpy(curImage.data, tempData, curImage.n);
    delete[] tempData;

    computeHistogram(&curImage);
    gui->DisplayWindow->redraw();
    gui->EditorWindow->redraw();
}

void Application::Undo() {
    if (oldImage.n == 0) return;

    Image temp;
    copyImage(&curImage, &temp);
    copyImage(&oldImage, &curImage);
    copyImage(&temp, &oldImage);
    if(temp.data) delete [] temp.data;


    gui->DisplayWindow->redraw();
    gui->EditorWindow->redraw();
}