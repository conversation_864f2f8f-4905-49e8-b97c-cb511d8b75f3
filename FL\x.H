//
// *Deprecated* platform header file for the Fast Light Tool Kit (FLTK).
//
// Copyright 1998-2018 by <PERSON> and others.
//
// This library is free software. Distribution and use rights are outlined in
// the file "COPYING" which should have been included with this file.  If this
// file is missing or damaged, see the license at:
//
//     https://www.fltk.org/COPYING.php
//
// Please see the following page on how to report bugs and issues:
//
//     https://www.fltk.org/bugs.php
//

// IMPORTANT: This file is deprecated since FLTK 1.4.0. DO NOT include it.
// FL/x.H will be removed in a future FLTK release.

// Please #include <FL/platform.H> instead if you really need it. See
// documentation in FL/platform.H to decide whether you need that file.

#if !defined(Fl_X_H) && !defined(FL_DOXYGEN)
#  define Fl_X_H
#  include <FL/platform.H>
#endif // !Fl_X_H
