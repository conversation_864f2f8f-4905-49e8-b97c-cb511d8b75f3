# Makefile for FLTK Image Processing Application
# This assumes you have MinGW-w64 or similar compiler installed

CXX = g++
CXXFLAGS = -std=c++11 -Wall -I. -IFL -IGL
LDFLAGS = -L./lib
LIBS = -lfltk -lfltkgl -lopengl32 -lglu32 -lgdi32 -lcomctl32 -lwsock32

# Source files
SOURCES = Main.cpp Application.cpp DisplayWindow.cpp EditorWindow.cpp gui.cpp
OBJECTS = $(SOURCES:.cpp=.o)
TARGET = ImageProcessor.exe

# Default target
all: $(TARGET)

# Link the executable
$(TARGET): $(OBJECTS)
	$(CXX) $(OBJECTS) -o $(TARGET) $(LDFLAGS) $(LIBS)

# Compile source files
%.o: %.cpp
	$(CXX) $(CXXFLAGS) -c $< -o $@

# Clean build files
clean:
	del /Q *.o $(TARGET) 2>nul || true

# Install dependencies (if using MSYS2)
install-deps:
	@echo "To install dependencies with MSYS2:"
	@echo "pacman -S mingw-w64-x86_64-gcc"
	@echo "pacman -S mingw-w64-x86_64-fltk"
	@echo "pacman -S mingw-w64-x86_64-cmake"

# Help
help:
	@echo "Available targets:"
	@echo "  all        - Build the application"
	@echo "  clean      - Remove build files"
	@echo "  install-deps - Show how to install dependencies"
	@echo "  help       - Show this help"

.PHONY: all clean install-deps help
