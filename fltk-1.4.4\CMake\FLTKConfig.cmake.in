#
# FLTKConfig.cmake - FLTK CMake configuration file for external projects.
#
# This file is generated by CMake and used to load FLTK's settings for
# an external project, i.e. a project using FLTK.
#
# It defines the following variables:
#
#  FLTK_VERSION           - FLTK version string ("x.y.z")
#  FLTK_INCLUDE_DIRS      - FLTK include directories
#  FLTK_LIBRARIES         - list of built FLTK libraries
#  FLTK_FLUID_EXECUTABLE  - needed by the function FLTK_RUN_FLUID
#                           (or the deprecated fltk_wrap_ui() CMake command)
#
# It defines the following deprecated variables for backwards
# compatibility (do not use for new projects):
#
#  FLTK_INCLUDE_DIR       - FLTK include directories (same as FLTK_INCLUDE_DIRS)
#
# Important note: FLTK's CMake build files are not yet complete and may be
# changed in future versions. This includes the list of defined variables
# above that may be changed if necessary.
#
# Note: FLTK 1.4.0 introduced "Modern CMake", therefore usage of most if not
# all of the variables mentioned above is no longer needed in user projects.
# Please use the CMake target names fltk::fltk, fltk::images, etc. instead.
# Please see README.CMake.txt for mor info on how to do this.
#

# Optional: Create backwards compatible aliases for libraries and fluid.
# This is enabled in FLTK 1.4.0 to simplify migration of user projects
# from "classic" (1.3.x) to "modern" CMake and will likely be removed
# in a later FLTK version (maybe 1.4.x, x>2, or 1.5.0).

set(FLTK_CREATE_COMPATIBILITY_ALIASES TRUE)

set(FLTK_VERSION @FLTK_VERSION@)

include(${CMAKE_CURRENT_LIST_DIR}/FLTK-Targets.cmake)

set(FLTK_INCLUDE_DIRS "@INCLUDE_DIRS@")
set(FLTK_LIBRARIES "@FLTK_LIBRARIES@")

# For compatibility with CMake's FindFLTK.cmake:

set(FLTK_INCLUDE_DIR "${FLTK_INCLUDE_DIRS}")

if(CMAKE_CROSSCOMPILING)

  # Find a fluid executable on the build host to be able to build fluid programs

  find_program(FLTK_FLUID_HOST
    NAMES fluid fluid.exe
    PATHS ENV PATH
    NO_DEFAULT_PATH
    NO_CMAKE_FIND_ROOT_PATH
  )

  if(FLTK_FLUID_HOST)

    if(0) # Experimental: currently not used
      # Import a special 'fluid' target called 'fluid-host' (fltk::fluid-host)
      # Note: this is "the same as" the CMake variable FLTK_FLUID_EXECUTABLE
      add_executable(fluid-host IMPORTED)
      set_target_properties(fluid-host
        PROPERTIES IMPORTED_LOCATION ${FLTK_FLUID_HOST}
      )
      add_executable(fltk::fluid-host ALIAS fluid-host)
      set(FLTK_FLUID_EXECUTABLE fltk::fluid-host)

    else()

      set(FLTK_FLUID_EXECUTABLE "${FLTK_FLUID_HOST}")

    endif()

  else() # fluid not found on build host

    message(STATUS "FLTKConfig.cmake (cross-compiling): fluid not found on the build host")
    # note: this assigns "FLTK_FLUID_HOST-NOTFOUND" and running fluid will fail
    set(FLTK_FLUID_EXECUTABLE "${FLTK_FLUID_HOST}")

  endif()

else(CMAKE_CROSSCOMPILING)

  if(FLTK_CREATE_COMPATIBILITY_ALIASES)

    function(_fltk_make_alias target from)
      if(TARGET ${from} AND NOT TARGET ${target})
        # message(STATUS "FLTKConfig.cmake - create alias: ${target} from ${from}")

        # promote imported target to global visibility (CMake < 3.18 only)
        if(CMAKE_VERSION VERSION_LESS "3.18")
          set_target_properties(${from} PROPERTIES IMPORTED_GLOBAL TRUE)
        endif()

        get_target_property(ttype ${from} TYPE)
        if(ttype STREQUAL "EXECUTABLE")
          add_executable(${target} ALIAS ${from})
        else()
          add_library(${target} ALIAS ${from})
        endif()
      endif()
    endfunction(_fltk_make_alias target)

    if(NOT TARGET fltk::fltk)
      message(FATAL "FLTKConfig.cmake: target fltk::fltk does not exist!")
    endif()

    _fltk_make_alias(fltk fltk::fltk)
    _fltk_make_alias(fltk-shared fltk::fltk-shared)

    _fltk_make_alias(fluid fltk::fluid)
    _fltk_make_alias(fluid-cmd fltk::fluid-cmd)

    _fltk_make_alias(fltk-options fltk::options)
    _fltk_make_alias(fltk-options-cmd fltk::options-cmd)

    foreach(target cairo forms gl images jpeg png z)
      _fltk_make_alias(fltk_${target} fltk::${target})
      _fltk_make_alias(fltk_${target}-shared fltk::${target}-shared)
    endforeach()

  endif() # Create aliases ...

  # set FLTK_FLUID_EXECUTABLE: try to use the fltk::target name if it
  # exists and fall back to 'fluid-cmd' or 'fluid' if not. This will
  # eventually call the build host's fluid if found in the users's PATH

  if(TARGET fltk::fluid-cmd)  # Windows only
    set(FLTK_FLUID_EXECUTABLE fltk::fluid-cmd)
  elseif(TARGET fltk::fluid)
    set(FLTK_FLUID_EXECUTABLE fltk::fluid)
  elseif(WIN32)
    set(FLTK_FLUID_EXECUTABLE fluid-cmd)
  else()
    set(FLTK_FLUID_EXECUTABLE fluid)
  endif()

endif(CMAKE_CROSSCOMPILING)

# Debug: should be commented out
# message(STATUS "FLTKConfig.cmake: FLTK_FLUID_EXECUTABLE = '${FLTK_FLUID_EXECUTABLE}'")
