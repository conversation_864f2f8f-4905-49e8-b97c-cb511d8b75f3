/**

 \page intro Introduction to FLTK

The Fast Light Tool Kit ("FLTK") is a cross-platform C++ GUI toolkit for
UNIX&reg;/Linux&reg; (X11 and Wayland), Microsoft&reg; Windows&reg;, and
Apple&reg; macOS&reg;. FLTK provides modern GUI functionality without
bloat and supports 3D graphics via OpenGL&reg; and its built-in
GLUT emulation. It was originally developed by Mr. <PERSON>
and is currently maintained by a small group of developers
across the world with a central repository on GitHub.

\section intro_history History of FLTK

It has always been <PERSON>'s belief that the GUI API of all
modern  systems is much too high level. Toolkits (even FLTK) are
\e not what should be provided and documented as part of an
operating system. The  system only has to provide arbitrary
shaped but featureless windows, a  powerful set of graphics
drawing calls, and a simple \e unalterable method of
delivering events to the owners of the windows. NeXT (if you
ignored NextStep) provided this, but they chose to hide it and
tried to push their own baroque toolkit instead.

Many of the ideas in FLTK were developed on a NeXT (but
\e not using NextStep) in 1987 in a C toolkit Bill called
"views". Here he came up with passing events downward
in the tree and having the handle routine return a value
indicating whether it used the event, and the table-driven menus. In
general he was trying to prove that complex UI ideas could be
entirely implemented in a user space toolkit, with no knowledge
or support by the system.

After going to film school for a few years, Bill worked at
Sun Microsystems on the (doomed) NeWS project. Here he found an
even better and cleaner windowing system, and he reimplemented
"views" atop that. NeWS did have an unnecessarily
complex method of delivering events which hurt it. But the
designers did admit that perhaps the user could write just as
good of a button as they could, and officially exposed the lower
level interface.

With the death of NeWS Bill realized that he would have to
live with X. The biggest problem with X is the "window
manager", which means that the toolkit can no longer
control the window borders or drag the window around.

At Digital Domain Bill discovered another toolkit,
"Forms". Forms was similar to his work, but provided
many more widgets, since it was used in many real applications,
rather than as theoretical work. He decided to use Forms, except
he integrated his table-driven menus into it. Several very large
programs were created using this version of Forms.

The need to switch to OpenGL and GLX, portability, and a
desire to use C++ subclassing required a rewrite of Forms.
This produced the first version of FLTK. The conversion to C++
required so many changes it made it impossible to recompile any
Forms objects. Since it was incompatible anyway, Bill decided
to incorporate his older ideas as much as possible by
simplifying the lower level interface and the event passing
mechanism.

Bill received permission to release it for free on the
Internet, with the GNU general public license. Response from
Internet users indicated that the Linux market dwarfed the SGI
and high-speed GL market, so he rewrote it to use X for all
drawing, greatly speeding it up on these machines.

Digital Domain has since withdrawn support for FLTK. While
Bill is no longer able to actively develop it, he still
contributes to FLTK in his free time and is a part of the FLTK
development team.

FLTK was later ported to Windows and macOS. FLTK 1.4 added a
"driver based" system of virtual device drivers that enabled
porting to Wayland as well. Drawing features include Windows GDI+,
Cairo (Wayland and X11), and improved text layout with Pango.

There have been experiments using this driver system to build FLTK
based on SDL2, Android, and other graphics systems based solely on
simple pixel drawing, but this experimental code is not included
in FLTK 1.4. There are thoughts to enable more platforms in later
FLTK versions.


\section intro_features Features

FLTK was designed to be statically linked. This was done by
splitting it into many small objects and designing it so that
functions that are not used do not have pointers to them in the
parts that are used, and thus do not get linked in. This allows
you to make an easy-to-install program or to modify FLTK to
the exact requirements of your application without worrying
about bloat. FLTK works fine as a shared library, though, and
is now included with several Linux distributions.

Here are some of the core features unique to FLTK:

Note: sizes given below are mostly from 32-bit systems and FLTK 1.1
or earlier, this list needs updates for current FLTK (1.4).

\li sizeof(Fl_Widget) == 64 to 92 (120 in FLTK 1.4 on 64-bit Linux).

\li The "core" (the "hello" program compiled & linked with a static FLTK
    library using gcc on a 486 and then stripped) is 114K.
    (FLTK 1.4 on 64-bit Linux: 1.1 MB).

\li The FLUID program (which includes every widget) is 538k.
    (FLTK 1.4 with more widgets on 64-bit Linux: 2.3 MB and
    2.0 MB on 32-bit Windows).

\li Written directly atop core libraries (Xlib, Wayland, Windows or Cocoa) for
    maximum speed, and carefully optimized for code size and performance.

\li Precise low-level compatibility between the X11, Windows and MacOS
    versions - only about 10% of the code is different.

\li Interactive user interface builder program FLUID. Its output is
    human-readable and editable C++ source code.

\li Support for overlay hardware, with emulation if none is available.

\li Very small & fast portable 2-D drawing library to hide Xlib, Cairo,
    Windows, or macOS Quartz.

\li OpenGL/Mesa drawing area widget.

\li Support for OpenGL overlay hardware on both X11 and Windows, with
    emulation if none is available.

\li Text widgets with cut & paste, undo, and support
    for Unicode text and international input methods.

\li Compatibility header file for the GLUT library.

\li Compatibility header file for the XForms library.


\section intro_licensing Licensing

FLTK comes with complete free source code.
FLTK is available under the terms of the
\ref license "GNU Library General Public License"
with exceptions that allow for static linking.
Contrary to popular belief, it can be used in
commercial software - even Bill Gates could use it!

\section intro_what What Does "FLTK" Mean?

FLTK was originally designed to be compatible with the Forms
Library written for SGI machines. In that library all the
functions and structures started with "fl_". This
naming was extended to all new methods and widgets in the C++
library, and this prefix was taken as the name of the library.
It is almost impossible to search for "FL" on the
Internet, due to the fact that it is also the abbreviation for
Florida. After much debating and searching for a new name for
the toolkit, which was already in use by several people, Bill
came up with "FLTK", including a bogus excuse that it
stands for "The Fast Light Toolkit".


\section intro_fluid FLUID

FLTK comes bundled with FLUID. FLUID, short for Fast Light User Interface
Designer, is a graphical editor capable of generating C++ source code and
header files ready for compilation. These files ultimately create the graphical
user interface for an application.

The FLUID User Handbook is available at https://www.fltk.org/documentation.php .
It can also be compiled from the FLTK source repository using the `fluid_docs`
target in the CMake build environment.


\section intro_cmake Building and Installing FLTK with CMake

Starting with version 1.4, the recommended FLTK building system is CMake.
CMake is a "Build System Generator" that can generate build environments
for usage with Ninja, Make, and many more, for instance IDE's.
See file README.CMake.txt of the FLTK source tree for more information.

\note
In FLTK 1.4 you can also use \p configure and \p make as follows to build and
install FLTK. However, configure/make support will be dropped in FLTK 1.5.0.


\section intro_unix Building and Installing FLTK Under UNIX and macOS with make

In most cases you can just type "make". This will run configure with
the default of no options and then compile everything.

FLTK uses GNU autoconf to configure itself for your UNIX
platform. The main things that the configure script will look
for are the X11 and OpenGL (or Mesa) header and library files.
If these cannot be found in the standard include/library
locations you'll need to define the \p CFLAGS,
\p CXXFLAGS, and \p LDFLAGS environment variables.
For the Bourne and Korn shells you'd use:

\code
CFLAGS=-Iincludedir; export CFLAGS
CXXFLAGS=-Iincludedir; export CXXFLAGS
LDFLAGS=-Llibdir; export LDFLAGS
\endcode

For C shell and tcsh, use:

\code
setenv CFLAGS "-Iincludedir"
setenv CXXFLAGS "-Iincludedir"
setenv LDFLAGS "-Llibdir"
\endcode

By default configure will look for a C++ compiler named
\p CC, \p c++, \p g++, or \p gcc in that
order. To use another compiler you need to set the \p CXX
environment variable:

\code
CXX=xlC; export CXX
setenv CXX "xlC"
\endcode

The \p CC environment variable can also be used to
override the default C compiler (\p cc or \p gcc),
which is used for a few FLTK source files.

You can run configure yourself to get the exact setup you need.
Type  "./configure <options>", where some of the options are:

\par --enable-cygwin
Enable the Cygwin libraries under Windows

\par --enable-debug
Enable debugging code & symbols

\par --disable-gl
Disable OpenGL support

\par --disable-svg
Disable support of reading and writing of Scalable Vector Graphics (.svg) files.

\par --disable-print
Disable print support for an X11/Wayland platform

\par --enable-shared
Enable generation of shared libraries

\par --enable-threads
Enable multithreading support

\par --enable-wayland
This is the default for Linux and FreeBSD systems equipped with the Wayland software.
Enable the use of Wayland for all window operations, of Cairo for all graphics, and
of Pango for text drawing. Resulting FLTK apps run as Wayland clients if a Wayland
compositor is available at run-time, and as X11 clients otherwise but keep using
Cairo and Pango for all graphics.

\par --disable-xft
Disables the Xft library, resulting in non anti-aliased fonts (X11 platform).
This is not recommended.

\par --enable-usecairo
All drawing operations use the Cairo library (rather than Xlib) producing
antialiased graphics (X11 platform, implies --enable-pango).

\par --enable-pango
Enable the Pango library for drawing any text in any script with any font
under X11/Wayland.

\par --enable-x11
When targeting Cygwin, build with X11 GUI instead of windows GDI.
Also applicable to macOS platforms supplemented with XQuartz.

\par --enable-cairo
Enable support of class Fl_Cairo_Window (all platforms, requires Cairo as
an external library).

\par --enable-cairoext
Enable the FLTK instrumentation for cairo extended use (implies --enable-cairo).

\par --disable-gdiplus
Don't use GDI+ when drawing curves and oblique lines (Windows platform).

\par --enable-cp936
Under X11, enable use of the GB2312 locale.

\par --bindir=/path
Set the location for executables. [default = $prefix/bin]

\par --datadir=/path
Set the location for data files. [default = $prefix/share]

\par --libdir=/path
Set the location for libraries. [default = $prefix/lib]

\par --includedir=/path
Set the location for include files. [default = $prefix/include]

\par --mandir=/path
Set the location for man pages. [default = $prefix/man]

\par --prefix=/dir
Set the directory prefix for files. [default = /usr/local]

When the configure script is done you can just run the
"make" command. This will build the library, FLUID tool,
fltk-options (setup tool), and all of the test programs.

To install the library, become root and type "make install".
This will copy the "fluid" executable to "bindir", the header
files to "includedir", and the library files to "libdir".


\section intro_windows Building FLTK Under Microsoft Windows

NOTE: This documentation section is currently under review. More
up-to-date information for this release may be available in the files
"README.Windows.txt" and "README.CMake.txt" and you should read
these files to determine if there are changes that may be
applicable to your build environment.

FLTK 1.4 is officially supported on Windows (2000,) 2003,
XP, and later.  Older Windows versions prior to Windows 2000
are not officially supported but may still work.
The main reason is that the OS version needs to support UTF-8.
FLTK 1.4 is known to work on recent versions of Windows such as
Windows 7, Windows 8/8.1, Windows 10 and Windows 11, and has been
reported to work in both 32-bit and 64-bit Windows versions.

\note Libraries built by any one of the following build environments
can not be mixed with object files from any of the other environments
because they use incompatible C++ conventions internally.

FLTK currently supports the following development environments on the
Windows platform:


\subsection intro_msvc Free and Commercial Microsoft Visual Studio Versions

Visual Studio 2015 Community or later versions use workspace and project
files generated by CMake. Older versions and the commercial versions can
be used as well, if they can open the project files generated by CMake.
FLTK support of Visual C++ is limited to the support of CMake for these
Visual Studio versions.
Be sure to get your service packs!

Since FLTK 1.4 the project files MUST be generated with CMake.
Please read "README.CMake.txt" for more information about this.


\subsection intro_msvc_dll Using the Visual C++ DLL Library

The Visual Studio project files can be used to build a DLL version
of the FLTK library if CMake option 'FLTK_BUILD_SHARED_LIBS=ON' is
set. Because of name mangling differences between PC compilers (even
between different versions of Visual Studio) you can only use the DLL
that is generated with the same compiler version that you built it with.

When compiling an application or DLL that uses the FLTK DLL with Visual
Studio, you need to define the \p FL_DLL preprocessor symbol to get
the correct linkage commands embedded within the FLTK header files.

New since FLTK 1.4.0:
If you build your application project with CMake and use the CMake target
'fltk::fltk-shared' to link your application, then 'FL_DLL' is defined
automatically for you (by CMake Compile Definition). If you use your
own (hand-made) Visual Studio project you still need to define FL_DLL
to compile all source files that use FLTK headers.


\subsection intro_cygwin_mingw GNU toolsets (Cygwin or MinGW) hosted on Windows

If using Cygwin with the Cygwin shell, or MinGW with the Msys shell,
these build environments behave very much like a Unix or macOS build
and the notes above in the section on
<i>Building and Installing FLTK Under UNIX and Apple macOS</i>
apply, in particular the descriptions of using the
"configure" script and its related options.

In general for a build using these tools, e.g. for the Msys shell with
MinGW, it should suffice to "cd" into the directory where you have
extracted the FLTK tarball and type:

\code
./configure
make
\endcode

This will build the FLTK libraries and they can then be
utilised directly from the build location.
NOTE: this may be simpler than "installing" them in
many cases as different tool chains on Windows have
different ideas about where the files should be "installed" to.

For example, if you "install" the libraries using Msys/MinGW
with the following command

\code
make install
\endcode

then Msys will "install" the libraries to where it thinks
the path "/usr/local/" leads to. If you only ever build code
from within the Msys environment this works well, but the
actual "Windows path" these files are located in will be
something like "C:\msys\1.0\local\lib", depending
on where your Msys installation is rooted, which may
not be useful to other tools.

If you want to install your built FLTK libraries in a
non-standard location you may do:

\code
sh configure --prefix=C:/FLTK
make
\endcode

Where the value passed to "prefix" is the path at which
you would like FLTK to be installed.

A subsequent invocation of "make install" will then place
the FLTK libraries and header files into that path.

The other options to "configure" may also be used to
tailor the build to suit your environment.


\section intro_internet Internet Resources

FLTK is available on the 'net in a bunch of locations:

\par FLTK Source Repository on GitHub
https://github.com/fltk/fltk

\par WWW
https://www.fltk.org/ <br>
https://www.fltk.org/bugs.php [for reporting bugs] <br>
https://www.fltk.org/software.php [download source code]<br>
https://www.fltk.org/newsgroups.php [newsgroup/forums]

\par User Forums and NNTP Newsgroups
https://groups.google.com/forum/#!forum/fltkgeneral [Google Groups interface] <br>
news://fltk.org:1024/ [NNTP interface]<br>
https://www.fltk.org/newsgroups.php [web interface]


\section intro_reporting Reporting Bugs

To report a bug in FLTK, or for feature requests, please use
<A href="https://www.fltk.org/bugs.php">https://www.fltk.org/bugs.php</A>
for information about where and how to post bugs, feature requests,
or ask for help on using FLTK.

For general support and questions, please use the fltk.general newsgroup
(see above, "NNTP Newsgroups") or the web interface to the newsgroups at
<A href="https://www.fltk.org/newsgroups.php">https://www.fltk.org/newsgroups.php</A>.

\htmlonly
<hr>
<table summary="navigation bar" width="100%" border="0">
<tr>
  <td width="45%" align="LEFT">
    <a class="el" href="preface.html">
    [Prev]
    Preface
    </a>
  </td>
  <td width="10%" align="CENTER">
    <a class="el" href="index.html">[Index]</a>
  </td>
  <td width="45%" align="RIGHT">
    <a class="el" href="basics.html">
    FLTK Basics
    [Next]
    </a>
  </td>
</tr>
</table>
\endhtmlonly

*/
