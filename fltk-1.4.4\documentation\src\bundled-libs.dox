/**

\page bundled-libs Developer info for bundled libs

This chapter details the procedure to update the libraries which are bundled inside FLTK.


\section bundled-intro Introduction

This file is mainly intended for FLTK developers and contains information
about the current versions of all bundled libraries and about how to
upgrade these bundled libraries.

Starting with FLTK 1.4.0 the bundled libraries jpeg, png, and zlib use
"symbol prefixing" with the prefix 'fltk_' for all external symbols to
distinguish the bundled libraries from existing system libraries and
to avoid runtime errors.

User code compiled correctly with the header files provided by the
bundled image libraries need not be changed.

The nanosvg library is not affected.

\section bundled-status Current status
\code
Current versions of bundled libraries (as of February 14, 2025):
  Library       Version/git commit Release date         FLTK Version
  --------------------------------------------------------------------------
  jpeg          jpeg-9f            2024-01-14           1.4.0
  nanosvg       7aeda550a8 [1]     2023-12-02           1.4.0
  png           libpng-1.6.44      2024-09-12           1.4.1
  zlib          zlib-1.3.1         2024-01-22           1.4.0
  libdecor      f7cd7ffd   [2]     2025-01-21           1.4.2
  --------------------------------------------------------------------------

Previous versions of bundled libraries (FLTK 1.3.x):

  Library       Version            Release date         FLTK Version
  --------------------------------------------------------------------------
  jpeg          jpeg-9e            2022-01-16           1.3.9
  png           libpng-1.6.40      2023-06-21           1.3.9
  zlib          zlib-1.3           2023-08-18           1.3.9

  jpeg          jpeg-9d            2020-01-12           1.3.6 - 1.3.8
  png           libpng-1.6.37      2019-04-14           1.3.6 - 1.3.8
  zlib          zlib-1.2.11        2017-01-15           1.3.6 - 1.3.8
  --------------------------------------------------------------------------


[1] Git commit in branch 'fltk' of https://github.com/fltk/nanosvg
    See also git tag 'fltk_yyyy-mm-dd' where yyyy-mm-dd == "Release date"
    and file nanosvg/README.txt.
[2] Git commit in https://gitlab.freedesktop.org/libdecor/libdecor
\endcode

<h2>General information:</h2>

  FLTK does not include the entire library distributions. We only provide the
  source files necessary to build the FLTK library and some README and/or
  CHANGELOG files. There are no test programs or other contributed files.

  We use our own build files, hence a few files MUST NOT be upgraded when
  the library source files are upgraded. We strive to keep changes to the
  library source files as small as possible. Patching library code to
  work with FLTK should be a rare exception. Symbol prefixing with prefix
  'fltk_' is one such exception to the rule.

  If patches are necessary all changes in the library files should be
  marked with "FLTK" in a comment so a developer who upgrades the library
  later is aware of changes in the source code for FLTK. Look for 'FLTK'
  and/or 'fltk_' to find the differences.

  Additional comments should be added to show the rationale, i.e. why
  a particular change was necessary. If applicable, add a reference to
  a Software Trouble Report, GitHub Issue or Pull Request (PR) like
  "STR 3456", "Issue #123", or "PR #234".


\section bundled-how-to How to update the bundled libraries

  It is generally advisable to use a graphical merge program. I'm using
  'meld' under Linux, but YMMV.

  Do not add any source files unless they are required to build the library.

  Some config header files may be pre-generated in the FLTK sources. These
  header files should be left untouched, but it may be necessary to update
  these files if new items were added to the new library version. In this
  case the new header should be pre-generated on a Linux system with default
  options unless otherwise mentioned below for a specific library.
  Currently there are no known exceptions.


<h2>Merging source files:</h2>

  Please check if some source and header files contain "FLTK" comments
  and/or 'fltk_' symbol prefixing to be aware of necessary merges.
  It is also good to download the distribution tar ball or Git source
  files of the previous version and to run a (graphical) diff or merge
  tool on the previous version and the bundled version of FLTK to see
  the "previous" differences.

  Files that were not patched in previous versions should be copied to
  the new version w/o changes. Files that had FLTK specific patches must
  be merged manually. FLTK patches should be verified (if still necessary)
  and should be kept in the new source files.

  Source and header files that have been added in the new library version
  should be added in FLTK as well if they are necessary to build the
  library. A simple "trial and error" should be sufficient to find files
  that need to be added. Added files must be added to FLTK's build files
  as well, usually to both `Makefile' and `CMakeLists.txt' to be used in
  configure/make and in CMake based builds, respectively.


<h2>Upgrade order:</h2>

  There is only one dependency between all bundled libraries: libpng
  depends on zlib. Hence zlib should be upgraded first, then all other
  libs can be upgraded in arbitrary order.


<h2>Tests after merge:</h2>

  Tests should be done on as many platforms as possible, both with
  autotools (configure/make) and CMake. Windows (Visual Studio) and
  macOS (Xcode) builds need CMake to generate the IDE files.


<h2>Upgrade notes for specific libraries:</h2>

  The following chapters contain informations about specific files and
  how they are upgraded. Since the changes in all bundled libraries are
  not known in advance this information may change in the future. Please
  verify that no other changes are necessary.


\section bundled-zlib zlib:

  Website:    https://zlib.net/
 \n Download:   See website and follow links.
 \n Repository: git clone https://github.com/madler/zlib.git

  zlib should be upgraded first because libpng depends on zlib.

  Download the latest zlib sources, `cd' to /path-to/zlib and run
\code
    $ ./configure --zprefix
\endcode
  This creates the header file 'zconf.h' with definitions to enable
  the standard 'z_' symbol prefix.

  Unfortunately zlib requires patching some source and header files to
  convert this 'z_' prefix to 'fltk_z_' to be more specific. As of this
  writing (Nov. 2021) three files need symbol prefix patches:

    - gzread.c
    - zconf.h
    - zlib.h

  You may want to compare these files and/or the previous version to
  find out which changes are required. The general rule is to change
  all occurrences of 'z_' to 'fltk_z_' but there *are* exceptions.


  The following files need special handling:

    - CMakeLists.txt: Keep FLTK version, update manually if necessary.
    - Makefile: Same as CMakeLists.txt.
    - gzread.c: Merge changes (see above, manual merge recommended).
    - zconf.h:  Merge changes (see above, manual merge recommended).
    - zlib.h:   Merge changes (see above, manual merge recommended).
    - makedepend: Keep this file.

Run `make depend' in the zlib folder on a Linux system after
      the upgrade to update this file.


\section bundled-png png:

  Website:    http://libpng.org/pub/png/libpng.html
\n  Download:   See website and follow links.
\n Repository: git clone https://git.code.sf.net/p/libpng/code libpng

  libpng should be upgraded after zlib because it depends on zlib.

  Download the latest libpng sources, `cd' to /path-to/libpng and run
\code
    $ ./configure --with-libpng-prefix=fltk_
    $ make
\endcode
  This creates the header files 'pnglibconf.h' and 'pngprefix.h'
  with the 'fltk_' symbol prefix.

  The following files need special handling:

    - CMakeLists.txt: Keep FLTK version, update manually if necessary.
    - Makefile: Same as CMakeLists.txt.
    - pnglibconf.h: Generate on a Linux system and merge (see above).
    - pngprefix.h:  Generate on a Linux system and merge (see above).
    - makedepend: Keep this file.

Run `make depend' in the png folder on a Linux system after
      the upgrade to update this file.


\section bundled-jpeg jpeg:

  Website:    https://ijg.org/
\n  Download:   See website and follow links.
\n  Repository: N/A

  Download the latest jpeg-xy sources on a Linux (or Unix) system,
  `cd' to /path-to/jpeg-xy and run
\code
    $ ./configure
    $ make [-jN]
\endcode
  This builds the library and should create the static library file
  '.libs/libjpeg.a'.

  Execute the following command to extract the libjpeg symbol names
  used to build the 'prefixed' libfltk_jpeg library:
\code
  $ nm --extern-only --defined-only .libs/libjpeg.a | awk '{print $3}' \
    | sed '/^$/d' | sort -u | awk '{print "#define "$1" fltk_"$1}' \
    > fltk_jpeg_prefix.h
\endcode
  This creates the header file 'fltk_jpeg_prefix.h' with the
  '# define' statements using the 'fltk_' symbol prefix.

  The following files need special handling:

    - CMakeLists.txt: Keep FLTK version, update manually if necessary.
    - Makefile: Same as CMakeLists.txt.
    - fltk_jpeg_prefix.h:  Generate on a Linux system and merge (see above).
    - jconfig.h: keep changes flagged with \verbatim /* FLTK */ \endverbatim
    Note: more to come...
    - makedepend: Keep this file.

Run `make depend' in the jpeg folder on a Linux system after
      the upgrade to update this file.


\section bundled-nanosvg nanosvg:

  Website:    https://github.com/memononen/nanosvg
\n  Download:   See website and follow links.
\n  Repository: git clone https://github.com/memononen/nanosvg.git
\n  FLTK Fork:  git clone https://github.com/fltk/nanosvg.git

  FLTK has its own GitHub fork of the original repository (see above).

  The intention is to update this fork from time to time so the FLTK
  specific patches are up-to-date with the original library. Hopefully
  the FLTK patches will be accepted upstream at some time in the future
  so we no longer need our own patches.
  AlbrechtS, 04 Feb 2018.

  Update (Feb 22, 2021): The upstream library is officially no longer
  maintained (see README.md) although updates appear from time to time.

  Use this fork (branch 'fltk') to get the nanosvg library with FLTK
  specific patches:
\code
    $ git clone https://github.com/fltk/nanosvg.git nanosvg-fltk
    $ cd nanosvg-fltk
    $ git checkout fltk
    $ cd src
    $ cp nanosvg.h nanosvgrast.h /path/to/fltk-1.4/nanosvg/
\endcode
  This library does not have its own build files since it is a header-only
  library. The headers are included in FLTK where necessary.

  The following files need special handling:

    nanosvg.h:     Merge or download from FLTK's fork (see above).
\n    nanosvgrast.h: Merge or download from FLTK's fork (see above).

  Maintaining branch 'fltk' in FLTK's fork of nanosvg (fltk/nanosvg):

    Only maintainers with write access on fltk/nanosvg can do this.
    Others can fork our fltk/nanosvg fork in their own GitHub account
    and either open a PR on fltk/nanosvg or tell us about their
    changes in fltk.development.

    Use something similar to the following commands to update FLTK's
    fork of nanosvg to the latest version. Commands are only examples,
    you may need to change more or less, depending on the outstanding
    updates.

    Step 1: clone the fltk/nanosvg fork, set the remote 'upstream',
    and update the 'master' branch:
\code
    $ cd /to/your/dev/dir
    $ git clone https://github.com/fltk/nanosvg.git nanosvg-fltk
    $ cd nanosvg-fltk
    $ git remote add upstream https://github.com/memononen/nanosvg
    $ git checkout master
    $ git pull upstream master
\endcode
    Note: the 'master' branch must never be changed, i.e. it must
    always be the same as 'upstream/master'. Never commit your own
    (FLTK specific) changes to branch 'master'.

    Step 2: rebase branch 'fltk' on the new master (upstream/master),
    fix potential conflicts, and tag the new branch.

    It is important to keep the individual FLTK specific patches intact
    (one commit per patch) because this will preserve the history and
    the committer and make it easier to skip single patches when they
    are accepted upstream.
\code
    $ git checkout fltk
    $ git rebase upstream/master
\endcode
    At this point you may need to fix conflicts! Do whatever is
    necessary to update the branch 'fltk'.

    Now `git tag' the 'fltk' branch for later reference.

    Hint: use `git show <any-older-tag-name>' to see its contents.
    I like to write a summary of commits in the tag comment.
\code
    $ git tag -a fltk_yyyy-mm-dd fltk
\endcode
    Replace 'yyyy-mm-dd' with the current date and add a comment
    when asked for it (your editor will open an empty file).

    Step 3: at this point it is recommended to copy the changed
    header files to your working copy of the FLTK library and test
    the changes. If anything is wrong, go back, fix the bugs
    and change the git tag (delete and create a new one).

    Step 4: push the new branch 'fltk' and the tag to the fltk/nanosvg
    repository:
\code
    $ git push -f origin fltk
    $ git push origin fltk_yyyy-mm-dd
\endcode
    Step 5: copy the changed files to your working copy of the FLTK
    repository (if not done already), update this file accordingly,
    and commit/push the update to the fltk/fltk repository.


\section bundled-libdecor libdecor:

  Website:    https://gitlab.freedesktop.org/libdecor/libdecor
\n  Download:   See website and follow links.
\n  Repository: git clone https://gitlab.freedesktop.org/libdecor/libdecor.git

  libdecor is used by the Wayland/X11 hybrid platform to draw window
  titlebars when FLTK apps run as Wayland clients and the running
  Wayland compositor uses client-side decoration. In the future, when
  libdecor will have made its way into Linux packages, FLTK will use
  the system version of libdecor. libdecor will remain as an FLTK bundle to
  support Linux configurations where the libdecor package is not
  available or not installed.

  FLTK uses libdecor source files without any modification.
  This part of the libdecor source tree is copied to directory libdecor/ of
  the FLTK source tree:
  <pre>
    LICENSE
    README.md
    src/    ... and files below except meson.build files
</pre>
  Furthermore, directory libdecor/build/ of the FLTK source tree does not
  originate from the libdecor source tree but contains 3 FLTK-created files.
  File build/Makefile may need changes if a libdecor update adds or renames source files.
*/
