CHANGES IN FLTK 1.3.9                                   RELEASED: Dec 09 2023

FLTK 1.3.9 is a maintenance release with some fixes and enhancements.

Highlights in this release:

  - Support macOS up to macOS 14 "Sonoma".
  - Update bundled libraries to current versions (see below).
  - Introduce bundled image library "prefixing" to avoid conflicts
    with system libraries.
  - New CMake option FLTK_MSVC_RUNTIME_DLL to select Visual Studio
    Runtime version: DLL (/MDx) or static (/MTx), see README.CMake.txt


Bundled library versions (see also README.bundled-libs.txt):

    Library       Version            Release date
    ---------------------------------------------
    jpeg          jpeg-9e            2022-01-16
    png           libpng-1.6.40      2023-06-21
    zlib          zlib-1.3           2023-08-18


Details:

  Al<PERSON> Schlosser:
    Backport X11 INCR protocol fixes from 1.4.0 (issue #451)
    X11: Suppress compiler warnings when using gcc or clang
    Fix crash if a program exits before it opens a window (STR 3484).
    Fix compilation error with current Visual Studio 2022
    Windows: #define WIN32 if not defined by user
    Backport warning fixes from 1.4.0 in src/fl_draw.cxx (#693)
    Fix compiler warning as pointed out in PR #693
    Fix another compiler warning (#693)
    Remove unused variable, fix "type issue" (#445, part 2)
    Fix stack buffer overflow found by address sanitizer
    Fix "gtk+ rendering" (GitHub Issue #443)
    Fix doxygen warnings
    Bump version numbers, prepare release 1.3.9
    Fix several compiler warnings
    Update bundled image libraries and zlib to current versions
    Update README, README.CMake.txt, and some support files
    Fix compiler warnings: backported from 1.4 (git 'master')
    CMake/MSVC: remove confusing recommendation to rerun CMake
    Documentation: remove dark color on title page
    Raise CMake minimum required version to 3.15 and more

  ManoloFLTK:
    macOS platform: Issue #325 "Disabling IM disables Greek and Cyrillic layouts"
    Fix fullscreen window level corner cases on macOS
    Fix issue #373 apparent with macOS platform and SDK ≤ 10.13
    Issue #452: Fl::get_font_name failure on OS-X.
    Issue #454: crash in Fl::get_font_name().
    Issue #469: Fl_Sys_Menu_Bar menu item shortcuts using Esc or Tab don't work on Mac
    Fix "Focus is lost leaving full screen on macOS 13" (#608)
    Add support of macOS Ventura 13.0 and macOS Sonoma 14.0
    macOS: remove configure option --enable-x11 and CMake OPTION_APPLE_X11;
      this functionality remains in FLTK 1.4.
    configure.ac: make sure local-png and local-zlib always run together
    Remove the -mwindows argument from CFLAGS and CXXFLAGS

Matthias Melcher:
    Issue #188: Fix reference counts and search for Fl_Shared_Image original

  YX:
    Fix IME problem (issue #270)


CHANGES IN FLTK 1.3.8                                   RELEASED: Nov 20 2021

FLTK 1.3.8 is a maintenance release with some fixes and enhancements.

Details:

  Albrecht Schlosser:
    Make "FLTK_CONSOLIDATE_MOTION" user-definable (issue #76)
    Fix compiler warnings (backported from 1.4)
    Add new dialog fl_choice_n() with extended return values (#282)

  ManoloFLTK:
    Account for deprecation of [NSBitmapImageRep initWithFocusedViewRect:]
      in macOS 10.14
    macOS: fix fullscreen window when other windows were created before
    Fix issue #287: "FLTK 1.3.6 doesn't handle fullscreen on macOS"
    Fix issue #288: "FLTK 1.3.6+ doesn't notify window movement on macOS"
    Fix issue #279: "HiDpi issue on macOS with retina display"
    macOS: Allow building with non-Apple compiler that may not support blocks


CHANGES IN FLTK 1.3.7                                   RELEASED: Jul 25 2021

FLTK 1.3.7 is a maintenance release for macOS only. It fixes one
regression introduced in FLTK 1.3.6 and two long standing timer
issues on macOS.

Other platforms than macOS are not concerned.

Changes:

  Avoid premature FL_RELEASE event at start of drag-n-drop operation
  Fix a timer inconsistency and prevent a crash
  Fl::add_timeout() must always create a new timer (#248)



CHANGES IN FLTK 1.3.6                                   RELEASED: May 15 2021

There have been no changes since FLTK 1.3.6rc2.


CHANGES IN FLTK 1.3.6rc2                                RELEASED: May 04 2021

FLTK 1.3.6rc2 fixes some minor issues of 1.3.6rc1, particularly macOS
bundle generation. It addresses an issue that bundles can't be executed
on macOS 11.3 Big Sur if built with configure/make after downloading
the release tarball because of the macOS "quarantine" feature.

Details:

  Albrecht Schlosser:
    Rename file 'VERSION' to 'fltk_version.dat' (#191)
    Fix version number in doxygen docs
    Fix build with Cairo for CMake < 3.13
    CMake: fix old (pre 3.13) link_directories() usage
    Enhance CMake build configuration summary
    Refactor macOS bundle generation to avoid "quarantine"
    Fix a new compiler warning


CHANGES IN FLTK 1.3.6rc1                                RELEASED: Apr 26 2021

FLTK 1.3.6 is a maintenance release with new macOS Big Sur support
and some bug fixes. macOS Big Sur is supported on both Intel and the
new Apple Silicon (M1) chips.

CMake support has been improved but is not yet "perfect". Documentation
has been enhanced, bundled image libraries have been updated to current
versions.

The following lists are subsets of all commits. References in '(...)'
are either STR numbers (STR xxxx) or GitHub Issues or Pull Requests
(#nnn).


Bug fixes and other improvements

  Albrecht Schlosser:
    Fix Fl::add_timeout() in draw() under Linux (STR 3188)
    X11: Fix X Input Methods (XIM) (STR 3502, 3192)
    Fix overly restrictive JPEG filter (#81)
    Fix DND in read-only Fl_Input (Fl_Output) (#113)
    Fix offscreen drawing under X11 (STR 3384)
    Fix potential fluid crashes (STR 3420) + memory leak
    Fix X11 copy-paste and drag-and-drop target selection (#182)
    Fix CMake install procedure (#212)
    Avoid "Bogus FL_MOVE/FL_DRAG events" (#76)
    CMake: Document FLTKConfig.cmake and set FLTK_INCLUDE_DIR

  Greg Ercolano:
    Fix issue92, added -d debug flag to fluid (#92)
    Merge pull request #176 from ComputerNerd/errmsg-fix-13

  ManoloFLTK:
    X11: add support for copy+paste of image within one app
    Windows: add bitmap version of graphics when copying to clipboard
    Fix use of Xrender extension with old, 16-bit framebuffers.
    Fix for Fl_Native_File_Chooser under macOS 10.15 Catalina and ...
    Restore macOS capacity to turn window fullscreen encompassing several screens.
    Improve Fl_Copy_Surface under macOS
    Set OPTION_USE_SYSTEM_LIBPNG/JPEG off under macOS by default
    Have Fl_Pack::draw() call Fl_Group::init_sizes() on its parent group.
    CMake support of the Darwin+XQuartz test platform
    Add support of macOS "Big Sur" 11.0
    Fix when building with SDK 10.15 and running with 11.0 Big Sur
    Backport fix for issue #185 "Shared Image reload() loses initial dimensions" from branch 1.4
    Fix fl_endpoints() for macOS platform that was not effective.
    Fix stack corruption when loading GIF (pull request #200)
    Restore ./configure --enable-x11 on macOS
    Fix crash when terminating any macOS app by cmd-Q

  OKAMURA, Yasunobu:
    Fix JIS Keyboard dead keys


CHANGES IN FLTK 1.3.5                            RELEASED: Mar 03 2019

  Bug fixes and other improvements

  - Updated CHANGES for release, removed one wrong entry


CHANGES IN FLTK 1.3.5rc2                         RELEASED: Feb 17 2019

  Bug fixes and other improvements

  - Fix Xcode FLTK project for use under macOS 10.14


CHANGES IN FLTK 1.3.5rc1                         RELEASED: Feb 11 2019

  New features and enhancements (highlights)

  - Add support for macOS 10.13 "High Sierra" and 10.14 "Mojave".
  - Add FL/platform.H for compatibility with FLTK 1.4 and higher.
  - Improve OpenGL(3+) support.
  - Improve Cairo support and (CMake) Cairo detection.

  Bug fixes and other improvements

  - Fix for STR 3473 (and its duplicate STR 3507) to restore configure-based
    builds on recent Linux/Unix distributions where the freetype-config
    command has been replaced by pkg-config.
  - Fix Fl_Tree::insert() with pos out ouf range (issue #18).
  - Fix Fl_GIF_Image Color Table handling (STR 3491).
  - Make sure not to access Fl_Menu_ widgets if the widget gets
    deleted while the menu (pulldown) is open (STR 3503).
  - Fix CMake build (fluid) on case sensitive macOS file systems.
  - Windows/OpenGL: improve pixel format detection (STR 3119).
  - Add support for macOS 10.14 "Mojave": all drawing to windows is done
    through "layer-backed views" when the app is linked to SDK 10.14.
  - MacOS platform: fix regression in macOS 10.13 where an unbundled app
    had its system menu bar unresponsive.
  - MacOS 10.10 platform: Fl_Window::fullscreen() and fullscreen_off()
    no longer call Fl_Window::hide() + Fl_Window::show().
    The new procedure essentially resizes the window, as done on
    the X11+EWMH and Windows platforms. This improves in particular
    the possibility to turn an Fl_Gl_Window fullscreen on and off.
  - MacOS platform: Support \r\n as end of line used by recent
    MS Office apps in clipboard.
  - Fix Fl_Text_Editor overstrike mode (STR 3463).
  - More consistent and optimized "gleam" scheme box drawing.
  - Fix a MinGW build error with new MinGW versions as of Feb 2018
    (undefined S_OK and __FD_ISSET), see STR 3454.
  - Windows/CMake: Add missing definition of WIN32 for Windows builds.
    This seems to be necessary for VS2017 and other compilers that
    don't #define WIN32, but FLTK relies on this preprocessor macro.
    Autoconf/configure and bundled IDE files #define WIN32 as well.
  - Fix illegal memory access after free when closing fluid (valgrind
    reports "Invalid read of size 4"), see also STR 3427.
  - Fix crash when closing fluid with Fl_Table (STR 3427).
  - Fix ignored buffer pre-allocation (requestedSize) in Fl_Text_Buffer.
    See fltk.general "Fl_Text_Buffer constructor bug" on Dec 5, 2016.
  - Fix Fl_Browser background and text color parsing (STR 3376).
  - Fix Windows CreateDC/DeleteDC mismatch (STR 3373).
  - Update bundled configure scripts config.guess and config.sub.
  - Fix configure --enable-cairo --enable-cairoext,
    see report in fltk.general:
    https://groups.google.com/forum/#!topic/fltkgeneral/x80qQ6wt0s4
  - Fix macOS timer implementation (STR 3387).
  - Fix drop-to-FLTK-widget under MSWindows when the desktop is
    rescaled (STR 3390).
  - Add support for macOS 10.13 "High Sierra". Subwindows don't
    move correctly under 10.13 without this fix.


CHANGES IN FLTK 1.3.4-1                          RELEASED: Nov 15 2016

  Bug fixes and other improvements

  - Fix Windows IDE solutions VisualC2008 and VisualC2010 to
    build properly if the FLTK source directory is located
    in a path that contains spaces.

  Note: this is a source tar file update that is only necessary
  if you build one of the above mentioned Visual Studio IDE
  solutions in a path with spaces. Nothing else changed, no
  documentation updates.


CHANGES IN FLTK 1.3.4                            RELEASED: Nov 11 2016

  Bug fixes and other improvements

  - Fix test/native-filechooser help display artifact (STR #3349).
  - MacOS: fix a crash when closing a fullscreen window.
  - Accept empty Fl_Pixmap in Fl_RGB_Image c'tor (STR #3348).
  - MacOS: fix drawing error when a window containing a hidden subwindow
    is changed to fullscreen (error visible with the tabs test program).
  - MacOS: fix possible crash when enabling fullscreen in test/scroll.
  - Add a snapshot of known STR's and RFE's (KNOWN_BUGS.html).

CHANGES IN FLTK 1.3.4rc3                         RELEASED: Oct 31 2016

  Bug fixes and other improvements

  - Fix string truncation in fluid (STR #2722 continued).
  - MacOS: fix a small problem where the Command-Quit menu hotkey
    may not work if Fl_Native_File_Chooser is used before any
    window is shown.


CHANGES IN FLTK 1.3.4rc2                         RELEASED: Oct 24 2016

  Bug fixes and other improvements

  - Make sure the "File Systems" menu item of Fl_File_Chooser
    lists "/" as the first mounted file system (Linux/Unix platform).
  - Added line numbers to fluid Edit -> Show Source Code viewer.
  - Improved fluid's template handling (STR #3335).


CHANGES IN FLTK 1.3.4rc1                         RELEASED: Oct 17 2016

  New features and extensions

  - Added support for Mac OS 10.11 "El Capitan" and 10.12 "Sierra".
  - Added full support of true subwindows to the Mac OS X platform.
    Window nesting to any depth is possible. An Fl_Gl_Window window or
    subwindow can contain Fl_Window's as subwindows.
  - Fluid now supports external editors (STR #3213)
    See: Edit -> GUI Settings -> Use external editor
  - Added method Fl_Widget::is_label_copied().
  - Added methods Fl_Copy_Surface::w(), Fl_Copy_Surface::h(),
    and Fl_Copy_Surface::draw_decorated_window().
  - Added method Fl_Image_Surface::draw_decorated_window().
  - Added method Fl_Shared_Image *Fl_Image_Surface::highres_image()
    to draw into a high-resolution bitmap image (Mac OS X only).
  - Added support for OpenGL version 3 and later.
  - Added method Fl_Mac_App_Menu::custom_application_menu_items()
    allowing customization of the application menu (Mac OS X only).
  - Added 'fluid -u' command-line switch to upgrade fluid files in
    batch mode. This option opens a fluid ('.fl') file and saves it
    overwriting the old file with the current syntax and library version
    number. Use with care (input file is overwritten unconditionally).
  - Added FL_ZOOM_GESTURE gesture for OS X (only). Needs implementation
    on other platforms.
  - Added FL_ABI_VERSION, FL_API_VERSION, Fl::abi_version(), and
    Fl::api_version(). Constants can be used to determine the compiled
    FLTK version. Functions can be used to find the linked library version.
  - Added Fl::abi_check() method to test if the runtime (linked)
    ABI version is correct.
  - Added Fl_Image::fail() to test if an image was loaded successfully
    to allow for easier error detection when loading images (STR #2873).
  - Added line numbers to fluid's source code viewer for New -> Code

  New configuration options (ABI version)

  - FLTK's ABI version can now be configured with 'configure', CMake, or
    by editing a supplied file when using the bundled IDE projects.
    See documentation in README.abi-version.txt.

  1.3.4 ABI FEATURES

  - Added deactivated user icon to Fl_Tree.
  - Added Fl_Shared_Image::scale(width, height) which gives a shared
    image its own drawing size, independently of the size of the
    underlying image. This improves image drawing on high resolution
    surfaces such as Laser printers, PDF files, and Apple retina displays.

  Other improvements

  - Configure now correctly sets HAVE_* variables only if both the
    library and the header files were found. The configuration process
    is aborted if Xft was requested explicitly with --enable-xft and
    Xft was not found. The configuration summary now shows _found_
    options as opposed to requested options.
  - Improved drawing of rounded box (STR #2943).
  - Full support of Apple 'retina' displays for which one drawing unit
    corresponds to two pixels.
  - The Mac OS X platform no longer uses the deprecated AGL
    (Apple GL library) to draw OpenGL data. Instead, it uses standard
    Cocoa APIs. This allows FLTK to support drawing GL scenes at high
    resolution when an Fl_Gl_Window is mapped to a 'retina' display.
  - Added Fl_Gl_Window::pixel_w(), Fl_Gl_Window::pixel_h(), and
    Fl_Gl_Window::pixels_per_unit() useful for high resolution OpenGL windows.
  - fl_read_image() now captures all pixels within the rectangle
    described by its arguments, whether they belong to a GL scene
    or not (STR #3142). It also captures subwindows of GL windows.
  - Fl::delete_widget() now hides the widget or window immediately
    (i.e. when called) - only destruction is delayed as before.
  - FLTK header files don't expose X11 definitions in user code any more
    unless requested by including FL/x.H explicitly or implicitly.
  - The PostScript code output when printing images under Linux/Unix
    is now much smaller by using lossless compression techniques.
  - The Linux/Unix printer dialog now uses BSD-style printing commands
    (lpr/lpq) when SystemV-style commands (lp/lpstat) are not available.
  - Drawing alpha-blended images under X11 is now accelerated with
    Xrender.
  - The font used for the FL_COURIER font family was changed on the Mac OS X
    platform from 'Courier New' to 'Courier' because it was too thin.
  - Text drawing on the Mac platform supports Unicode 'variation selectors'
    in the range [0xFE00-0xFE0F].
  - Added a Mac OS implementation of Fl_Window::wait_for_expose() that
    became necessary with Mac OS 10.10 "Yosemite".
  - Added the libfltk target to the Xcode project producing a static
    version of the FLTK library (Mac OS X).
  - Restored the possibility to call Fl::set_font() before main() starts.
  - Allow using the --enable-x11 configure option on the Mac OS platform
    which produces an X11 version of the FLTK library (don't use unless
    you know what you're doing).
  - FLTK code and fluid-generated code can be used in static initializers.
  - Added light gray separator line to fluid's widget browser (STR #2997).
  - Improved tooltip behavior for huge tooltips: remove flicker, support
    key/mouse dismiss (STR #2650).
  - Modifier key names displayed in shortcut labels can now be localized.
    See documentation of fl_shortcut_label(). You can set modifier key
    names for Ctrl, Alt, Shift, and Meta by assigning global string
    pointers to your translated modifier key names.
  - Many documentation improvements and fixes.
  - Fixed many compiler warnings (STR #2988).
  - Fluid now generates code with less compiler warnings (STR #2813).
  - Many CMake improvements and fixes. Note: CMake is mostly operating
    now, but not yet recommended for production use. Please test and
    report issues.
  - Updated bundled zlib from 1.2.5 to 1.2.8.
  - Updated bundled libjpeg from jpeg-8c to jpeg-9a.


  Bug fixes

  - Fix potential crash when using keyboard navigation on Fl_Tabs
    widget without children (STR #3333).
  - Fix potential crash if Fl_Window::icon(NULL) was called to clear
    (reset) a window icon. This is now legal and documented behavior.
    The same applies to Fl_Window::default_icon().
  - Allow widget callback to be NULL, i.e. no callback (STR #2835).
  - Fixed Fl_Help_View buffer overflow (STR #3275).
  - Fl_Browser now correctly adjusts its scrollbars when the default
    text size is changed with textsize(int) (STR #3057).
  - Fixed Fl_Text_Display/Fl_Text_Editor slow scrolling, line number
    display, wrap mode "hiding" text behind scrollbars, and more
    scrollbar handling (alignment) (STR #3272).
  - Fixed valgrind warning (uninitialized variable) in Fl_Window.
    Visible only with FLTK_ABI_VERSION >= 10303, since FLTK 1.3.3.
  - Fixed Windows drag'n'drop not showing insert position if the drop
    target is inside the same window or process (STR #3209).
  - Fixed undefined reference building shared cairo library (STR #3276).
  - Fixed Fl_Browser if text argument to some methods is NULL (STR #3269).
  - Fixed missing image release in fluid (STR #2840).
  - Fixed out-of-bounds memory access in fluid (STR #3263).
  - Fixed trailing white space in fluid .fl files (STR #3239).
  - Several box types were not drawn correctly when deactivated.
    The background color for deactivated widgets is now correct:
    fl_inactive(color()) (STR #2907).
  - Fix inconsistent interpretation of ld() in image handling
    (STR #3308). This is a minor issue since ld() is usually 0 or
    w()*d(). Documentation has been fixed, and ld() handling is now
    consistent in Fl_(RGB_)Image and fl_draw_image() and friends. See
    documentation of Fl_Image and Fl_RGB_Image for more information.
  - Fixed Fl_Pixmap (background) drawing to respect the clip region
    (STR #3206).
  - Fixed reading .pbm image files: 1 is now interpreted as black,
    and images whose width are a multiple of 8 are correctly read.
    Note: if you relied on the faulty behavior you may need to fix
    your image files.
  - Restored window background drawing, particularly the 'plastic'
    scheme's background (STR #3059). This was a regression since
    FLTK 1.3.0.
  - Prevent sending (FL_SHORTCUT) events to inactive widgets (STR #3216).
  - Fixed button label clipping (STR #3237).
  - Fixed Fl_Menu_::item_pathname() handling of descending submenu
    pointers (STR #3177).
  - Fl_Text_Display: style buffer colors are no longer manipulated by
    fl_contrast() for normal text drawing (fltk.coredev thread started
    04/08/15, Subject: "RFC: Fl_Text_Display style buffer color weirdness")
  - Fl_Tree::deactivate() now affects draw color of items (must have
    ABI 1.3.3 or higher enabled). For icons to draw deactivated,
    enable ABI 1.3.4. (test/tree has a 'deactivate tree' button)
  - Fl_Tree::find_item() did not find items if the pathname contained
    identical strings (STR #3234).
  - Fixed possible bad border effect when a set of radio menu items
    is located first in its menu (STR #3176): Fl_Menu_Item::set_only()
    is deprecated and replaced by Fl_Menu_::set_only(Fl_Menu_item*).
  - Restore the correct state of mouse buttons and keyboard modifier keys
    after closing a file or printer dialog (STR #3221).
  - Fix for deactivate colors issue with Fl_Text_Display/Editor (STR #3219).
  - Fixed issue with MSWindows platform where the program kept running
    after closing all its windows (STR #3165).
  - Fix potential buffer overflow (MSWindows: BEX64 error) and problem with
    MSWindows environment variables "HOME"(MinGW) vs. "UserProfile"(DOS)
  - Fix issue with MSWindows platform where a window would decrease
    in size after each close/open (STR #3167).
  - Fix undefined reference XGetUtf8FontAndGlyph (STR #3191).
  - Fix potential keyboard hangup (STR #3192).
  - Fix for: .xbm images were not previewed (STR #3131).
  - Fixed crash on Mac platform when resizing a not yet shown() Fl_Gl_Window.
  - Fixed potential Windows GDI leak (STR #3254).
  - Fixed Linux/Unix potential program hang when dialog pops up while
    a menu is open (STR #3179).
  - Fixed a crash when a Unix/Linux program calls take_focus() before any
    window has been show()n.
  - Fixed an error on the Mac platform when drawing to an Fl_Image_Surface
    object without using the Fl_Image_Surface::draw() method.
  - Fixed STR #3268 where a fullscreen window could become relocated
    behind the menu bar and dock (Mac OS only).
  - Fixed STR #3207: ^C not working when numlock or capslock is on.
  - Fixed code editor in Fluid (STR #3184).
  - Fixed a potential hangup of the Fl::awake() queue under Windows if
    a window is resized or moved while the Fl::awake() message is sent
    (STR #3143).
  - Fixed a regression: restore the possibility to call
    fl_draw_image(buf,X,Y,W,H,D,L) with negative D and/or L arguments.
  - Fixed overflow in Fl_Valuator::precision(int) to 0...9 (STR #3280).


CHANGES IN FLTK 1.3.3                            RELEASED: Nov 03 2014

  New features and extensions

  - New class Fl_Copy_Surface allows copying graphical data to the
    clipboard in a cross-platform way (STR #3058).
  - Support for pasting graphical data from the clipboard to a widget.
  - New class Fl_Image_Surface allows drawing into an Fl_Image object.
  - Methods Fl::add_clipboard_notify() and Fl::remove_clipboard_notify()
    to get notifications whenever the clipboard changes (STR #2636).
  - New method Fl_Window::wait_for_expose() (STR #3124, STR #3129).
  - New bilinear scaling method for RGB images (STR #2869, STR #3062).
  - New method Fl_Widget::top_window() (STR #2948).
  - New method Fl_Widget::top_window_offset() (part of STR #2944).
  - New Fl_Tree_Item methods label_x(), label_y(), label_w(), label_h().
  - New methods Fl::enable_im() and Fl::disable_im() to enable/disable
    system Input Methods (IM).
  - New methods Fl::add_system_handler() and Fl::remove_system_handler()
    to intercept low level system events.
  - New FLTK scheme "gleam" (STR #2672).
  - New method Fl::is_scheme() to compare the current scheme name.
  - New method to set custom icons for windows (STR #2816),
    see Fl_Window::icon() for more info.
  - New method to set any custom cursor, based on a Fl_RGB_Image object.
    Also changed fallback cursors to use this method, so that fallback
    cursors are handled in a platform independent manner (STR #2660).
  - New ability to convert Fl_Pixmap to Fl_RGB_Image (STR #2659).
  - Support for full screen windows over multiple monitors.
  - New optional line numbers in Fl_Text_Display and options to control
    line numbers and word wrapping (on/off) in example test/editor.cxx.
  - On Linux/Unix, class Fl_Native_File_Chooser uses file dialogs from
    the Gnome environment (provided by the libgtk dynamic library) if
    available, and falls back to Fl_File_Chooser if not (STR #3088).
    Use option Fl::OPTION_FNFC_USES_GTK to enable/disable this feature.
  - New support for the Mac OS text input system that deals with character
    composition and input of languages with large character sets (e.g.
    Chinese and Japanese). This implementation has been reported to work
    well for Chinese. Superficial testing suggests it's also operational
    for Japanese. In-depth testing remains needed though.

  Other improvements

  - Many CMake improvements (e.g. STR #2962, 2977, 2994, 3045, 3055).
  - Some autoconf, configure improvements.
  - Removed 1024 bytes constraint in labels and browser lines (STR #2990).
  - Mac OS version of Fl_Native_File_Chooser: when using filters in a
    save file dialog, the output file extension gets changed when the
    user modifies the output file type.
  - Many Mac OS X improvements related to Fl_Sys_Menu_Bar.
  - Improved Mac OS X keyboard handling of special keys or combinations.
  - General Mac OS X improvements, incl. high resolution (Retina)
    displays, and support up to Mac OS X 10.10.
  - Documentation improvements and clarifications.
  - Printing support improvements, including GL windows.
  - Many improvements related to Fl_Tree (some of them only as ABI
    breaking features, see below).
  - Some static data has been made 'const' to improve memory usage
    with shared libraries.
  - Don't grab navkeys with modifiers (CTRL, ALT), as the application
    may want them (STR #3104).
  - Check compiler support of -fvisibility, -fvisibility-inlines-hidden,
    and use it, if available. Reduces shared library size.
  - Drag'n'drop behaves better now: if text is dropped in the widget it
    comes from, then it works like cut and paste.
  - Other improvements of DND handling when dragging text from other
    applications, e.g. Firefox and Thunderbird.
  - Windows message WM_QUIT is processed like SIGTERM, i.e. as a
    request to terminate the program.
  - Fluid no longer closes the code window when hitting ESC (STR #2997).
  - Improved Fl_Widget::when() handling in Fl_Tabs (STR #2939).
  - Improved support for more recent compilers (clang and gcc) that
    issue more warnings, and fixed some 32-/64-bit compilation issues.
  - Added method Fl_Window::clear_modal_states() to make it possible
    to remove the modal or non-modal state from a window (STR #3123).

  Bug fixes

  - Fixed word selection dragging in text input (STR #3014).
  - Fixed border padding for special cases (STR #3061, Windows only).
  - Fixed crash if Fl_Window::flush() called while window not shown()
    (STR #3028).
  - Fixed segfault in fl_set_fonts_xft.cxx when loading fonts not named
    as expected (STR #2976).
  - Fixed a memory leak in font loading/allocation (STR #3069).
  - Fixed minor memory leaks in handling/freeing of X11 objects.
  - Fixed OS X fl_read_image() and Fl_Paged_Device::print_window_part()
    when using a Retina display.
  - Fixed MinGW build if configure didn't find strcasecmp() (STR #2994).
  - Fixed access of protected member under Linux/Unix (STR #2903).
  - Fixed various Mac specific OpenGL issues (STR #2944)
  - Fixed PostScript printing with non-english locale (STR #3130).
  - Fixed a regression in Fl_File_Chooser since FLTK 1.1.8: the
    Enter key now selects the file again.
  - Fixed a bug in Fl_Native_File_Chooser (Windows) for some file name
    input filters (STR #3101).
  - Fixed out-of-bounds memory access in Fl_Text_Display (STR #2730).

1.3.3 ABI FEATURES

  To enable the following ABI features, put:
  #define FLTK_ABI_VERSION 10303
  ..at the top of your FL/Enumerations.H and rebuild FLTK and your app.

  - Added Fl_Window::shape(const Fl_Image*) to create arbitrarily-shaped
    windows
  - Made Fl_Help_View::handle() public and Fl_Help_View::draw() protected
    to enable inheritance and for consistency (STR #2834).
    Note: both methods were private.
  - Made Fl_Scroll::recalc_scrollbars() protected so that it can be used
    in derived widgets to calculate scrollbar visibility and positions.
  - Added Fl_Table::tab_cell_nav() for controlling Tab key navigation of table
  - Added Fl_Tree::get_selected_items(), returns the selected items as an array
  - Added Fl_Tree::item_draw_callback(), letting one define a custom draw
    function for Fl_Tree_Item's.
  - Fl_Tree: various related changes:
      o Added horizontal scrollbar
      o Separated draw() and tree size calculation
      o Added new public methods:
          > resize()           -- uses optimized dim calc, avoids tree recalc
          > next_item()        -- added parameters: direction, visibility
          > extend_selection() -- added parameters, improved algorithm
          > calc_dimensions()  -- calc tix/y/w/h, tox/y/w/h and scrollbars
          > calc_tree()        -- calc tree_w/tree_h
          > recalc_tree()      -- schedules calc_tree()
          > first_visible_item(), last_visible_item(), next_visible_item()
          > first_selected_item(), last_selected_item(), next_selected_item()
      o Added protected variables:
          > _tix/y/w/h      -- tree widget 'inner' dimension
          > _tox/y/w/h      -- tree widget 'outer' dimension
          > _tree_w,_tree_h -- entire tree hierarchy width/height
      o Deprecated:
          > item_clicked() -- use callback_item() instead
          > first_visible() -- use first_visible_item() instead
          > last_visible() -- use last_visible_item() instead

  - Fl_Tree_Item: various related changes:
      o Added Fl_Tree ptr: needed for auto-recalc when item modified directly
      o Added new methods tree(), recalc_tree()
      o Added new ctor that accepts Fl_Tree*
      o draw() parameters changed to include tree size calculations
      o Deprecated:
          > ctor using Fl_Tree_Prefs parameter (Fl_Tree* version better,
            and must be used for 1.3.3 ABI features to work correctly)
          > next_displayed() -- use next_visible() instead
          > prev_displayed() -- use prev_visible() instead
  - test/tree: added tests for newly added features

CHANGES IN FLTK 1.3.2                            RELEASED: Dec 12 2012

  - Removed unnecessary drawing calls (STR #2898)
  - Fixed regression in FLTK 1.3.1 for unbundled Mac OS FLTK applications
    that did not appear in dock nor have a menu bar (STR #2890).
  - Fl_Table::clear() now calls table->clear() for consistency. (STR #2889)
  - Fixed Fl_Scroll widget that fails under Mac OS X 10.8 and Retina
    display (STR #2887).
  - Prevents scrollbars from drawing when widget is sized too small
    to be visible (STR #2886).
  - Documented how to make a Mac OS X FLTK application launchable
    by dropping files on its icon.
  - Fixed a Mac-specific issue that appeared with OS 10.8 (Mountain Lion):
    long delay before opening when the application is started by dragging
    a file on the application icon.
  - Fixed use of PNG image from in-memory data (STR #2884).
  - Added static Fl_RGB_Image::max_size(size_t) to limit the maximum
    memory size allowed to RGB images (STR #2881).

CHANGES IN FLTK 1.3.1                            RELEASED: Nov 06 2012

  - Fixed utf_strncasecmp and utf_strcasecmp
  - Moved all inline constructors into source file to avoid bad DLLs
  - Fixed Fl_Widget::copy_label() and Fl_Window::copy_label() when
    called with the old label() (STR #2836)
  - Fixed Fl_Input_::maximum_size() documentation and replace() method
    to honor maximum_size() as number of allowed characters (STR #2747).
  - Fixed a potential crash if Fl_Double_Window::flush() was called
    before show()
  - Fixed regression (in FLTK 1.3.0) that could clear a radio
    button by using the keyboard (space or shortcut) (STR #2748)
  - Fixed fl_pie() drawing too small on X11 (STR #2703)
  - Fixed Fl_Menu issue with unusual menu flags (STR #2680)
  - Fixed Windows DLL import of fl_xid() (STR #2670)
  - Added Fl::screen_work_area() functions that compute the work area
    of a screen. These functions improve the positioning of menus.
    Changed Fl::x(), Fl::y(), Fl::w(), Fl::h() functions
    to return the origin and size of the work area of the main screen
    (STR #2695 + 2697)
  - Created the FL_SCREEN_CONFIGURATION_CHANGED event that is triggered
    when a screen is added, removed, moved or when resolution is changed
    (STR #2600)
  - Improved the description of page size and orientation by
    Fl_PostScript_File_Device.
  - Added support for horizontal wheel movement under X11 and Windows
    Vista and above (STR #2644).

1.3.1 ABI FEATURES
    (To enable the following ABI features, put: #define FLTK_ABI_VERSION 10301
    at the top of your FL/Enumerations.H and rebuild FLTK and your app)
  - Fl_Tree optimized to support large trees (eg. 100k items):
    Added _next_sibling and _prev_sibling to Fl_Tree_Item class,
    and support methods.


CHANGES IN FLTK 1.3.0                            RELEASED: Jun 16 2011

  New Features

  - Added UTF-8 Unicode support throughout FLTK and Fluid
  - Added Fl_Tree widget for hierarchical views
  - Added Fl_Table widget for widget layout
  - Added Fl_Native_Filechooser as a widget and global options
  - Added printing support (Fl_Printer class and friends) for native
    printing on Windows and Mac OS X, PostScript printing on X11
    with CUPS or lp, and PostScript file output on all platforms
  - Added basic Fl_Device abstraction layer for all drawing functions
  - Moved OS X code base to the more modern Cocoa toolkit thanks
    to the awesome work of Manolo Gouy (STR #2221)

  API and ABI Compatibility to FLTK 1.1.x

  - All strings are expected to be in UTF-8 Unicode encoding
  - Replaced Fl_Scroll::position(int,int) with scroll_to(int,int)
    to avoid overriding Fl_Widget::position(int,int) (STR #1303)
  - Although FLTK 1.3 maintains source code compatibility as good
    as possible, the binary interface changed considerably.
  - Updated the bundled libpng to v1.5.1 (released Feb  3, 2011)
  - Updated the bundled libjpeg to v8c   (released Jan 16, 2011)
  - Updated the bundled zlib to v1.2.5   (released Apr 19, 2010)

  API Enhancements and Changes

  - Changed font index to 32 bit
  - Changed font size to 32 bit
  - Changed widget coordinates to 32 bit
  - Corrected const methods of Fl_Text_{Buffer|Display|Selection}
    to be declared const, corrected an Fl_Text_Buffer attrib. typo
  - All draw() methods of widgets are now protected (STR #2142).
  - Changed Fl_Group::clip_children() to public (STR #2017)
  - Changed socket library to winsock2 (ws2_32.dll) instead of
    wsock32.dll for Windows. The dll is now loaded dynamically only
    if/when needed.
  - Changed hide() and show() methods. They are now virtual from
    Fl_Widget.
  - Added new label and image alignments (STR #2269)
  - Added global UI options (STR #2471)
  - Added class Fl_Widget_Tracker to simplify safe handling of widget
    deletion in callbacks. This is used in Fl_Widget::do_callback()
    to prevent accessing widgets after deletion in the callback.
  - Added sorting to Fl_Browser_ (STR #2113)
  - Added new method client_area() for easier positioning of children
    in Fl_Tabs (STR #2480)
  - Added interface to set color chooser mode (STR #2407)
  - Added Fl_Menu_::find_item by callback
  - Added loading jpeg images from memory
  - Added support for shortcuts for Fl_Input_, Fl_Value_Input, and
    Fl_Text_Display derived widgets (STR #1770)
  - Added Fl_Menu_ methods: insert(), find_index(), clear_submenu()
  - Hotspot behavior of fl_message() and other common dialogs is now
    optional (STR #2561).
  - Common dialogs like fl_message() and fl_ask() can now have a window
    title (STR #2562).

  IDE Support and Command Line Build Options

  - FLTK now supports Apple Xcode 3, MS VisualC 2008, and
    MS VisualC 2010. MS VisualC 6 support is maintained as a
    compatibility base for VisualC.net, etc. . FLTK continues
    to support configure/make and improved CMake build files.
  - Added step by step README's for MSWindows, OS X, and four
    Linux distros.
  - Added "ide" subdirectory for all IDE support files
  - Added template to generate new projects with Xcode.
  - Added --enable-cairo and --enable-cairoext  configure options.
  - redesigned CMake files (STR #2317).
  - The new configure option --enable-x11 (lowercase 'x') enables
    Cygwin builds under Windows (with --enable-cygwin) to use X11
    instead of GDI drawing (STR #2147, #2183) and is ignored otherwise.
  - Changed "fltk-config --post foo" to create an application
    bundle rather than attaching a resource fork.
  - Changed default for configure option --with-links for common
    misspellings on case sensitive file systems like Unix/Linux.
    The new default is --without-links; use --with-links on
    Unix/Linux only if you need the links (e.g. lowercase '.h')

  Fluid

  - Added argument-less constructor in Fluid Widget Class
  - Added jpeg support to Fluid image() element
  - Added binary data type to Fluid
  - Empty functions in Fluid no longer create an
    implementation (STR #2259)
  - Fixed internationalisation of menus using Fluid (STR #2246)
  - Fixed menu item counting issue in Fluid (STR #2322)
  - Fixed lost top item in Fluid's tree browser (STR #2233)
  - Fixed Fluid dependency on X11 (STR #2261)
  - Fixed Fluid textcolor output (STR #1992)
  - Fixed wrong default value of Fl_Spinner in Fluid (STR #1991)

  Documentation

  - Added documentation for event delivery (STR #1983)
  - Added Fl_Scroll::bbox() documentation (STR #1893)
  - Updated documentation for Fl_Input_
  - Updated Copyright dates to 2010 (STR #2036)
  - Updated mirror sites in documentation (STR #2220)
  - Fixed documentation for Fl_Progress (STR #2209)
  - Fixed documentation (added missing COMCTL32.LIB dependency)

  Other Improvements

  - Added drag'n'drop support for Fl_Text_*
  - Added visual feedback for button shortcuts (STR #2372)
  - Added callback when double-clicking file in a file chooser
    (STR #2346)
  - Added alternative Xft font names (STR #2215)
  - Added mouse!=0 check in Fl_Glut_Window::handle (STR #2381)
  - Added indexing to Fl_Preferences
  - Added OS X cursor control to Fl_Input (STR #2169)
  - Added menu shortcut alignment for OS X
  - Added drop box to UTF-8 test that will show the UTF-8 encoding
    for the first dropped character
  - Added flexible gap size for text buffer (STR #2046)
  - Added clarification to Fl_GL_Window mode function (STR #1945)
  - Added alternative text input awareness on OS X
  - Improved handling of composed keys in OS X 10.5 and up
  - Improved stability of fl_read_image (STR #2021)
  - Much faster fl_read_image() for Windows (STR #2387).
  - Improved support for faulty X11 clients (STR #2385)
  - Integrated default menu into Demo test app
  - Replaced _WIN32 symbols that had come with UTF-8 and the
    new Fl_Table widget with WIN32
  - Widgets now remove stale entries from the default callback
    queue when they are deleted (STR #2302)
  - Managing all Widget flags in a single location now (STR #2161)
  - File chooser preview now recognizes UTF-8 encoded
    text files (STR #2218)
  - Setting a default font for Xft (STR #2216)
  - Restructured the unittest application
  - Fl_Preferences.H now doesn't include windows.h any more
    (Windows only, STR #2173).
  - Fl_Window::draw() now doesn't reset its x/y-coordinates to 0
    anymore before drawing itself and its children.
  - Fl_Help_View handles HTML2 font color specification (STR #890)
  - Widgets now remove themselves from their parent group (if any),
    when destroyed (STR #1894)
  - Changed Fl_Group and Fl_Scroll to resize themselves before
    resizing their children (STR #2032)

  Bug Fixes

  - Fixed potential crash in fl_xid() on Linux (STR #2635)
  - Fixed keyboard navigation in fl_choice() (STR #2591)
  - Fixed alpha blending under X11 when line data size != 0 (STR #2606)
  - Fixed Fl_Tabs selection border drawing, if tabs are at the bottom
    of the widget (STR #2480)
  - Fixed Compiling with mingw-w64 (STR #2308).
  - Fixed crashes when detecting illegal UTF-8 sequences
    in Fl_Text_* widgets (STR #2348)
  - Fixed Fl_Text_Display Tabulator calculations (STR #2450)
  - Fixed file access code to use UTF-8 strings (STR #2440)
  - Fixed ARM Unicode cross compilation issue (STR #2432)
  - Fixed xclass support for Fl_Window (STR #2053)
  - Fixed Caps Lock handling in X11/XIM (STR #2366)
  - Fixed handling of missing fonts in Xft (STR #2355)
  - Fixed OpenGL hide/show issue in OS X (STR #2260)
  - Fixed File Chooser preview hang if a device was choosen
  - Fixed blinking of selection when the mouse was dragged
    outside of the Fl_Text_* widget
  - Fixed Unicode support for Fl_Text_* widgets
  - Fixed menu and shortcut handling (STR #2243)
  - Fixed fltk-config to give --libs on one line (STR #2408)
  - Fixed outside label redraw damage areas (STR #2436)
  - Fixed compile errors when HAVE_LIBJPEG was not defined
    (STR #2382)
  - Fixed special handling for ISO back-tab keycode (STR #2369)
  - Fixed static allocation in Fl_Tabs (STR #2370)
  - Fixed Fl_Widget::measure_label() to const (STR #2406)
  - Fixed unbalanced Windows OleInitialize/OleUnitialize calls
    when loading/unloading the FLTK dll (STR #2417)
  - Fixed X11/XDBE double buffering (STR #2152, #2197)
  - Fixed menu and tooltip window animation bug under X11 (compiz)
    by setting an appropriate window type (STR #2082)
  - Fixed max. fd calculation, if USE_POLL is enabled (STR #2324)
  - Fixed clipping for offscreen rendering (OSX, STR #2330)
  - Fixed possibility of wrong flags() in Fl_Window::iconlabel()
    (STR #2161)
  - Fixed Scrollbar events when max is less than min (STR #2283)
  - Fixed crash in test/Editor when freeing buffer
    too soon (STR #2294)
  - Fixed Fl_Preferences Cygwin wide character bug (STR #2164)
  - Fixed sorting in Fl_Browser - last item would not
    be sorted (STR #2300)
  - Fixed window levels in OS X Cocoa (STR #2316)
  - Fixed a buffer overflow in fl_utf8from_mb() (STR #2279)
  - Fixed a Windows GDI leak when testing alpha blending capabilities
  - Fixed crashes for recursive common dialogs (STR #1986, #2150)
  - Fixed a name conflict with new (VS 2008 Express) winsock2.h
    versions and another conflict that produced compile errors
    with VS 2008 Express (STR #2301)
  - Fixed all color related calls to Fl_Color type (STR #2208)
  - Fixed Fl_Choice contrast with light-on-dark settings (STR #2219)
  - Fixed X server "lock", if a modal dialog window is opened
    while a menu is active (STR #1986)
  - Fixed Windows compile bug with "#define USE_COLORMAP 0"
    (STR #2241)
  - Fixed glibc 2.10 compiler problems (Fedora 11 and others)
    with scandir() and strchr() (STR #2222)
  - Fixed OpenGL shared context handling (STR #2135)
  - Fixed gray-scale images with alpha channel (STR #2105)
  - Fixed unexpected shortcut behavior for Win32 (STR #2199)
  - Fixed fl_draw_image to obey the alpha channel (OS X only)
  - Fix for multiple popups, when dragging and calling fl_alert()
    and friends from the callback (STR #2159)
  - Fixed control key keycodes with modifiers on OS X
  - Fixed bad system menu handling in OS X (STR #2153)
  - Fixed File Input mouse pointer dragging (STR #2181)
  - Fixed 'del' keycode on OS X
  - Fixed OS X support for sending and receiving dnd data as UTF-8
  - Fixed Copy/Paste operations with UTF-8, UTF-16 support and
    fltk1.1 compatibility. (STR #2104, 2121).
  - Fixed fl_set_spot() for Windows (STR #2101)
  - Fixed callback that would not be called when shortcut was used
    with radio and toggle buttons in default FL_RELEASE mode.
  - Fixed a problem with TrackMouseEvent() (Windows only) that would
    generate wrong FL_LEAVE events with subwindows. TrackMouseEvent
    is now enabled by default (it was disabled for GNU compilers).
    It can be disabled by defining NO_TRACK_MOUSE.
    Improved test/subwindow.cxx (STR #2079)
  - Fixed menu position close to screen border (STR #2057)
  - Fixed adding an idle handler during a draw() call (STR #1950)
  - Fixed first modifier key event (STR #1952)
  - Fixed Fl_Scroll inside Fl_Scroll (STR #265)

  Removed Features

  - Removed Watcom compiler support because it was introduced in
    1.1.6 as a partial solution and never completed.
  - Removed an XForms compatibility "feature" that prevented the
    down array of Fl_Menu_Button from drawing (STR #2141).
  - Removed support for gcc 2.x (or older)
  - Removed redundant Fl_Group casts


CHANGES IN FLTK 1.1

  See CHANGES_1.1.txt


CHANGES IN FLTK 1.0

  See CHANGES_1.0.txt
